## Bloc
- 1 usecase có nhiều events.
- 1 event buộc phải thuộc 1 hoặc nhiều usecase.
- GetIt.init sẽ add các usecases có thể có trong app
- E.g:
    ```dart
        GetIt.I.get<AppUseCaseManagement>().ignoreEvent.addAll([TestEvent]);

        GetIt.instance<AppUseCaseManagement>().ignoreEvent.addAll([TestEvent]);
        
        GetIt.I<AppUseCaseManagement>().ignoreEvent.addAll([TestEvent]);
    ```