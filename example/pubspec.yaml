name: example
description: Gapo Flutter core example
publish_to: "none"
version: 2.0.0

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.29.1"

dependency_overrides:
  intl: 0.20.2
  http: 1.3.0
  dart_style: 3.0.1
  flutter_sticky_header: 0.7.0
  sentry: 8.14.0
  sentry_flutter: 8.14.0
  sentry_logging: 8.14.0
  sentry_dio: 8.14.0
  source_gen:
    git:
      url: **********************:flutter/utils/gp_source_gen.git
      path: "source_gen"
      ref: "feat/3.29.1"

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2

  gp_core_v2:
    path: ../gp_core_v2

  gp_core:
    git:
      url: **********************:flutter/core/gp_core.git
      ref: "develop"

  auto_mappr_annotation: 2.3.0
  json_annotation: 4.9.0
  dio: 5.8.0+1
  intl: 0.20.2
  go_router: 15.2.0

  get_it: 8.0.3
  talker: 4.7.1
  talker_flutter: 4.7.1
  talker_bloc_logger: 4.7.1
  flutter_bloc: 9.1.0
  injectable: 2.5.0
  logger: 2.5.0
  retrofit: 4.4.2
  freezed_annotation: 3.0.0
  flutter_json_viewer: 1.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: 5.0.0
  build_runner: 2.4.15
  freezed: 3.0.4
  json_serializable: 6.9.4
  injectable_generator: 2.7.0
  auto_mappr: 2.8.0
  mock_web_server: ^5.0.0-nullsafety.1
  retrofit_generator: 9.1.9
  go_router_builder: 3.0.0

  # Testing dependencies
  mockito: ^5.4.4
  bloc_test: ^10.0.0

flutter:
  uses-material-design: true
