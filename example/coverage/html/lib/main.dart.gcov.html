<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/main.dart</title>
  <link rel="stylesheet" type="text/css" href="../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib">lib</a> - main.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">29.5&nbsp;%</td>
            <td class="headerCovTableEntry">61</td>
            <td class="headerCovTableEntry">18</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : /*</span>
<span id="L2"><span class="lineNum">       2</span>              :  * Created Date: 2/12/2023 10:56:3</span>
<span id="L3"><span class="lineNum">       3</span>              :  * Author: Nguyen Manh Toan</span>
<span id="L4"><span class="lineNum">       4</span>              :  * -----</span>
<span id="L5"><span class="lineNum">       5</span>              :  * Last Modified: Monday, 8th January 2024 14:29:36</span>
<span id="L6"><span class="lineNum">       6</span>              :  * Modified By: Nguyen Manh Toan</span>
<span id="L7"><span class="lineNum">       7</span>              :  * -----</span>
<span id="L8"><span class="lineNum">       8</span>              :  * Copyright (c) 2021 - 2024 GAPO</span>
<span id="L9"><span class="lineNum">       9</span>              :  */</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import 'dart:async';</span>
<span id="L12"><span class="lineNum">      12</span>              : import 'dart:developer';</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              : import 'package:example/di/component/app.component.config.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import 'package:flutter/foundation.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import 'package:flutter/material.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import 'package:flutter_bloc/flutter_bloc.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import 'package:get_it/get_it.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import 'package:go_router/go_router.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import 'package:gp_core/core.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import 'package:gp_core_v2/base/bloc/common/common_bloc.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import 'package:gp_core_v2/base/constants/di.constants.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import 'package:gp_core_v2/base/navigator/observer/gp_navigator_observer.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import 'package:talker_bloc_logger/talker_bloc_logger.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import 'package:talker_flutter/talker_flutter.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              : import 'di/component/app.component.dart' as app;</span>
<span id="L29"><span class="lineNum">      29</span>              : import 'domain/entity/entity.dart';</span>
<span id="L30"><span class="lineNum">      30</span>              : import 'navigator/go_router/go_router.route.dart';</span>
<span id="L31"><span class="lineNum">      31</span>              : import 'navigator/model/example_app_info_route.dart';</span>
<span id="L32"><span class="lineNum">      32</span>              : import 'presentation/presentation.dart';</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              : const _appNavigator = AppNavigator.getx;</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 : final _appRouter = GoRouter(</span></span>
<span id="L37"><span class="lineNum">      37</span>              :   debugLogDiagnostics: kDebugMode,</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :   routes: $appRoutes,</span></span>
<span id="L39"><span class="lineNum">      39</span>              :   initialLocation: kExampleInitial,</span>
<span id="L40"><span class="lineNum">      40</span>              :   navigatorKey:</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :       GetIt.I&lt;GlobalKey&lt;NavigatorState&gt;&gt;(instanceName: 'kAppNavigatorKey'),</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   observers: [</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :     GPNavigatorObserver(),</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     if (GetIt.I.isRegistered&lt;Talker&gt;())</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       TalkerRouteObserver(GetIt.I.get&lt;Talker&gt;()),</span></span>
<span id="L46"><span class="lineNum">      46</span>              :   ],</span>
<span id="L47"><span class="lineNum">      47</span>              : );</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 : void _initNavigatorScope() {</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   switch (_appNavigator) {</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     case AppNavigator.flutter:</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       GetIt.I.initNavigateWithFlutterScope();</span></span>
<span id="L53"><span class="lineNum">      53</span>              :       break;</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :     case AppNavigator.goRouter:</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :       GetIt.I.initNavigateWithGoRouterScope();</span></span>
<span id="L56"><span class="lineNum">      56</span>              :       break;</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :     case AppNavigator.getx:</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :       GetIt.I.initNavigateWithGetXScope();</span></span>
<span id="L59"><span class="lineNum">      59</span>              :       break;</span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : }</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 : void main(List&lt;String&gt; args) async {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   runZonedGuarded(() async {</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     WidgetsFlutterBinding.ensureInitialized();</span></span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :     await app.configureInjection();</span></span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :     if (GetIt.I.isRegistered&lt;Talker&gt;()) {</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       Bloc.observer = TalkerBlocObserver(talker: GetIt.instance&lt;Talker&gt;());</span></span>
<span id="L71"><span class="lineNum">      71</span>              :     } else {</span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       log(&quot;Talker is not registered&quot;);</span></span>
<span id="L73"><span class="lineNum">      73</span>              :     }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :     _initNavigatorScope();</span></span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     runApp(const MyWidget());</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :   }, (Object error, StackTrace stackTrace) {</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :     log(&quot;App error: $error, stackTrace: $stackTrace&quot;);</span></span>
<span id="L80"><span class="lineNum">      80</span>              :   });</span>
<span id="L81"><span class="lineNum">      81</span>              : }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span>              : class MyWidget extends StatelessWidget {</span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaGNC">          10 :   const MyWidget({super.key});</span></span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L87"><span class="lineNum">      87</span>              :   Widget build(BuildContext context) {</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           1 :     return BlocProvider&lt;CommonBloc&gt;(</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           3 :       create: (context) =&gt; GetIt.I&lt;CommonBloc&gt;(instanceName: 'kCommonBloc'),</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           1 :       child: RepositoryProvider.value(</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaGNC">           2 :         value: GetIt.I&lt;GPAppNavigator&lt;ExampleAppInfoRoute&gt;&gt;(</span></span>
<span id="L92"><span class="lineNum">      92</span>              :             instanceName: 'kAppNavigator'),</span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaGNC">           1 :         child: _AppWidget(),</span></span>
<span id="L94"><span class="lineNum">      94</span>              :       ),</span>
<span id="L95"><span class="lineNum">      95</span>              :     );</span>
<span id="L96"><span class="lineNum">      96</span>              :   }</span>
<span id="L97"><span class="lineNum">      97</span>              : }</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span>              : class _AppWidget extends StatelessWidget {</span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           1 :   const _AppWidget();</span></span>
<span id="L101"><span class="lineNum">     101</span>              : </span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           1 :   Route&lt;dynamic&gt; _onGenerateRoute(RouteSettings settings) {</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaGNC">           1 :     switch (settings.name) {</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           1 :       case kExampleInitial:</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaGNC">           1 :         return MaterialPageRoute(</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaGNC">           1 :           builder: (context) =&gt; const TestPage(),</span></span>
<span id="L107"><span class="lineNum">     107</span>              :         );</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :       case kExampleHome:</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         return MaterialPageRoute(</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :           builder: (context) =&gt; const ExampleHomePage(),</span></span>
<span id="L111"><span class="lineNum">     111</span>              :         );</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       case kExampleLogin:</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :         return MaterialPageRoute(</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :           builder: (context) =&gt; const ExampleLoginPage(),</span></span>
<span id="L115"><span class="lineNum">     115</span>              :         );</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       case kExampleUserDetails:</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :         return MaterialPageRoute(</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :           builder: (context) =&gt; ExampleUserPage(</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :             user: settings.arguments as User,</span></span>
<span id="L120"><span class="lineNum">     120</span>              :           ),</span>
<span id="L121"><span class="lineNum">     121</span>              :         );</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       case kExampleAssigneeDetails:</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :         return MaterialPageRoute(</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :           builder: (context) =&gt; ExampleAssigneePage(</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :             assigneeEntity: settings.arguments as AssigneeEntity,</span></span>
<span id="L126"><span class="lineNum">     126</span>              :           ),</span>
<span id="L127"><span class="lineNum">     127</span>              :         );</span>
<span id="L128"><span class="lineNum">     128</span>              : </span>
<span id="L129"><span class="lineNum">     129</span>              :       default:</span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :         return MaterialPageRoute(</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :           builder: (context) =&gt; Text('Not found page'),</span></span>
<span id="L132"><span class="lineNum">     132</span>              :         );</span>
<span id="L133"><span class="lineNum">     133</span>              :     }</span>
<span id="L134"><span class="lineNum">     134</span>              :   }</span>
<span id="L135"><span class="lineNum">     135</span>              : </span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L137"><span class="lineNum">     137</span>              :   Widget build(BuildContext context) {</span>
<span id="L138"><span class="lineNum">     138</span>              :     return switch (_appNavigator) {</span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           1 :       AppNavigator.flutter =&gt; MaterialApp(</span></span>
<span id="L140"><span class="lineNum">     140</span>              :           showPerformanceOverlay: false,</span>
<span id="L141"><span class="lineNum">     141</span>              :           debugShowMaterialGrid: false,</span>
<span id="L142"><span class="lineNum">     142</span>              :           debugShowCheckedModeBanner: false,</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :           onGenerateRoute: _onGenerateRoute,</span></span>
<span id="L144"><span class="lineNum">     144</span>              :           initialRoute: kExampleInitial,</span>
<span id="L145"><span class="lineNum">     145</span>              :         ),</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaGNC">           1 :       AppNavigator.goRouter =&gt; MaterialApp.router(</span></span>
<span id="L147"><span class="lineNum">     147</span>              :           showPerformanceOverlay: false,</span>
<span id="L148"><span class="lineNum">     148</span>              :           debugShowMaterialGrid: false,</span>
<span id="L149"><span class="lineNum">     149</span>              :           debugShowCheckedModeBanner: false,</span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :           routerConfig: _appRouter,</span></span>
<span id="L151"><span class="lineNum">     151</span>              :         ),</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaGNC">           2 :       AppNavigator.getx =&gt; GetMaterialApp(</span></span>
<span id="L153"><span class="lineNum">     153</span>              :           showPerformanceOverlay: false,</span>
<span id="L154"><span class="lineNum">     154</span>              :           debugShowMaterialGrid: false,</span>
<span id="L155"><span class="lineNum">     155</span>              :           debugShowCheckedModeBanner: false,</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           1 :           onGenerateRoute: _onGenerateRoute,</span></span>
<span id="L157"><span class="lineNum">     157</span>              :           initialRoute: kExampleInitial,</span>
<span id="L158"><span class="lineNum">     158</span>              :         ),</span>
<span id="L159"><span class="lineNum">     159</span>              :     };</span>
<span id="L160"><span class="lineNum">     160</span>              :   }</span>
<span id="L161"><span class="lineNum">     161</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
