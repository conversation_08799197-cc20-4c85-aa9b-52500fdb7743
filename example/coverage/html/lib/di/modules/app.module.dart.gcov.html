<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/di/modules/app.module.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/di/modules">lib/di/modules</a> - app.module.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">100.0&nbsp;%</td>
            <td class="headerCovTableEntry">14</td>
            <td class="headerCovTableEntry">14</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : /*</span>
<span id="L2"><span class="lineNum">       2</span>              :  * Created Date: 5/12/2023 09:47:29</span>
<span id="L3"><span class="lineNum">       3</span>              :  * Author: Nguyen Manh Toan</span>
<span id="L4"><span class="lineNum">       4</span>              :  * -----</span>
<span id="L5"><span class="lineNum">       5</span>              :  * Last Modified: Monday, 8th January 2024 16:38:44</span>
<span id="L6"><span class="lineNum">       6</span>              :  * Modified By: Nguyen Manh Toan</span>
<span id="L7"><span class="lineNum">       7</span>              :  * -----</span>
<span id="L8"><span class="lineNum">       8</span>              :  * Copyright (c) 2021 - 2024 GAPO</span>
<span id="L9"><span class="lineNum">       9</span>              :  */</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import 'package:get_it/get_it.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import 'package:gp_core_v2/base/configs/app/app.configs.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import 'package:gp_core_v2/base/configs/bloc/talker_observer.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import 'package:gp_core_v2/base/configs/log/log.configs.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import 'package:gp_core_v2/base/constants/di.constants.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import 'package:injectable/injectable.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import 'package:talker/talker.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../presentation/test/bloc/test_event.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              : @module</span>
<span id="L22"><span class="lineNum">      22</span>              : abstract class AppModule {</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           1 :   @preResolve</span></span>
<span id="L24"><span class="lineNum">      24</span>              :   Future&lt;bool&gt; get init async {</span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           1 :     _initIgnoreUsecases();</span></span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           1 :     return Future.value(true);</span></span>
<span id="L28"><span class="lineNum">      28</span>              :   }</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :   @Singleton(env: kFlavorDevs)</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           1 :   Talker get talker =&gt; Talker(</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           1 :         settings: TalkerSettings(</span></span>
<span id="L33"><span class="lineNum">      33</span>              :           enabled: LogConfig.kEnableTalkerLog,</span>
<span id="L34"><span class="lineNum">      34</span>              :           maxHistoryItems: 100,</span>
<span id="L35"><span class="lineNum">      35</span>              :           useHistory: true,</span>
<span id="L36"><span class="lineNum">      36</span>              :           useConsoleLogs: true,</span>
<span id="L37"><span class="lineNum">      37</span>              :         ),</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaGNC">           1 :         logger: TalkerLogger(),</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           1 :         observer: GPTalkerObserver(),</span></span>
<span id="L40"><span class="lineNum">      40</span>              :       );</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaGNC">           1 :   void _initIgnoreUsecases() {</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :     final appUseCaseManagement = GetIt.I.get&lt;AppUseCaseManagement&gt;();</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           1 :     final eventsToAdd = [TestEvent];</span></span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span>              :     // Only add events that are not already in the list to prevent duplicates</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           2 :     for (final event in eventsToAdd) {</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           2 :       if (!appUseCaseManagement.ignoreEvents.contains(event)) {</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaGNC">           2 :         appUseCaseManagement.ignoreEvents.add(event);</span></span>
<span id="L50"><span class="lineNum">      50</span>              :       }</span>
<span id="L51"><span class="lineNum">      51</span>              :     }</span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
