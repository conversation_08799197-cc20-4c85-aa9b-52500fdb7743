<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/navigator/app_navigator/app_navigator_getx_route_impl.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/navigator/app_navigator">lib/navigator/app_navigator</a> - app_navigator_getx_route_impl.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">35</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart' as material;</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:gp_core/core.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:gp_core_v2/base/configs/log/log.configs.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:gp_core_v2/base/constants/di.constants.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:gp_core_v2/base/navigator/navigator.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:injectable/injectable.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../mapper/example_getx_route_info_mapper.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../model/example_app_info_route.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : @LazySingleton(</span>
<span id="L12"><span class="lineNum">      12</span>              :   as: GPAppNavigator,</span>
<span id="L13"><span class="lineNum">      13</span>              :   order: DiConstants.kDomainPresentationOrder,</span>
<span id="L14"><span class="lineNum">      14</span>              :   scope: 'NavigateWithGetX',</span>
<span id="L15"><span class="lineNum">      15</span>              : )</span>
<span id="L16"><span class="lineNum">      16</span>              : @Named('kAppNavigator')</span>
<span id="L17"><span class="lineNum">      17</span>              : // Apply default Flutter navigation</span>
<span id="L18"><span class="lineNum">      18</span>              : final class ExampleGetXRouteNavigatorImpl</span>
<span id="L19"><span class="lineNum">      19</span>              :     extends GPAppNavigator&lt;ExampleAppInfoRoute&gt; {</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaUNC">           0 :   ExampleGetXRouteNavigatorImpl(</span></span>
<span id="L21"><span class="lineNum">      21</span>              :     @Named('kAppNavigatorKey') super.navigatorKey,</span>
<span id="L22"><span class="lineNum">      22</span>              :   );</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :   GPAppPopupNavigator get appPopupNavigator =&gt; GPAppPopupNavigatorImpl(this);</span></span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L28"><span class="lineNum">      28</span>              :   GPAppSnackbarNavigator get appSnackbarNavigator =&gt;</span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :       GPAppGetXSnackbarNavigatorImpl(this);</span></span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L32"><span class="lineNum">      32</span>              :   GPBaseRouteInfoMapper&lt;material.RouteSettings, ExampleAppInfoRoute&gt;</span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :       get routeInfoMapper =&gt; AppGetXRouteInfoMapper();</span></span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L36"><span class="lineNum">      36</span>              :   Object? parseArguments&lt;T extends Object?&gt;(</span>
<span id="L37"><span class="lineNum">      37</span>              :     ExampleAppInfoRoute appRouteInfo,</span>
<span id="L38"><span class="lineNum">      38</span>              :     Object? arguments,</span>
<span id="L39"><span class="lineNum">      39</span>              :   ) {</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :     dynamic routeData = routeInfoMapper.map(appRouteInfo);</span></span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span>              :     try {</span>
<span id="L43"><span class="lineNum">      43</span>              :       // null safety cast</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :       arguments ??= arguments ?? routeData.arguments;</span></span>
<span id="L45"><span class="lineNum">      45</span>              :     } catch (ex, stackTrace) {</span>
<span id="L46"><span class="lineNum">      46</span>              :       if (LogConfig.kLogOnNavigatorPushError) {</span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         logDebug('$runtimeType push with arguments error $ex, \n $stackTrace');</span></span>
<span id="L48"><span class="lineNum">      48</span>              :       }</span>
<span id="L49"><span class="lineNum">      49</span>              :     }</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span>              :     return arguments;</span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   bool canPop(material.BuildContext context) {</span>
<span id="L56"><span class="lineNum">      56</span>              :     // GetX check canPop when `Get.back()`</span>
<span id="L57"><span class="lineNum">      57</span>              :     return true;</span>
<span id="L58"><span class="lineNum">      58</span>              :   }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   void pop&lt;T extends Object?&gt;(</span>
<span id="L62"><span class="lineNum">      62</span>              :     material.BuildContext context, {</span>
<span id="L63"><span class="lineNum">      63</span>              :     T? result,</span>
<span id="L64"><span class="lineNum">      64</span>              :     bool useRootNavigator = false,</span>
<span id="L65"><span class="lineNum">      65</span>              :   }) {</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :     Get.back(result: result, canPop: true);</span></span>
<span id="L67"><span class="lineNum">      67</span>              :   }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L70"><span class="lineNum">      70</span>              :   void popUntil&lt;T extends Object?&gt;(</span>
<span id="L71"><span class="lineNum">      71</span>              :     material.BuildContext context,</span>
<span id="L72"><span class="lineNum">      72</span>              :     ExampleAppInfoRoute appRouteInfo,</span>
<span id="L73"><span class="lineNum">      73</span>              :   ) {</span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :     Get.until((route) {</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :       return route.settings.name == routeInfoMapper.map(appRouteInfo).name;</span></span>
<span id="L76"><span class="lineNum">      76</span>              :     });</span>
<span id="L77"><span class="lineNum">      77</span>              :   }</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L80"><span class="lineNum">      80</span>              :   Future&lt;T?&gt; popAndPush&lt;T extends Object?, R extends Object?&gt;(</span>
<span id="L81"><span class="lineNum">      81</span>              :     material.BuildContext context,</span>
<span id="L82"><span class="lineNum">      82</span>              :     ExampleAppInfoRoute appRouteInfo, {</span>
<span id="L83"><span class="lineNum">      83</span>              :     R? result,</span>
<span id="L84"><span class="lineNum">      84</span>              :     bool useRootNavigator = false,</span>
<span id="L85"><span class="lineNum">      85</span>              :     Object? arguments,</span>
<span id="L86"><span class="lineNum">      86</span>              :   }) async {</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :     final routeSettings = routeInfoMapper.map(appRouteInfo);</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :     final location = routeSettings.name ?? '';</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :     assert(location.isNotEmpty == true);</span></span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span>              :     // null safety cast</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :     arguments ??= parseArguments(appRouteInfo, arguments);</span></span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :     return Get.offAndToNamed(</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       location,</span>
<span id="L96"><span class="lineNum">      96</span>              :       arguments: arguments,</span>
<span id="L97"><span class="lineNum">      97</span>              :     );</span>
<span id="L98"><span class="lineNum">      98</span>              :   }</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L101"><span class="lineNum">     101</span>              :   Future&lt;T?&gt; push&lt;T extends Object?, R extends Object?&gt;(</span>
<span id="L102"><span class="lineNum">     102</span>              :     material.BuildContext context,</span>
<span id="L103"><span class="lineNum">     103</span>              :     ExampleAppInfoRoute appRouteInfo, {</span>
<span id="L104"><span class="lineNum">     104</span>              :     R? result,</span>
<span id="L105"><span class="lineNum">     105</span>              :     bool useRootNavigator = false,</span>
<span id="L106"><span class="lineNum">     106</span>              :     Object? arguments,</span>
<span id="L107"><span class="lineNum">     107</span>              :   }) async {</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :     final routeSettings = routeInfoMapper.map(appRouteInfo);</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :     final location = routeSettings.name ?? '';</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     assert(location.isNotEmpty == true);</span></span>
<span id="L111"><span class="lineNum">     111</span>              : </span>
<span id="L112"><span class="lineNum">     112</span>              :     // null safety cast</span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :     arguments ??= parseArguments(appRouteInfo, arguments);</span></span>
<span id="L114"><span class="lineNum">     114</span>              : </span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :     return Get.toNamed(</span></span>
<span id="L116"><span class="lineNum">     116</span>              :       location,</span>
<span id="L117"><span class="lineNum">     117</span>              :       arguments: arguments,</span>
<span id="L118"><span class="lineNum">     118</span>              :     );</span>
<span id="L119"><span class="lineNum">     119</span>              :   }</span>
<span id="L120"><span class="lineNum">     120</span>              : </span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L122"><span class="lineNum">     122</span>              :   Future&lt;T?&gt; replace&lt;T extends Object?&gt;(</span>
<span id="L123"><span class="lineNum">     123</span>              :     material.BuildContext context,</span>
<span id="L124"><span class="lineNum">     124</span>              :     ExampleAppInfoRoute appRouteInfo, {</span>
<span id="L125"><span class="lineNum">     125</span>              :     Object? arguments,</span>
<span id="L126"><span class="lineNum">     126</span>              :   }) async {</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :     final routeSettings = routeInfoMapper.map(appRouteInfo);</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :     final location = routeSettings.name ?? '';</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :     assert(location.isNotEmpty == true);</span></span>
<span id="L130"><span class="lineNum">     130</span>              : </span>
<span id="L131"><span class="lineNum">     131</span>              :     // null safety cast</span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :     arguments ??= parseArguments(appRouteInfo, arguments);</span></span>
<span id="L133"><span class="lineNum">     133</span>              : </span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :     Get.offNamed(location, arguments: arguments);</span></span>
<span id="L135"><span class="lineNum">     135</span>              : </span>
<span id="L136"><span class="lineNum">     136</span>              :     return null;</span>
<span id="L137"><span class="lineNum">     137</span>              :   }</span>
<span id="L138"><span class="lineNum">     138</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
