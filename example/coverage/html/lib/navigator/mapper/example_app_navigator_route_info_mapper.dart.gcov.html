<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/navigator/mapper/example_app_navigator_route_info_mapper.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/navigator/mapper">lib/navigator/mapper</a> - example_app_navigator_route_info_mapper.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryMed">88.9&nbsp;%</td>
            <td class="headerCovTableEntry">9</td>
            <td class="headerCovTableEntry">8</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : // ignore_for_file: library_private_types_in_public_api</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter/widgets.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:gp_core_v2/base/navigator/mapper/base_route_info_mapper.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../model/example_app_info_route.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : typedef _T = RouteSettings;</span>
<span id="L9"><span class="lineNum">       9</span>              : typedef _R = ExampleAppInfoRoute;</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : // Apply default Flutter navigation</span>
<span id="L12"><span class="lineNum">      12</span>              : final class AppNavigatorRouteInfoMapper extends GPBaseRouteInfoMapper&lt;_T, _R&gt; {</span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L14"><span class="lineNum">      14</span>              :   _T map(_R infoRoute) {</span>
<span id="L15"><span class="lineNum">      15</span>              :     return switch (infoRoute) {</span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           3 :       ExampleInitialRoute() =&gt; RouteSettings(name: infoRoute.route),</span></span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           3 :       ExampleLoginRoute() =&gt; RouteSettings(name: infoRoute.route),</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           3 :       ExampleHomeRoute() =&gt; RouteSettings(name: infoRoute.route),</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           3 :       ExampleUserRoute(user: final user) =&gt; RouteSettings(</span></span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           1 :           name: infoRoute.route,</span></span>
<span id="L21"><span class="lineNum">      21</span>              :           arguments: user,</span>
<span id="L22"><span class="lineNum">      22</span>              :         ),</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           3 :       ExampleAssigneeRoute(entity: final assigneeEntity) =&gt; RouteSettings(</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           1 :           name: infoRoute.route,</span></span>
<span id="L25"><span class="lineNum">      25</span>              :           arguments: assigneeEntity,</span>
<span id="L26"><span class="lineNum">      26</span>              :         ),</span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :       _R() =&gt; throw UnimplementedError(),</span></span>
<span id="L28"><span class="lineNum">      28</span>              :     };</span>
<span id="L29"><span class="lineNum">      29</span>              :   }</span>
<span id="L30"><span class="lineNum">      30</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
