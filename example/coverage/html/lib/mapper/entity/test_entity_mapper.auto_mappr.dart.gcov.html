<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/mapper/entity/test_entity_mapper.auto_mappr.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/mapper/entity">lib/mapper/entity</a> - test_entity_mapper.auto_mappr.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">24.4&nbsp;%</td>
            <td class="headerCovTableEntry">90</td>
            <td class="headerCovTableEntry">22</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : // dart format width=80</span>
<span id="L2"><span class="lineNum">       2</span>              : // GENERATED CODE - DO NOT MODIFY BY HAND</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : // **************************************************************************</span>
<span id="L5"><span class="lineNum">       5</span>              : // AutoMapprGenerator</span>
<span id="L6"><span class="lineNum">       6</span>              : // **************************************************************************</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : // ignore_for_file: type=lint, unnecessary_cast, unused_local_variable</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : // ignore_for_file: no_leading_underscores_for_library_prefixes</span>
<span id="L11"><span class="lineNum">      11</span>              : import 'package:auto_mappr_annotation/auto_mappr_annotation.dart' as _i1;</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../domain/entity/test.entity.dart' as _i2;</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              : /// {@template package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L16"><span class="lineNum">      16</span>              : /// Available mappings:</span>
<span id="L17"><span class="lineNum">      17</span>              : /// - `UserDto` → `User`.</span>
<span id="L18"><span class="lineNum">      18</span>              : /// {@endtemplate}</span>
<span id="L19"><span class="lineNum">      19</span>              : class $TestMapper implements _i1.AutoMapprInterface {</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">          56 :   const $TestMapper();</span></span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           3 :   Type _typeOf&lt;T&gt;() =&gt; T;</span></span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :   List&lt;_i1.AutoMapprInterface&gt; get _delegates =&gt; const [];</span></span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// {@macro AutoMapprInterface:canConvert}</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L29"><span class="lineNum">      29</span>              :   bool canConvert&lt;SOURCE, TARGET&gt;({bool recursive = true}) {</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           3 :     final sourceTypeOf = _typeOf&lt;SOURCE&gt;();</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaGNC">           3 :     final targetTypeOf = _typeOf&lt;TARGET&gt;();</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           6 :     if ((sourceTypeOf == _typeOf&lt;_i2.UserDto&gt;() ||</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :             sourceTypeOf == _typeOf&lt;_i2.UserDto?&gt;()) &amp;&amp;</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           6 :         (targetTypeOf == _typeOf&lt;_i2.User&gt;() ||</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaGNC">           4 :             targetTypeOf == _typeOf&lt;_i2.User?&gt;())) {</span></span>
<span id="L36"><span class="lineNum">      36</span>              :       return true;</span>
<span id="L37"><span class="lineNum">      37</span>              :     }</span>
<span id="L38"><span class="lineNum">      38</span>              :     if (recursive) {</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :       for (final mappr in _delegates) {</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :         if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L41"><span class="lineNum">      41</span>              :           return true;</span>
<span id="L42"><span class="lineNum">      42</span>              :         }</span>
<span id="L43"><span class="lineNum">      43</span>              :       }</span>
<span id="L44"><span class="lineNum">      44</span>              :     }</span>
<span id="L45"><span class="lineNum">      45</span>              :     return false;</span>
<span id="L46"><span class="lineNum">      46</span>              :   }</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span>              :   /// {@macro AutoMapprInterface:convert}</span>
<span id="L49"><span class="lineNum">      49</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   TARGET convert&lt;SOURCE, TARGET&gt;(SOURCE? model) {</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">           3 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           3 :       return _convert(model)!;</span></span>
<span id="L54"><span class="lineNum">      54</span>              :     }</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         return mappr.convert(model)!;</span></span>
<span id="L58"><span class="lineNum">      58</span>              :       }</span>
<span id="L59"><span class="lineNum">      59</span>              :     }</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span>              :   /// {@macro AutoMapprInterface:tryConvert}</span>
<span id="L65"><span class="lineNum">      65</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L67"><span class="lineNum">      67</span>              :   TARGET? tryConvert&lt;SOURCE, TARGET&gt;(</span>
<span id="L68"><span class="lineNum">      68</span>              :     SOURCE? model, {</span>
<span id="L69"><span class="lineNum">      69</span>              :     void Function(Object error, StackTrace stackTrace, SOURCE? source)?</span>
<span id="L70"><span class="lineNum">      70</span>              :         onMappingError,</span>
<span id="L71"><span class="lineNum">      71</span>              :   }) {</span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :       return _safeConvert(</span></span>
<span id="L74"><span class="lineNum">      74</span>              :         model,</span>
<span id="L75"><span class="lineNum">      75</span>              :         onMappingError: onMappingError,</span>
<span id="L76"><span class="lineNum">      76</span>              :       );</span>
<span id="L77"><span class="lineNum">      77</span>              :     }</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :         return mappr.tryConvert(</span></span>
<span id="L81"><span class="lineNum">      81</span>              :           model,</span>
<span id="L82"><span class="lineNum">      82</span>              :           onMappingError: onMappingError,</span>
<span id="L83"><span class="lineNum">      83</span>              :         );</span>
<span id="L84"><span class="lineNum">      84</span>              :       }</span>
<span id="L85"><span class="lineNum">      85</span>              :     }</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span>              :     return null;</span>
<span id="L88"><span class="lineNum">      88</span>              :   }</span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span>              :   /// {@macro AutoMapprInterface:convertIterable}</span>
<span id="L91"><span class="lineNum">      91</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   Iterable&lt;TARGET&gt; convertIterable&lt;SOURCE, TARGET&gt;(Iterable&lt;SOURCE?&gt; model) {</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :       return model.map&lt;TARGET&gt;((item) =&gt; _convert(item)!);</span></span>
<span id="L96"><span class="lineNum">      96</span>              :     }</span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :         return mappr.convertIterable(model);</span></span>
<span id="L100"><span class="lineNum">     100</span>              :       }</span>
<span id="L101"><span class="lineNum">     101</span>              :     }</span>
<span id="L102"><span class="lineNum">     102</span>              : </span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L104"><span class="lineNum">     104</span>              :   }</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span>              :   /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Iterable.</span>
<span id="L107"><span class="lineNum">     107</span>              :   ///</span>
<span id="L108"><span class="lineNum">     108</span>              :   /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null</span>
<span id="L109"><span class="lineNum">     109</span>              :   ///</span>
<span id="L110"><span class="lineNum">     110</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L112"><span class="lineNum">     112</span>              :   Iterable&lt;TARGET?&gt; tryConvertIterable&lt;SOURCE, TARGET&gt;(</span>
<span id="L113"><span class="lineNum">     113</span>              :     Iterable&lt;SOURCE?&gt; model, {</span>
<span id="L114"><span class="lineNum">     114</span>              :     void Function(Object error, StackTrace stackTrace, SOURCE? source)?</span>
<span id="L115"><span class="lineNum">     115</span>              :         onMappingError,</span>
<span id="L116"><span class="lineNum">     116</span>              :   }) {</span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :       return model.map&lt;TARGET?&gt;(</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :           (item) =&gt; _safeConvert(item, onMappingError: onMappingError));</span></span>
<span id="L120"><span class="lineNum">     120</span>              :     }</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :         return mappr.tryConvertIterable(</span></span>
<span id="L124"><span class="lineNum">     124</span>              :           model,</span>
<span id="L125"><span class="lineNum">     125</span>              :           onMappingError: onMappingError,</span>
<span id="L126"><span class="lineNum">     126</span>              :         );</span>
<span id="L127"><span class="lineNum">     127</span>              :       }</span>
<span id="L128"><span class="lineNum">     128</span>              :     }</span>
<span id="L129"><span class="lineNum">     129</span>              : </span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L131"><span class="lineNum">     131</span>              :   }</span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span>              :   /// {@macro AutoMapprInterface:convertList}</span>
<span id="L134"><span class="lineNum">     134</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L136"><span class="lineNum">     136</span>              :   List&lt;TARGET&gt; convertList&lt;SOURCE, TARGET&gt;(Iterable&lt;SOURCE?&gt; model) {</span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :       return convertIterable&lt;SOURCE, TARGET&gt;(model).toList();</span></span>
<span id="L139"><span class="lineNum">     139</span>              :     }</span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :         return mappr.convertList(model);</span></span>
<span id="L143"><span class="lineNum">     143</span>              :       }</span>
<span id="L144"><span class="lineNum">     144</span>              :     }</span>
<span id="L145"><span class="lineNum">     145</span>              : </span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L147"><span class="lineNum">     147</span>              :   }</span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span>              :   /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into List.</span>
<span id="L150"><span class="lineNum">     150</span>              :   ///</span>
<span id="L151"><span class="lineNum">     151</span>              :   /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null</span>
<span id="L152"><span class="lineNum">     152</span>              :   ///</span>
<span id="L153"><span class="lineNum">     153</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L155"><span class="lineNum">     155</span>              :   List&lt;TARGET?&gt; tryConvertList&lt;SOURCE, TARGET&gt;(</span>
<span id="L156"><span class="lineNum">     156</span>              :     Iterable&lt;SOURCE?&gt; model, {</span>
<span id="L157"><span class="lineNum">     157</span>              :     void Function(Object error, StackTrace stackTrace, SOURCE? source)?</span>
<span id="L158"><span class="lineNum">     158</span>              :         onMappingError,</span>
<span id="L159"><span class="lineNum">     159</span>              :   }) {</span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :       return tryConvertIterable&lt;SOURCE, TARGET&gt;(</span></span>
<span id="L162"><span class="lineNum">     162</span>              :         model,</span>
<span id="L163"><span class="lineNum">     163</span>              :         onMappingError: onMappingError,</span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       ).toList();</span></span>
<span id="L165"><span class="lineNum">     165</span>              :     }</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :         return mappr.tryConvertList(</span></span>
<span id="L169"><span class="lineNum">     169</span>              :           model,</span>
<span id="L170"><span class="lineNum">     170</span>              :           onMappingError: onMappingError,</span>
<span id="L171"><span class="lineNum">     171</span>              :         );</span>
<span id="L172"><span class="lineNum">     172</span>              :       }</span>
<span id="L173"><span class="lineNum">     173</span>              :     }</span>
<span id="L174"><span class="lineNum">     174</span>              : </span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L176"><span class="lineNum">     176</span>              :   }</span>
<span id="L177"><span class="lineNum">     177</span>              : </span>
<span id="L178"><span class="lineNum">     178</span>              :   /// {@macro AutoMapprInterface:convertSet}</span>
<span id="L179"><span class="lineNum">     179</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L181"><span class="lineNum">     181</span>              :   Set&lt;TARGET&gt; convertSet&lt;SOURCE, TARGET&gt;(Iterable&lt;SOURCE?&gt; model) {</span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :       return convertIterable&lt;SOURCE, TARGET&gt;(model).toSet();</span></span>
<span id="L184"><span class="lineNum">     184</span>              :     }</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :         return mappr.convertSet(model);</span></span>
<span id="L188"><span class="lineNum">     188</span>              :       }</span>
<span id="L189"><span class="lineNum">     189</span>              :     }</span>
<span id="L190"><span class="lineNum">     190</span>              : </span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L192"><span class="lineNum">     192</span>              :   }</span>
<span id="L193"><span class="lineNum">     193</span>              : </span>
<span id="L194"><span class="lineNum">     194</span>              :   /// For iterable items, converts from SOURCE to TARGET if such mapping is configured, into Set.</span>
<span id="L195"><span class="lineNum">     195</span>              :   ///</span>
<span id="L196"><span class="lineNum">     196</span>              :   /// When an item in the source iterable is null, uses `whenSourceIsNull` if defined or null</span>
<span id="L197"><span class="lineNum">     197</span>              :   ///</span>
<span id="L198"><span class="lineNum">     198</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L200"><span class="lineNum">     200</span>              :   Set&lt;TARGET?&gt; tryConvertSet&lt;SOURCE, TARGET&gt;(</span>
<span id="L201"><span class="lineNum">     201</span>              :     Iterable&lt;SOURCE?&gt; model, {</span>
<span id="L202"><span class="lineNum">     202</span>              :     void Function(Object error, StackTrace stackTrace, SOURCE? source)?</span>
<span id="L203"><span class="lineNum">     203</span>              :         onMappingError,</span>
<span id="L204"><span class="lineNum">     204</span>              :   }) {</span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaUNC">           0 :     if (canConvert&lt;SOURCE, TARGET&gt;(recursive: false)) {</span></span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :       return tryConvertIterable&lt;SOURCE, TARGET&gt;(</span></span>
<span id="L207"><span class="lineNum">     207</span>              :         model,</span>
<span id="L208"><span class="lineNum">     208</span>              :         onMappingError: onMappingError,</span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :       ).toSet();</span></span>
<span id="L210"><span class="lineNum">     210</span>              :     }</span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :     for (final mappr in _delegates) {</span></span>
<span id="L212"><span class="lineNum">     212</span> <span class="tlaUNC">           0 :       if (mappr.canConvert&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L213"><span class="lineNum">     213</span> <span class="tlaUNC">           0 :         return mappr.tryConvertSet(</span></span>
<span id="L214"><span class="lineNum">     214</span>              :           model,</span>
<span id="L215"><span class="lineNum">     215</span>              :           onMappingError: onMappingError,</span>
<span id="L216"><span class="lineNum">     216</span>              :         );</span>
<span id="L217"><span class="lineNum">     217</span>              :       }</span>
<span id="L218"><span class="lineNum">     218</span>              :     }</span>
<span id="L219"><span class="lineNum">     219</span>              : </span>
<span id="L220"><span class="lineNum">     220</span> <span class="tlaUNC">           0 :     throw Exception('No ${_typeOf&lt;SOURCE&gt;()} -&gt; ${_typeOf&lt;TARGET&gt;()} mapping.');</span></span>
<span id="L221"><span class="lineNum">     221</span>              :   }</span>
<span id="L222"><span class="lineNum">     222</span>              : </span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaGNC">           3 :   TARGET? _convert&lt;SOURCE, TARGET&gt;(</span></span>
<span id="L224"><span class="lineNum">     224</span>              :     SOURCE? model, {</span>
<span id="L225"><span class="lineNum">     225</span>              :     bool canReturnNull = false,</span>
<span id="L226"><span class="lineNum">     226</span>              :   }) {</span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaGNC">           3 :     final sourceTypeOf = _typeOf&lt;SOURCE&gt;();</span></span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaGNC">           3 :     final targetTypeOf = _typeOf&lt;TARGET&gt;();</span></span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaGNC">           6 :     if ((sourceTypeOf == _typeOf&lt;_i2.UserDto&gt;() ||</span></span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaUNC">           0 :             sourceTypeOf == _typeOf&lt;_i2.UserDto?&gt;()) &amp;&amp;</span></span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaGNC">           6 :         (targetTypeOf == _typeOf&lt;_i2.User&gt;() ||</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaGNC">           4 :             targetTypeOf == _typeOf&lt;_i2.User?&gt;())) {</span></span>
<span id="L233"><span class="lineNum">     233</span>              :       if (canReturnNull &amp;&amp; model == null) {</span>
<span id="L234"><span class="lineNum">     234</span>              :         return null;</span>
<span id="L235"><span class="lineNum">     235</span>              :       }</span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaGNC">           3 :       return (_map__i2$UserDto_To__i2$User((model as _i2.UserDto?)) as TARGET);</span></span>
<span id="L237"><span class="lineNum">     237</span>              :     }</span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :     throw Exception('No ${model.runtimeType} -&gt; $targetTypeOf mapping.');</span></span>
<span id="L239"><span class="lineNum">     239</span>              :   }</span>
<span id="L240"><span class="lineNum">     240</span>              : </span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :   TARGET? _safeConvert&lt;SOURCE, TARGET&gt;(</span></span>
<span id="L242"><span class="lineNum">     242</span>              :     SOURCE? model, {</span>
<span id="L243"><span class="lineNum">     243</span>              :     void Function(Object error, StackTrace stackTrace, SOURCE? source)?</span>
<span id="L244"><span class="lineNum">     244</span>              :         onMappingError,</span>
<span id="L245"><span class="lineNum">     245</span>              :   }) {</span>
<span id="L246"><span class="lineNum">     246</span> <span class="tlaUNC">           0 :     if (!useSafeMapping&lt;SOURCE, TARGET&gt;()) {</span></span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :       return _convert(</span></span>
<span id="L248"><span class="lineNum">     248</span>              :         model,</span>
<span id="L249"><span class="lineNum">     249</span>              :         canReturnNull: true,</span>
<span id="L250"><span class="lineNum">     250</span>              :       );</span>
<span id="L251"><span class="lineNum">     251</span>              :     }</span>
<span id="L252"><span class="lineNum">     252</span>              :     try {</span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :       return _convert(</span></span>
<span id="L254"><span class="lineNum">     254</span>              :         model,</span>
<span id="L255"><span class="lineNum">     255</span>              :         canReturnNull: true,</span>
<span id="L256"><span class="lineNum">     256</span>              :       );</span>
<span id="L257"><span class="lineNum">     257</span>              :     } catch (e, s) {</span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaUNC">           0 :       onMappingError?.call(e, s, model);</span></span>
<span id="L259"><span class="lineNum">     259</span>              :       return null;</span>
<span id="L260"><span class="lineNum">     260</span>              :     }</span>
<span id="L261"><span class="lineNum">     261</span>              :   }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span>              :   /// {@macro AutoMapprInterface:useSafeMapping}</span>
<span id="L264"><span class="lineNum">     264</span>              :   /// {@macro package:example/mapper/entity/test_entity_mapper.dart}</span>
<span id="L265"><span class="lineNum">     265</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L266"><span class="lineNum">     266</span>              :   bool useSafeMapping&lt;SOURCE, TARGET&gt;() {</span>
<span id="L267"><span class="lineNum">     267</span>              :     return false;</span>
<span id="L268"><span class="lineNum">     268</span>              :   }</span>
<span id="L269"><span class="lineNum">     269</span>              : </span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaGNC">           3 :   _i2.User _map__i2$UserDto_To__i2$User(_i2.UserDto? input) {</span></span>
<span id="L271"><span class="lineNum">     271</span>              :     final model = input;</span>
<span id="L272"><span class="lineNum">     272</span>              :     if (model == null) {</span>
<span id="L273"><span class="lineNum">     273</span> <span class="tlaUNC">           0 :       throw Exception(</span></span>
<span id="L274"><span class="lineNum">     274</span>              :           r'Mapping UserDto → User failed because UserDto was null, and no default value was provided. '</span>
<span id="L275"><span class="lineNum">     275</span>              :           r'Consider setting the whenSourceIsNull parameter on the MapType&lt;UserDto, User&gt; to handle null values during mapping.');</span>
<span id="L276"><span class="lineNum">     276</span>              :     }</span>
<span id="L277"><span class="lineNum">     277</span> <span class="tlaGNC">           3 :     return _i2.User(</span></span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaGNC">           3 :       id: model.id,</span></span>
<span id="L279"><span class="lineNum">     279</span> <span class="tlaGNC">           3 :       name: model.name,</span></span>
<span id="L280"><span class="lineNum">     280</span>              :     );</span>
<span id="L281"><span class="lineNum">     281</span>              :   }</span>
<span id="L282"><span class="lineNum">     282</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
