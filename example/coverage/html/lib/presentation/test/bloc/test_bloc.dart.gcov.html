<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/presentation/test/bloc/test_bloc.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/presentation/test/bloc">lib/presentation/test/bloc</a> - test_bloc.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryMed">78.7&nbsp;%</td>
            <td class="headerCovTableEntry">75</td>
            <td class="headerCovTableEntry">59</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_bloc/flutter_bloc.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:get_it/get_it.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:gp_core_v2/base/base.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:injectable/injectable.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../../data/model/auth/response/auth/auth_check_mail_response.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../domain/entity/auth.entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../domain/usecase/auth_check_mail.usecase.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../mapper/entity/mapper.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import 'bloc.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              : @injectable</span>
<span id="L13"><span class="lineNum">      13</span>              : class TestBloc extends CoreV2BaseBloc&lt;CoreV2BaseEvent, TestState&gt; {</span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaGNC">           4 :   TestBloc({</span></span>
<span id="L15"><span class="lineNum">      15</span>              :     required this.authCheckMailUseCase,</span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           4 :   }) : super(const TestState(&quot;test&quot;)) {</span></span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaGNC">           4 :     on&lt;TestEvent&gt;(</span></span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           4 :       _onTestEvent,</span></span>
<span id="L19"><span class="lineNum">      19</span>              :     );</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaGNC">           4 :     on&lt;TestCounterEvent&gt;(</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           4 :       _onTestCounterEvent,</span></span>
<span id="L23"><span class="lineNum">      23</span>              :       // ignore the same events</span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           4 :       transformer: distinct(),</span></span>
<span id="L25"><span class="lineNum">      25</span>              :     );</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           4 :     on&lt;AuthEmailCheck&gt;(</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           4 :       _onAuthEmailCheck,</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           4 :       transformer: distinct(),</span></span>
<span id="L30"><span class="lineNum">      30</span>              :     );</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaGNC">           8 :     on&lt;AuthEmailCheckWithRunCatching&gt;(_onAuthEmailCheckWithRunCatching);</span></span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           8 :     on&lt;TestError&gt;(_onTestError);</span></span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaGNC">           8 :     on&lt;TestErrorWithCatching&gt;(_onTestErrorWithCatching);</span></span>
<span id="L37"><span class="lineNum">      37</span>              :   }</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :   final AuthCheckMailUseCase authCheckMailUseCase;</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           3 :   late final GPMapper gpMapper = GetIt.I&lt;GPMapper&gt;(instanceName: 'kGPMapper');</span></span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaGNC">           2 :   Future _onTestEvent(TestEvent event, Emitter emit) async {</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaGNC">           2 :     throw Exception(&quot;test exception&quot;);</span></span>
<span id="L45"><span class="lineNum">      45</span>              :   }</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaGNC">           3 :   Future _onTestCounterEvent(TestCounterEvent event, Emitter emit) async {</span></span>
<span id="L48"><span class="lineNum">      48</span>              :     if (LogConfig.kLogOnStreamData || LogConfig.kLogOnStreamError) {</span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :       logD('_onTestCounterEvent');</span></span>
<span id="L50"><span class="lineNum">      50</span>              :     }</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaGNC">          12 :     return emit(TestState(&quot;_onTestCounterEvent: ${event.counter}&quot;));</span></span>
<span id="L53"><span class="lineNum">      53</span>              :   }</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaGNC">           1 :   Future _onTestErrorWithCatching(</span></span>
<span id="L56"><span class="lineNum">      56</span>              :       TestErrorWithCatching event, Emitter emit) async {</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           1 :     return runCatching(</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaGNC">           1 :       action: () async {</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaGNC">           2 :         addError(Exception(&quot;add an error exception&quot;));</span></span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaGNC">           1 :         throw Exception(&quot;throw exception&quot;);</span></span>
<span id="L62"><span class="lineNum">      62</span>              :       },</span>
<span id="L63"><span class="lineNum">      63</span>              :       handleLoading: true,</span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaGNC">           2 :       doOnCompleted: () async =&gt; emit(const TestState(&quot;success&quot;)),</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaGNC">           1 :       doOnSuccessOrError: () async {</span></span>
<span id="L66"><span class="lineNum">      66</span>              :         if (LogConfig.kLogOnStreamData || LogConfig.kLogOnStreamError) {</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :           logD('doOnSuccessOrError');</span></span>
<span id="L68"><span class="lineNum">      68</span>              :         }</span>
<span id="L69"><span class="lineNum">      69</span>              :       },</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaGNC">           1 :       doOnError: (error, stackTrace) async {</span></span>
<span id="L71"><span class="lineNum">      71</span>              :         if (LogConfig.kLogOnStreamError) {</span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :           logE('doOnError: $error');</span></span>
<span id="L73"><span class="lineNum">      73</span>              :         }</span>
<span id="L74"><span class="lineNum">      74</span>              :       },</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaGNC">           1 :       doOnStarted: () async {</span></span>
<span id="L76"><span class="lineNum">      76</span>              :         if (LogConfig.kLogOnStreamData) {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :           logD('doOnStarted aaaa');</span></span>
<span id="L78"><span class="lineNum">      78</span>              :         }</span>
<span id="L79"><span class="lineNum">      79</span>              :       },</span>
<span id="L80"><span class="lineNum">      80</span>              :       handleRetry: true,</span>
<span id="L81"><span class="lineNum">      81</span>              :       maxRetries: 2,</span>
<span id="L82"><span class="lineNum">      82</span>              :     );</span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           3 :   Future _onAuthEmailCheck(AuthEmailCheck event, Emitter emit) async {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaGNC">           3 :     showLoading();</span></span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaGNC">           2 :     await authCheckMailUseCase</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           4 :         .execute(event.authCheckEmailRequest)</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaGNC">           4 :         .then((value) {</span></span>
<span id="L91"><span class="lineNum">      91</span>              :       if (LogConfig.kLogOnStreamData) {</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :         logD(&quot;value -&gt; ${value.data.toJson()}&quot;);</span></span>
<span id="L93"><span class="lineNum">      93</span>              :       }</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaGNC">           2 :     }).catchError((error) {</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       if (LogConfig.kLogOnStreamError) {</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :         logE(&quot;_onAuthEmailCheck catchError $error&quot;);</span></span>
<span id="L97"><span class="lineNum">      97</span>              :       }</span>
<span id="L98"><span class="lineNum">      98</span>              :     });</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaGNC">           2 :     await Future.delayed(const Duration(seconds: 1));</span></span>
<span id="L101"><span class="lineNum">     101</span>              : </span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           1 :     hideLoading();</span></span>
<span id="L103"><span class="lineNum">     103</span>              :   }</span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaGNC">           2 :   Future _onAuthEmailCheckWithRunCatching(</span></span>
<span id="L106"><span class="lineNum">     106</span>              :       AuthEmailCheckWithRunCatching event, Emitter emit) async {</span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaGNC">           2 :     return runCatching(</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaGNC">           2 :       action: () async {</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaGNC">           2 :         await Future.delayed(const Duration(seconds: 2));</span></span>
<span id="L110"><span class="lineNum">     110</span>              : </span>
<span id="L111"><span class="lineNum">     111</span>              :         final response =</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaGNC">           3 :             await authCheckMailUseCase.execute(event.authCheckEmailRequest);</span></span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span>              :         final entity =</span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaGNC">           1 :             _convertWithCatching&lt;AuthCheckMailResponse, AuthCheckMailEntity&gt;(</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaGNC">           1 :           &lt;R, E&gt;(source) {</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaGNC">           1 :             return gpMapper.convert&lt;R, E&gt;(source);</span></span>
<span id="L118"><span class="lineNum">     118</span>              :           },</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaGNC">           1 :           response.data,</span></span>
<span id="L120"><span class="lineNum">     120</span>              :         );</span>
<span id="L121"><span class="lineNum">     121</span>              : </span>
<span id="L122"><span class="lineNum">     122</span>              :         if (LogConfig.kLogOnStreamData) {</span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :           logD(</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :               'entity after map -&gt; newDomain: ${entity.newDomain}, userId: ${entity.userId}');</span></span>
<span id="L125"><span class="lineNum">     125</span>              :         }</span>
<span id="L126"><span class="lineNum">     126</span>              :       },</span>
<span id="L127"><span class="lineNum">     127</span>              :       handleLoading: true,</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaGNC">           2 :       doOnCompleted: () async =&gt; emit(const TestState(&quot;success&quot;)),</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :       doOnSuccessOrError: () async {</span></span>
<span id="L130"><span class="lineNum">     130</span>              :         if (LogConfig.kLogOnStreamError || LogConfig.kLogOnStreamData) {</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :           logD('doOnSuccessOrError');</span></span>
<span id="L132"><span class="lineNum">     132</span>              :         }</span>
<span id="L133"><span class="lineNum">     133</span>              :       },</span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :       doOnError: (error, stackTrace) async {</span></span>
<span id="L135"><span class="lineNum">     135</span>              :         if (LogConfig.kLogOnStreamError) {</span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :           logE('doOnError: $error');</span></span>
<span id="L137"><span class="lineNum">     137</span>              :         }</span>
<span id="L138"><span class="lineNum">     138</span>              :       },</span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaGNC">           2 :       doOnStarted: () async {</span></span>
<span id="L140"><span class="lineNum">     140</span>              :         if (LogConfig.kLogOnStreamData) {</span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :           logD('doOnStarted aaaa');</span></span>
<span id="L142"><span class="lineNum">     142</span>              :         }</span>
<span id="L143"><span class="lineNum">     143</span>              :       },</span>
<span id="L144"><span class="lineNum">     144</span>              :       handleRetry: true,</span>
<span id="L145"><span class="lineNum">     145</span>              :       maxRetries: 2,</span>
<span id="L146"><span class="lineNum">     146</span>              :     );</span>
<span id="L147"><span class="lineNum">     147</span>              :   }</span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaGNC">           2 :   Future _onTestError(TestError event, Emitter emit) async {</span></span>
<span id="L150"><span class="lineNum">     150</span>              :     // addError(Exception(&quot;test exception&quot;));</span>
<span id="L151"><span class="lineNum">     151</span>              : </span>
<span id="L152"><span class="lineNum">     152</span>              :     // throw Exception(&quot;test exception&quot;);</span>
<span id="L153"><span class="lineNum">     153</span>              : </span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaGNC">           2 :     return runCatching(</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaGNC">           2 :       action: () async {</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaGNC">           2 :         await Future.delayed(const Duration(seconds: 2));</span></span>
<span id="L157"><span class="lineNum">     157</span>              : </span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaGNC">           2 :         addError(Exception(&quot;add an error exception&quot;));</span></span>
<span id="L159"><span class="lineNum">     159</span>              : </span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaGNC">           1 :         throw Exception(&quot;throw exception&quot;);</span></span>
<span id="L161"><span class="lineNum">     161</span>              :       },</span>
<span id="L162"><span class="lineNum">     162</span>              :       handleLoading: true,</span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaGNC">           2 :       doOnCompleted: () async =&gt; emit(const TestState(&quot;success&quot;)),</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaGNC">           1 :       doOnSuccessOrError: () async {</span></span>
<span id="L165"><span class="lineNum">     165</span>              :         if (LogConfig.kLogOnStreamData || LogConfig.kLogOnStreamError) {</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :           logD('doOnSuccessOrError');</span></span>
<span id="L167"><span class="lineNum">     167</span>              :         }</span>
<span id="L168"><span class="lineNum">     168</span>              :       },</span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaGNC">           1 :       doOnError: (error, stackTrace) async {</span></span>
<span id="L170"><span class="lineNum">     170</span>              :         if (LogConfig.kLogOnStreamError) {</span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :           logE('doOnError: $error');</span></span>
<span id="L172"><span class="lineNum">     172</span>              :         }</span>
<span id="L173"><span class="lineNum">     173</span>              :       },</span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaGNC">           2 :       doOnStarted: () async {</span></span>
<span id="L175"><span class="lineNum">     175</span>              :         if (LogConfig.kLogOnStreamData) {</span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :           logD('doOnStarted aaaa');</span></span>
<span id="L177"><span class="lineNum">     177</span>              :         }</span>
<span id="L178"><span class="lineNum">     178</span>              :       },</span>
<span id="L179"><span class="lineNum">     179</span>              :       handleRetry: true,</span>
<span id="L180"><span class="lineNum">     180</span>              :       maxRetries: 2,</span>
<span id="L181"><span class="lineNum">     181</span>              :     );</span>
<span id="L182"><span class="lineNum">     182</span>              :   }</span>
<span id="L183"><span class="lineNum">     183</span>              : </span>
<span id="L184"><span class="lineNum">     184</span>              :   /// convert [SOURCE] to [TARGET]</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaGNC">           1 :   TARGET _convertWithCatching&lt;SOURCE, TARGET&gt;(</span></span>
<span id="L186"><span class="lineNum">     186</span>              :     TARGET Function&lt;SOURCE, TARGET&gt;(SOURCE source) convertFunc,</span>
<span id="L187"><span class="lineNum">     187</span>              :     SOURCE source,</span>
<span id="L188"><span class="lineNum">     188</span>              :   ) {</span>
<span id="L189"><span class="lineNum">     189</span>              :     try {</span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaGNC">           1 :       return convertFunc.call&lt;SOURCE, TARGET&gt;(source);</span></span>
<span id="L191"><span class="lineNum">     191</span>              :     } catch (e) {</span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaGNC">           1 :       throw ParseException(ParseExceptionKind.errorMapping, e);</span></span>
<span id="L193"><span class="lineNum">     193</span>              :     }</span>
<span id="L194"><span class="lineNum">     194</span>              :   }</span>
<span id="L195"><span class="lineNum">     195</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
