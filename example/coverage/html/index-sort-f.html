<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info</title>
  <link rel="stylesheet" type="text/css" href="gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue">top level</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">47.8&nbsp;%</td>
            <td class="headerCovTableEntry">1067</td>
            <td class="headerCovTableEntry">510</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 17:25:33</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <center>
          <table width="80%" cellpadding=1 cellspacing=1 border=0>

            <tr>
              <td width="40%"><br></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            </tr>

            <tr>
              <td class="tableHead" rowspan=2>Directory <span  title="Click to sort table by file name" class="tableHeadSort"><a href="index.html"><img src="updown.png" width=10 height=14 alt="Sort by file name" title="Click to sort table by file name" border=0></a></span></td>
        <td class="tableHead" colspan=4>Line Coverage <span  title="Click to sort table by line coverage" class="tableHeadSort"><a href="index-sort-l.html"><img src="updown.png" width=10 height=14 alt="Sort by line coverage" title="Click to sort table by line coverage" border=0></a></span></td>
            </tr>
            <tr>
                    <td class="tableHead" colspan=2> Rate</td>
                    <td class="tableHead"> Total</td>
                    <td class="tableHead"> Hit</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib">lib/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=30 height=10 alt="29.5%"><img src="snow.png" width=70 height=10 alt="29.5%"></td></tr></table>
              </td>
              <td class="coverPerLo">29.5&nbsp;%</td>
              <td class="coverNumDflt">61</td>
              <td class="coverNumDflt">18</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/data_source/remote/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/data_source/remote">lib/data/data_source/remote/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">31</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/model/auth/request/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/model/auth/request">lib/data/model/auth/request/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">12</td>
              <td class="coverNumDflt">12</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/model/auth/response/auth/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/model/auth/response/auth">lib/data/model/auth/response/auth/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">14</td>
              <td class="coverNumDflt">14</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/data/repository/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/data/repository">lib/data/repository/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">3</td>
              <td class="coverNumDflt">3</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/di/component/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/di/component">lib/di/component/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">5</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/di/modules/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/di/modules">lib/di/modules/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="amber.png" width=84 height=10 alt="84.2%"><img src="snow.png" width=16 height=10 alt="84.2%"></td></tr></table>
              </td>
              <td class="coverPerMed">84.2&nbsp;%</td>
              <td class="coverNumDflt">19</td>
              <td class="coverNumDflt">16</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/domain/entity/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity">lib/domain/entity/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">4</td>
              <td class="coverNumDflt">4</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/domain/entity/test/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test">lib/domain/entity/test/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="amber.png" width=85 height=10 alt="85.4%"><img src="snow.png" width=15 height=10 alt="85.4%"></td></tr></table>
              </td>
              <td class="coverPerMed">85.4&nbsp;%</td>
              <td class="coverNumDflt">89</td>
              <td class="coverNumDflt">76</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/domain/usecase/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/usecase">lib/domain/usecase/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">3</td>
              <td class="coverNumDflt">3</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/mapper/entity/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/mapper/entity">lib/mapper/entity/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=20 height=10 alt="20.4%"><img src="snow.png" width=80 height=10 alt="20.4%"></td></tr></table>
              </td>
              <td class="coverPerLo">20.4&nbsp;%</td>
              <td class="coverNumDflt">167</td>
              <td class="coverNumDflt">34</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/mapper/entity/auth/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/mapper/entity/auth">lib/mapper/entity/auth/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=29 height=10 alt="28.6%"><img src="snow.png" width=71 height=10 alt="28.6%"></td></tr></table>
              </td>
              <td class="coverPerLo">28.6&nbsp;%</td>
              <td class="coverNumDflt">91</td>
              <td class="coverNumDflt">26</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/navigator/app_navigator/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/navigator/app_navigator">lib/navigator/app_navigator/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">113</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/navigator/go_router/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/navigator/go_router">lib/navigator/go_router/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=3 height=10 alt="3.4%"><img src="snow.png" width=97 height=10 alt="3.4%"></td></tr></table>
              </td>
              <td class="coverPerLo">3.4&nbsp;%</td>
              <td class="coverNumDflt">87</td>
              <td class="coverNumDflt">3</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/navigator/mapper/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/navigator/mapper">lib/navigator/mapper/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="ruby.png" width=31 height=10 alt="30.8%"><img src="snow.png" width=69 height=10 alt="30.8%"></td></tr></table>
              </td>
              <td class="coverPerLo">30.8&nbsp;%</td>
              <td class="coverNumDflt">26</td>
              <td class="coverNumDflt">8</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/navigator/model/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/navigator/model">lib/navigator/model/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">1</td>
              <td class="coverNumDflt">1</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation">lib/presentation/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">2</td>
              <td class="coverNumDflt">2</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/assignee/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/assignee">lib/presentation/assignee/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">10</td>
              <td class="coverNumDflt">10</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/home/<USER>" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/home">lib/presentation/home/<USER>/a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=90 height=10 alt="90.4%"><img src="snow.png" width=10 height=10 alt="90.4%"></td></tr></table>
              </td>
              <td class="coverPerHi">90.4&nbsp;%</td>
              <td class="coverNumDflt">125</td>
              <td class="coverNumDflt">113</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/login/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/login">lib/presentation/login/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="emerald.png" width=100 height=10 alt="100.0%"></td></tr></table>
              </td>
              <td class="coverPerHi">100.0&nbsp;%</td>
              <td class="coverNumDflt">21</td>
              <td class="coverNumDflt">21</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/shared/talker/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/shared/talker">lib/presentation/shared/talker/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">5</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/test/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/test">lib/presentation/test/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="amber.png" width=82 height=10 alt="81.7%"><img src="snow.png" width=18 height=10 alt="81.7%"></td></tr></table>
              </td>
              <td class="coverPerMed">81.7&nbsp;%</td>
              <td class="coverNumDflt">60</td>
              <td class="coverNumDflt">49</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/test/bloc/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/test/bloc">lib/presentation/test/bloc/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="amber.png" width=82 height=10 alt="82.2%"><img src="snow.png" width=18 height=10 alt="82.2%"></td></tr></table>
              </td>
              <td class="coverPerMed">82.2&nbsp;%</td>
              <td class="coverNumDflt">90</td>
              <td class="coverNumDflt">74</td>
            </tr>
            <tr>
              <td class="coverDirectory"><a href="lib/presentation/user/index.html" title="Click to go to directory /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/presentation/user">lib/presentation/user/</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="amber.png" width=82 height=10 alt="82.1%"><img src="snow.png" width=18 height=10 alt="82.1%"></td></tr></table>
              </td>
              <td class="coverPerMed">82.1&nbsp;%</td>
              <td class="coverNumDflt">28</td>
              <td class="coverNumDflt">23</td>
            </tr>
        <tr>
          <td class="footnote" colspan=5>Note:  'Function Coverage' columns elided as function owner is not identified.</td>
         </tr>
          </table>
          </center>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
