<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - domain/entity/test</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - domain/entity/test</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">51.7&nbsp;%</td>
            <td class="headerCovTableEntry">89</td>
            <td class="headerCovTableEntry">46</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 09:57:39</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <center>
          <table width="80%" cellpadding=1 cellspacing=1 border=0>

            <tr>
              <td width="40%"><br></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            </tr>

            <tr>
              <td class="tableHead" rowspan=2>File <span  title="Click to sort table by file name" class="tableHeadSort"><img src="../../../glass.png" width=10 height=14 alt="Sort by file name" title="Click to sort table by file name" border=0></span></td>
        <td class="tableHead" colspan=4>Line Coverage <span  title="Click to sort table by line coverage" class="tableHeadSort"><a href="index-sort-l.html"><img src="../../../updown.png" width=10 height=14 alt="Sort by line coverage" title="Click to sort table by line coverage" border=0></a></span></td>
            </tr>
            <tr>
                    <td class="tableHead" colspan=2> Rate</td>
                    <td class="tableHead"> Total</td>
                    <td class="tableHead"> Hit</td>
            </tr>
            <tr>
              <td class="coverFile"><a href="assignee.entity.dart.gcov.html" title="Click to go to file /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test/assignee.entity.dart">assignee.entity.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../ruby.png" width=45 height=10 alt="45.5%"><img src="../../../snow.png" width=55 height=10 alt="45.5%"></td></tr></table>
              </td>
              <td class="coverPerLo">45.5&nbsp;%</td>
              <td class="coverNumDflt">22</td>
              <td class="coverNumDflt">10</td>
            </tr>
            <tr>
              <td class="coverFile"><a href="assignee.entity.g.dart.gcov.html" title="Click to go to file /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test/assignee.entity.g.dart">assignee.entity.g.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../emerald.png" width=97 height=10 alt="97.3%"><img src="../../../snow.png" width=3 height=10 alt="97.3%"></td></tr></table>
              </td>
              <td class="coverPerHi">97.3&nbsp;%</td>
              <td class="coverNumDflt">37</td>
              <td class="coverNumDflt">36</td>
            </tr>
            <tr>
              <td class="coverFile"><a href="assignee_info.entity.dart.gcov.html" title="Click to go to file /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test/assignee_info.entity.dart">assignee_info.entity.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">2</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="assignee_info.entity.g.dart.gcov.html" title="Click to go to file /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test/assignee_info.entity.g.dart">assignee_info.entity.g.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">7</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="assignee_work.entity.dart.gcov.html" title="Click to go to file /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test/assignee_work.entity.dart">assignee_work.entity.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">2</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="assignee_work.entity.g.dart.gcov.html" title="Click to go to file /Users/<USER>/Softwares/flutter.packages/gp_core_v2/example/lib/domain/entity/test/assignee_work.entity.g.dart">assignee_work.entity.g.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">19</td>
              <td class="coverNumDflt"></td>
            </tr>
        <tr>
          <td class="footnote" colspan=5>Note:  'Function Coverage' columns elided as function owner is not identified.</td>
         </tr>
          </table>
          </center>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
