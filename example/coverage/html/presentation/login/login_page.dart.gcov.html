<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - presentation/login/login_page.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory presentation/login">presentation/login</a> - login_page.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryHi">95.2&nbsp;%</td>
            <td class="headerCovTableEntry">21</td>
            <td class="headerCovTableEntry">20</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 09:57:39</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:example/presentation/utils.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/material.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../navigator/model/example_app_info_route.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : class ExampleLoginPage extends StatelessWidget {</span>
<span id="L7"><span class="lineNum">       7</span> <span class="tlaGNC">           2 :   const ExampleLoginPage({super.key});</span></span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaGNC">           2 :   void navigateToHome(BuildContext context) {</span></span>
<span id="L10"><span class="lineNum">      10</span> <span class="tlaGNC">           2 :     context.appNavigator().popAndPush(</span></span>
<span id="L11"><span class="lineNum">      11</span>              :           context,</span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :           ExampleAppInfoRoute.home(),</span></span>
<span id="L13"><span class="lineNum">      13</span>              :         );</span>
<span id="L14"><span class="lineNum">      14</span>              :   }</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L17"><span class="lineNum">      17</span>              :   Widget build(BuildContext context) {</span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaGNC">           2 :     return Scaffold(</span></span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaGNC">           2 :       appBar: AppBar(</span></span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           2 :         title: Text('Login'),</span></span>
<span id="L21"><span class="lineNum">      21</span>              :       ),</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaGNC">           2 :       body: SafeArea(</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaGNC">           2 :         child: Padding(</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaGNC">           2 :           padding: EdgeInsets.fromLTRB(16, 100, 16, 16),</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaGNC">           2 :           child: SingleChildScrollView(</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaGNC">           2 :             child: Column(</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaGNC">           2 :               children: [</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           2 :                 TextFormField(</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaGNC">           2 :                   decoration: InputDecoration(</span></span>
<span id="L30"><span class="lineNum">      30</span>              :                     labelText: 'Email',</span>
<span id="L31"><span class="lineNum">      31</span>              :                   ),</span>
<span id="L32"><span class="lineNum">      32</span>              :                 ),</span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaGNC">           2 :                 TextFormField(</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaGNC">           2 :                   decoration: InputDecoration(</span></span>
<span id="L35"><span class="lineNum">      35</span>              :                     labelText: 'Password',</span>
<span id="L36"><span class="lineNum">      36</span>              :                   ),</span>
<span id="L37"><span class="lineNum">      37</span>              :                 ),</span>
<span id="L38"><span class="lineNum">      38</span>              :                 const SizedBox(height: 16),</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           2 :                 ElevatedButton(</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaGNC">           4 :                   onPressed: () =&gt; navigateToHome(context),</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           2 :                   child: Text('Login'),</span></span>
<span id="L42"><span class="lineNum">      42</span>              :                 ),</span>
<span id="L43"><span class="lineNum">      43</span>              :               ],</span>
<span id="L44"><span class="lineNum">      44</span>              :             ),</span>
<span id="L45"><span class="lineNum">      45</span>              :           ),</span>
<span id="L46"><span class="lineNum">      46</span>              :         ),</span>
<span id="L47"><span class="lineNum">      47</span>              :       ),</span>
<span id="L48"><span class="lineNum">      48</span>              :     );</span>
<span id="L49"><span class="lineNum">      49</span>              :   }</span>
<span id="L50"><span class="lineNum">      50</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
