<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - presentation/test/test_page_behavior.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory presentation/test">presentation/test</a> - test_page_behavior.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">31.6&nbsp;%</td>
            <td class="headerCovTableEntry">19</td>
            <td class="headerCovTableEntry">6</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 09:57:39</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:example/data/model/auth/request/auth_check_email_request.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:example/presentation/utils.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter/material.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_bloc/flutter_bloc.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../navigator/model/example_app_info_route.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'bloc/test_bloc.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'bloc/test_event.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : /// Toàn bộ behavior của `TestPage`</span>
<span id="L11"><span class="lineNum">      11</span>              : abstract class TestPageBehavior {</span>
<span id="L12"><span class="lineNum">      12</span>              :   void authCheckEmailRequest(BuildContext context);</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   void authCheckEmailRequestWithUseCase(BuildContext context);</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              :   void test(BuildContext context);</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   void testCounter(BuildContext context);</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span>              :   void testError(BuildContext context);</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   void testErrorWithCatching(BuildContext context);</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              :   void navigateToLogin(BuildContext context);</span>
<span id="L25"><span class="lineNum">      25</span>              : }</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              : mixin TestPageBehaviorMixin on Widget implements TestPageBehavior {</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L29"><span class="lineNum">      29</span>              :   void authCheckEmailRequest(BuildContext context) {</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaGNC">           1 :     context.read&lt;TestBloc&gt;().add(</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :           AuthEmailCheck(</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :             AuthCheckEmailRequest('<EMAIL>', ''),</span></span>
<span id="L33"><span class="lineNum">      33</span>              :           ),</span>
<span id="L34"><span class="lineNum">      34</span>              :         );</span>
<span id="L35"><span class="lineNum">      35</span>              :   }</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L38"><span class="lineNum">      38</span>              :   void authCheckEmailRequestWithUseCase(BuildContext context) {</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :     context.read&lt;TestBloc&gt;().add(</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :           AuthEmailCheckWithRunCatching(</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :             AuthCheckEmailRequest('<EMAIL>', ''),</span></span>
<span id="L42"><span class="lineNum">      42</span>              :           ),</span>
<span id="L43"><span class="lineNum">      43</span>              :         );</span>
<span id="L44"><span class="lineNum">      44</span>              :   }</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L47"><span class="lineNum">      47</span>              :   void test(BuildContext context) {</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaGNC">           1 :     context.read&lt;TestBloc&gt;().add(const TestEvent());</span></span>
<span id="L49"><span class="lineNum">      49</span>              :   }</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L52"><span class="lineNum">      52</span>              :   void testCounter(BuildContext context) {</span>
<span id="L53"><span class="lineNum">      53</span>              :     // fixed params</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaGNC">           1 :     context.read&lt;TestBloc&gt;().add(TestCounterEvent(100));</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L58"><span class="lineNum">      58</span>              :   void testError(BuildContext context) {</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :     context.read&lt;TestBloc&gt;().add(const TestError());</span></span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L63"><span class="lineNum">      63</span>              :   void testErrorWithCatching(BuildContext context) {</span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :     context.read&lt;TestBloc&gt;().add(const TestError());</span></span>
<span id="L65"><span class="lineNum">      65</span>              :   }</span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   void navigateToLogin(BuildContext context) {</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :     context.appNavigator().push(</span></span>
<span id="L70"><span class="lineNum">      70</span>              :           context,</span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :           ExampleAppInfoRoute.login(),</span></span>
<span id="L72"><span class="lineNum">      72</span>              :         );</span>
<span id="L73"><span class="lineNum">      73</span>              :   }</span>
<span id="L74"><span class="lineNum">      74</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
