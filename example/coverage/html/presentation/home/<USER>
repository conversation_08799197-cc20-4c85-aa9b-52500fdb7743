<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - presentation/home/<USER>/title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory presentation/home">presentation/home</a> - home_behavior.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">34.4&nbsp;%</td>
            <td class="headerCovTableEntry">64</td>
            <td class="headerCovTableEntry">22</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 09:57:39</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:example/presentation/presentation.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter/material.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:gp_core/core.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:gp_core_v2/base/navigator/model/popup/app_popup_info.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:gp_core_v2/base/navigator/model/snackbar/app_snackbar_info.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../domain/entity/test.entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../navigator/model/example_app_info_route.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              : /// Toàn bộ behavior của `HomePage`</span>
<span id="L11"><span class="lineNum">      11</span>              : abstract class HomeBehavior {</span>
<span id="L12"><span class="lineNum">      12</span>              :   void navigateToUserProfile(BuildContext context);</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   // ---------- SnackBar ---------- \\</span>
<span id="L15"><span class="lineNum">      15</span>              :   void showNormalSnackBar({required BuildContext context});</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              :   void showSuccessSnackBar({required BuildContext context});</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   void showErrorSnackBar({required BuildContext context});</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              :   // ---------- SnackBar ---------- \\</span>
<span id="L22"><span class="lineNum">      22</span>              :   void showDialogWithOneBtn({required BuildContext context});</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              :   void showDialogWithTwoBtn({required BuildContext context});</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   void showDialogWithTwoVerticalBtn({required BuildContext context});</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   // ---------- BottomSheet ---------- \\</span>
<span id="L29"><span class="lineNum">      29</span>              :   void showSimpleBottomSheet({required BuildContext context});</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              :   void showBottomSheetWithOneBtn({required BuildContext context});</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   void showBottomSheetWithTwoBtn({required BuildContext context});</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span>              :   void showBottomSheetWithTwoVerticalBtn({required BuildContext context});</span>
<span id="L36"><span class="lineNum">      36</span>              : }</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              : mixin HomeBehaviorMixin on Widget implements HomeBehavior {</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaGNC">           3 :   @override</span></span>
<span id="L40"><span class="lineNum">      40</span>              :   void navigateToUserProfile(BuildContext context) {</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaGNC">           3 :     context.appNavigator().push(</span></span>
<span id="L42"><span class="lineNum">      42</span>              :           context,</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :           ExampleAppInfoRoute.userProfile(</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :             user: User(</span></span>
<span id="L45"><span class="lineNum">      45</span>              :               id: 1,</span>
<span id="L46"><span class="lineNum">      46</span>              :               name: 'Nguyen Manh Toan',</span>
<span id="L47"><span class="lineNum">      47</span>              :               tag: '<EMAIL>',</span>
<span id="L48"><span class="lineNum">      48</span>              :             ),</span>
<span id="L49"><span class="lineNum">      49</span>              :           ),</span>
<span id="L50"><span class="lineNum">      50</span>              :         );</span>
<span id="L51"><span class="lineNum">      51</span>              :   }</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L54"><span class="lineNum">      54</span>              :   void showNormalSnackBar({</span>
<span id="L55"><span class="lineNum">      55</span>              :     required BuildContext context,</span>
<span id="L56"><span class="lineNum">      56</span>              :   }) {</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaGNC">           2 :     context.appNavigator().showSnackBar(</span></span>
<span id="L58"><span class="lineNum">      58</span>              :           context: context,</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :           snackbarInfo: GPAppSnackBarInfo(</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :             contentWidget: Text(</span></span>
<span id="L61"><span class="lineNum">      61</span>              :               'Normal snackbar',</span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :               style: TextStyle(color: Colors.white),</span></span>
<span id="L63"><span class="lineNum">      63</span>              :             ),</span>
<span id="L64"><span class="lineNum">      64</span>              :             snackBarType: GPSnackBarType.normal,</span>
<span id="L65"><span class="lineNum">      65</span>              :           ),</span>
<span id="L66"><span class="lineNum">      66</span>              :         );</span>
<span id="L67"><span class="lineNum">      67</span>              :   }</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L70"><span class="lineNum">      70</span>              :   void showSuccessSnackBar({</span>
<span id="L71"><span class="lineNum">      71</span>              :     required BuildContext context,</span>
<span id="L72"><span class="lineNum">      72</span>              :   }) {</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaGNC">           2 :     context.appNavigator().showSnackBar(</span></span>
<span id="L74"><span class="lineNum">      74</span>              :           context: context,</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :           snackbarInfo: GPAppSnackBarInfo(</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :             contentWidget: Text(</span></span>
<span id="L77"><span class="lineNum">      77</span>              :               'Normal snackbar',</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :               style: TextStyle(color: Colors.white),</span></span>
<span id="L79"><span class="lineNum">      79</span>              :             ),</span>
<span id="L80"><span class="lineNum">      80</span>              :             snackBarType: GPSnackBarType.success,</span>
<span id="L81"><span class="lineNum">      81</span>              :           ),</span>
<span id="L82"><span class="lineNum">      82</span>              :         );</span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L86"><span class="lineNum">      86</span>              :   void showErrorSnackBar({</span>
<span id="L87"><span class="lineNum">      87</span>              :     required BuildContext context,</span>
<span id="L88"><span class="lineNum">      88</span>              :   }) {</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaGNC">           2 :     context.appNavigator().showSnackBar(</span></span>
<span id="L90"><span class="lineNum">      90</span>              :           context: context,</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :           snackbarInfo: GPAppSnackBarInfo(</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :             contentWidget: Text(</span></span>
<span id="L93"><span class="lineNum">      93</span>              :               'Error snackbar',</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :               style: TextStyle(color: Colors.white),</span></span>
<span id="L95"><span class="lineNum">      95</span>              :             ),</span>
<span id="L96"><span class="lineNum">      96</span>              :             snackBarType: GPSnackBarType.error,</span>
<span id="L97"><span class="lineNum">      97</span>              :           ),</span>
<span id="L98"><span class="lineNum">      98</span>              :         );</span>
<span id="L99"><span class="lineNum">      99</span>              :   }</span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span>              :   // ---------- Dialog ---------- \\</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L103"><span class="lineNum">     103</span>              :   void showDialogWithOneBtn({required BuildContext context}) {</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaGNC">           2 :     context.appNavigator().showDialog(</span></span>
<span id="L105"><span class="lineNum">     105</span>              :           context: context,</span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L107"><span class="lineNum">     107</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L108"><span class="lineNum">     108</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L109"><span class="lineNum">     109</span>              :                 'appears in front of app content to\n'</span>
<span id="L110"><span class="lineNum">     110</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L111"><span class="lineNum">     111</span>              :                 'for a decision to be made.',</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L113"><span class="lineNum">     113</span>              :               displayName: 'OK',</span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L115"><span class="lineNum">     115</span>              :             ),</span>
<span id="L116"><span class="lineNum">     116</span>              :           ),</span>
<span id="L117"><span class="lineNum">     117</span>              :         );</span>
<span id="L118"><span class="lineNum">     118</span>              :   }</span>
<span id="L119"><span class="lineNum">     119</span>              : </span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L121"><span class="lineNum">     121</span>              :   void showDialogWithTwoBtn({required BuildContext context}) {</span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaGNC">           2 :     context.appNavigator().showDialog(</span></span>
<span id="L123"><span class="lineNum">     123</span>              :           context: context,</span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L125"><span class="lineNum">     125</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L126"><span class="lineNum">     126</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L127"><span class="lineNum">     127</span>              :                 'appears in front of app content to\n'</span>
<span id="L128"><span class="lineNum">     128</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L129"><span class="lineNum">     129</span>              :                 'for a decision to be made.',</span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultNegativeModel(</span></span>
<span id="L131"><span class="lineNum">     131</span>              :               displayName: 'Cancel',</span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L133"><span class="lineNum">     133</span>              :             ),</span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :             secondBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L135"><span class="lineNum">     135</span>              :               displayName: 'OK',</span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L137"><span class="lineNum">     137</span>              :             ),</span>
<span id="L138"><span class="lineNum">     138</span>              :           ),</span>
<span id="L139"><span class="lineNum">     139</span>              :         );</span>
<span id="L140"><span class="lineNum">     140</span>              :   }</span>
<span id="L141"><span class="lineNum">     141</span>              : </span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaGNC">           2 :   @override</span></span>
<span id="L143"><span class="lineNum">     143</span>              :   void showDialogWithTwoVerticalBtn({required BuildContext context}) {</span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaGNC">           2 :     context.appNavigator().showDialog(</span></span>
<span id="L145"><span class="lineNum">     145</span>              :           context: context,</span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L147"><span class="lineNum">     147</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L148"><span class="lineNum">     148</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L149"><span class="lineNum">     149</span>              :                 'appears in front of app content to\n'</span>
<span id="L150"><span class="lineNum">     150</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L151"><span class="lineNum">     151</span>              :                 'for a decision to be made.',</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L153"><span class="lineNum">     153</span>              :               displayName: 'OK',</span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L155"><span class="lineNum">     155</span>              :             ),</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :             secondBtn: GPAppPopupInfoBtn.defaultNegativeModel(</span></span>
<span id="L157"><span class="lineNum">     157</span>              :               displayName: 'Cancel',</span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L159"><span class="lineNum">     159</span>              :             ),</span>
<span id="L160"><span class="lineNum">     160</span>              :             buttonLayout: GPAppPopupButtonLayout.vertical,</span>
<span id="L161"><span class="lineNum">     161</span>              :           ),</span>
<span id="L162"><span class="lineNum">     162</span>              :         );</span>
<span id="L163"><span class="lineNum">     163</span>              :   }</span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L166"><span class="lineNum">     166</span>              :   void showBottomSheetWithOneBtn({required BuildContext context}) {</span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaGNC">           1 :     context.appNavigator().showBaseBottomSheet(</span></span>
<span id="L168"><span class="lineNum">     168</span>              :           context: context,</span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L170"><span class="lineNum">     170</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L171"><span class="lineNum">     171</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L172"><span class="lineNum">     172</span>              :                 'appears in front of app content to\n'</span>
<span id="L173"><span class="lineNum">     173</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L174"><span class="lineNum">     174</span>              :                 'for a decision to be made.',</span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L176"><span class="lineNum">     176</span>              :               displayName: 'OK',</span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L178"><span class="lineNum">     178</span>              :             ),</span>
<span id="L179"><span class="lineNum">     179</span>              :           ),</span>
<span id="L180"><span class="lineNum">     180</span>              :         );</span>
<span id="L181"><span class="lineNum">     181</span>              :   }</span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span>              :   // ---------- BottomSheet ---------- \\</span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L185"><span class="lineNum">     185</span>              :   void showBottomSheetWithTwoBtn({required BuildContext context}) {</span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaGNC">           1 :     context.appNavigator().showBaseBottomSheet(</span></span>
<span id="L187"><span class="lineNum">     187</span>              :           context: context,</span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L189"><span class="lineNum">     189</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L190"><span class="lineNum">     190</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L191"><span class="lineNum">     191</span>              :                 'appears in front of app content to\n'</span>
<span id="L192"><span class="lineNum">     192</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L193"><span class="lineNum">     193</span>              :                 'for a decision to be made.',</span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultNegativeModel(</span></span>
<span id="L195"><span class="lineNum">     195</span>              :               displayName: 'Cancel',</span>
<span id="L196"><span class="lineNum">     196</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L197"><span class="lineNum">     197</span>              :             ),</span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :             secondBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L199"><span class="lineNum">     199</span>              :               displayName: 'OK',</span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L201"><span class="lineNum">     201</span>              :             ),</span>
<span id="L202"><span class="lineNum">     202</span>              :           ),</span>
<span id="L203"><span class="lineNum">     203</span>              :         );</span>
<span id="L204"><span class="lineNum">     204</span>              :   }</span>
<span id="L205"><span class="lineNum">     205</span>              : </span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L207"><span class="lineNum">     207</span>              :   void showSimpleBottomSheet({required BuildContext context}) {</span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaGNC">           1 :     context.appNavigator().showBaseBottomSheet(</span></span>
<span id="L209"><span class="lineNum">     209</span>              :           context: context,</span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L211"><span class="lineNum">     211</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L212"><span class="lineNum">     212</span>              :             iconSrc: Assets</span>
<span id="L213"><span class="lineNum">     213</span>              :                 .PACKAGES_GP_ASSETS_IMAGES_SVG_CHILD_CALENDAR_NOTI_ROOM_SVG,</span>
<span id="L214"><span class="lineNum">     214</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L215"><span class="lineNum">     215</span>              :                 'appears in front of app content to\n'</span>
<span id="L216"><span class="lineNum">     216</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L217"><span class="lineNum">     217</span>              :                 'for a decision to be made.',</span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultNegativeModel(</span></span>
<span id="L219"><span class="lineNum">     219</span>              :               displayName: 'Cancel',</span>
<span id="L220"><span class="lineNum">     220</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L221"><span class="lineNum">     221</span>              :             ),</span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :             secondBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L223"><span class="lineNum">     223</span>              :               displayName: 'OK',</span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L225"><span class="lineNum">     225</span>              :             ),</span>
<span id="L226"><span class="lineNum">     226</span>              :           ),</span>
<span id="L227"><span class="lineNum">     227</span>              :         );</span>
<span id="L228"><span class="lineNum">     228</span>              :   }</span>
<span id="L229"><span class="lineNum">     229</span>              : </span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaGNC">           1 :   @override</span></span>
<span id="L231"><span class="lineNum">     231</span>              :   void showBottomSheetWithTwoVerticalBtn({required BuildContext context}) {</span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaGNC">           1 :     context.appNavigator().showBaseBottomSheet(</span></span>
<span id="L233"><span class="lineNum">     233</span>              :           context: context,</span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :           popupInfo: GPAppPopupInfo(</span></span>
<span id="L235"><span class="lineNum">     235</span>              :             title: 'GPBaseInfoWidget',</span>
<span id="L236"><span class="lineNum">     236</span>              :             content: 'A dialog is a type of modal window that\n'</span>
<span id="L237"><span class="lineNum">     237</span>              :                 'appears in front of app content to\n'</span>
<span id="L238"><span class="lineNum">     238</span>              :                 'provide critical information, or prompt\n'</span>
<span id="L239"><span class="lineNum">     239</span>              :                 'for a decision to be made.',</span>
<span id="L240"><span class="lineNum">     240</span> <span class="tlaUNC">           0 :             firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(</span></span>
<span id="L241"><span class="lineNum">     241</span>              :               displayName: 'OK',</span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L243"><span class="lineNum">     243</span>              :             ),</span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaUNC">           0 :             secondBtn: GPAppPopupInfoBtn.defaultNegativeModel(</span></span>
<span id="L245"><span class="lineNum">     245</span>              :               displayName: 'Cancel',</span>
<span id="L246"><span class="lineNum">     246</span> <span class="tlaUNC">           0 :               onClick: (context) =&gt; context.appNavigator().pop(context),</span></span>
<span id="L247"><span class="lineNum">     247</span>              :             ),</span>
<span id="L248"><span class="lineNum">     248</span>              :             buttonLayout: GPAppPopupButtonLayout.vertical,</span>
<span id="L249"><span class="lineNum">     249</span>              :           ),</span>
<span id="L250"><span class="lineNum">     250</span>              :         );</span>
<span id="L251"><span class="lineNum">     251</span>              :   }</span>
<span id="L252"><span class="lineNum">     252</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
