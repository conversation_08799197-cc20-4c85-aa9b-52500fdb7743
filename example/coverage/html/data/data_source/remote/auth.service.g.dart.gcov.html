<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - data/data_source/remote/auth.service.g.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory data/data_source/remote">data/data_source/remote</a> - auth.service.g.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">31</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-26 09:57:39</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : // GENERATED CODE - DO NOT MODIFY BY HAND</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : part of 'auth.service.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : // **************************************************************************</span>
<span id="L6"><span class="lineNum">       6</span>              : // RetrofitGenerator</span>
<span id="L7"><span class="lineNum">       7</span>              : // **************************************************************************</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : // ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : class _AuthService implements AuthService {</span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :   _AuthService(this._dio, {this.baseUrl, this.errorLogger});</span></span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   final Dio _dio;</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              :   String? baseUrl;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   final ParseErrorLogger? errorLogger;</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L21"><span class="lineNum">      21</span>              :   Future&lt;ApiResponseV2&lt;AuthCheckMailResponse&gt;&gt; checkEmail({</span>
<span id="L22"><span class="lineNum">      22</span>              :     required AuthCheckEmailRequest checkEmailRequest,</span>
<span id="L23"><span class="lineNum">      23</span>              :   }) async {</span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :     final _extra = &lt;String, dynamic&gt;{};</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :     final queryParameters = &lt;String, dynamic&gt;{};</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :     final _headers = &lt;String, dynamic&gt;{r'Content-Type': 'application/json'};</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :     _headers.removeWhere((k, v) =&gt; v == null);</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :     final _data = &lt;String, dynamic&gt;{};</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :     _data.addAll(checkEmailRequest.toJson());</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :     final _options = _setStreamType&lt;ApiResponseV2&lt;AuthCheckMailResponse&gt;&gt;(</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :       Options(</span></span>
<span id="L32"><span class="lineNum">      32</span>              :         method: 'POST',</span>
<span id="L33"><span class="lineNum">      33</span>              :         headers: _headers,</span>
<span id="L34"><span class="lineNum">      34</span>              :         extra: _extra,</span>
<span id="L35"><span class="lineNum">      35</span>              :         contentType: 'application/json',</span>
<span id="L36"><span class="lineNum">      36</span>              :       )</span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :           .compose(</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :             _dio.options,</span></span>
<span id="L39"><span class="lineNum">      39</span>              :             '/check-email',</span>
<span id="L40"><span class="lineNum">      40</span>              :             queryParameters: queryParameters,</span>
<span id="L41"><span class="lineNum">      41</span>              :             data: _data,</span>
<span id="L42"><span class="lineNum">      42</span>              :           )</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :           .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),</span></span>
<span id="L44"><span class="lineNum">      44</span>              :     );</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     final _result = await _dio.fetch&lt;Map&lt;String, dynamic&gt;&gt;(_options);</span></span>
<span id="L46"><span class="lineNum">      46</span>              :     late ApiResponseV2&lt;AuthCheckMailResponse&gt; _value;</span>
<span id="L47"><span class="lineNum">      47</span>              :     try {</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       _value = ApiResponseV2&lt;AuthCheckMailResponse&gt;.fromJson(</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         _result.data!,</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :         (json) =&gt; AuthCheckMailResponse.fromJson(json as Map&lt;String, dynamic&gt;),</span></span>
<span id="L51"><span class="lineNum">      51</span>              :       );</span>
<span id="L52"><span class="lineNum">      52</span>              :     } on Object catch (e, s) {</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :       errorLogger?.logError(e, s, _options);</span></span>
<span id="L54"><span class="lineNum">      54</span>              :       rethrow;</span>
<span id="L55"><span class="lineNum">      55</span>              :     }</span>
<span id="L56"><span class="lineNum">      56</span>              :     return _value;</span>
<span id="L57"><span class="lineNum">      57</span>              :   }</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :   RequestOptions _setStreamType&lt;T&gt;(RequestOptions requestOptions) {</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :     if (T != dynamic &amp;&amp;</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         !(requestOptions.responseType == ResponseType.bytes ||</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :             requestOptions.responseType == ResponseType.stream)) {</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :       if (T == String) {</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         requestOptions.responseType = ResponseType.plain;</span></span>
<span id="L65"><span class="lineNum">      65</span>              :       } else {</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         requestOptions.responseType = ResponseType.json;</span></span>
<span id="L67"><span class="lineNum">      67</span>              :       }</span>
<span id="L68"><span class="lineNum">      68</span>              :     }</span>
<span id="L69"><span class="lineNum">      69</span>              :     return requestOptions;</span>
<span id="L70"><span class="lineNum">      70</span>              :   }</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :   String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :     if (baseUrl == null || baseUrl.trim().isEmpty) {</span></span>
<span id="L74"><span class="lineNum">      74</span>              :       return dioBaseUrl;</span>
<span id="L75"><span class="lineNum">      75</span>              :     }</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     final url = Uri.parse(baseUrl);</span></span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :     if (url.isAbsolute) {</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :       return url.toString();</span></span>
<span id="L81"><span class="lineNum">      81</span>              :     }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :     return Uri.parse(dioBaseUrl).resolveUri(url).toString();</span></span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3.1-1</a></td></tr>
          </table>
          <br>

</body>
</html>
