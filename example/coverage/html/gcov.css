/* All views: initial background and text color */
body
{
  color: #000000;
  background-color: #ffffff;
}

/* All views: standard link format*/
a:link
{
  color: #284fa8;
  text-decoration: underline;
}

/* All views: standard link - visited format */
a:visited
{
  color: #00cb40;
  text-decoration: underline;
}

/* All views: standard link - activated format */
a:active
{
  color: #ff0040;
  text-decoration: underline;
}

/* All views: main title format */
td.title
{
  text-align: center;
  padding-bottom: 10px;
  font-family: sans-serif;
  font-size: 20pt;
  font-style: italic;
  font-weight: bold;
}
/* table footnote */
td.footnote
{
  text-align: left;
  padding-left: 100px;
  padding-right: 10px;
  background-color: #dae7fe; /* light blue table background color */
  /* dark blue table header color
  background-color: #6688d4; */
  white-space: nowrap;
  font-family: sans-serif;
  font-style: italic;
  font-size:70%;
}
/* "Line coverage date bins" leader */
td.subTableHeader
{
  text-align: center;
  padding-bottom: 6px;
  font-family: sans-serif;
  font-weight: bold;
  vertical-align: center;
}

/* All views: header item format */
td.headerItem
{
  text-align: right;
  padding-right: 6px;
  font-family: sans-serif;
  font-weight: bold;
  vertical-align: top;
  white-space: nowrap;
}

/* All views: header item value format */
td.headerValue
{
  text-align: left;
  color: #284fa8;
  font-family: sans-serif;
  font-weight: bold;
  white-space: nowrap;
}

/* All views: header item coverage table heading */
td.headerCovTableHead
{
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
}

/* All views: header item coverage table entry */
td.headerCovTableEntry
{
  text-align: right;
  color: #284fa8;
  font-family: sans-serif;
  font-weight: bold;
  white-space: nowrap;
  padding-left: 12px;
  padding-right: 4px;
  background-color: #dae7fe;
}

/* All views: header item coverage table entry for high coverage rate */
td.headerCovTableEntryHi
{
  text-align: right;
  color: #000000;
  font-family: sans-serif;
  font-weight: bold;
  white-space: nowrap;
  padding-left: 12px;
  padding-right: 4px;
  background-color: #a7fc9d;
}

/* All views: header item coverage table entry for medium coverage rate */
td.headerCovTableEntryMed
{
  text-align: right;
  color: #000000;
  font-family: sans-serif;
  font-weight: bold;
  white-space: nowrap;
  padding-left: 12px;
  padding-right: 4px;
  background-color: #ffea20;
}

/* All views: header item coverage table entry for ow coverage rate */
td.headerCovTableEntryLo
{
  text-align: right;
  color: #000000;
  font-family: sans-serif;
  font-weight: bold;
  white-space: nowrap;
  padding-left: 12px;
  padding-right: 4px;
  background-color: #ff0000;
}

/* All views: header legend value for legend entry */
td.headerValueLeg
{
  text-align: left;
  color: #000000;
  font-family: sans-serif;
  font-size: 80%;
  white-space: nowrap;
  padding-top: 4px;
}

/* All views: color of horizontal ruler */
td.ruler
{
  background-color: #6688d4;
}

/* All views: version string format */
td.versionInfo
{
  text-align: center;
  padding-top: 2px;
  font-family: sans-serif;
  font-style: italic;
}

/* Directory view/File view (all)/Test case descriptions:
   table headline format */
td.tableHead
{
  text-align: center;
  color: #ffffff;
  background-color: #6688d4;
  font-family: sans-serif;
  font-size: 120%;
  font-weight: bold;
  white-space: nowrap;
  padding-left: 4px;
  padding-right: 4px;
}

span.tableHeadSort
{
  padding-right: 4px;
}

/* Directory view/File view (all): filename entry format */
td.coverFile
{
  text-align: left;
  padding-left: 10px;
  padding-right: 20px;
  color: #284fa8;
  background-color: #dae7fe;
  font-family: monospace;
}

/* Directory view/File view (all): directory name entry format */
td.coverDirectory
{
  text-align: left;
  padding-left: 10px;
  padding-right: 20px;
  color: #284fa8;
  background-color: #b8d0ff;
  font-family: monospace;
}

/* Directory view/File view (all): filename entry format */
td.overallOwner
{
  text-align: center;
  font-weight: bold;
  font-family: sans-serif;
  background-color: #dae7fe;
  padding-right: 10px;
  padding-left: 10px;
}

/* Directory view/File view (all): filename entry format */
td.ownerName
{
  text-align: right;
  font-style: italic;
  font-family: sans-serif;
  background-color: #E5DBDB;
  padding-right: 10px;
  padding-left: 20px;
}

/* Directory view/File view (all): bar-graph entry format*/
td.coverBar
{
  padding-left: 10px;
  padding-right: 10px;
  background-color: #dae7fe;
}

/* Directory view/File view (all): bar-graph entry format*/
td.owner_coverBar
{
  padding-left: 10px;
  padding-right: 10px;
  background-color: #E5DBDB;
}

/* Directory view/File view (all): bar-graph outline color */
td.coverBarOutline
{
  background-color: #000000;
}

/* Directory view/File view (all): percentage entry for files with
   high coverage rate */
td.coverPerHi
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #a7fc9d;
  font-weight: bold;
  font-family: sans-serif;
}

/* 'owner' entry:  slightly lighter color than 'coverPerHi' */
td.owner_coverPerHi
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #82E0AA;
  font-weight: bold;
  font-family: sans-serif;
}

/* Directory view/File view (all): line count entry */
td.coverNumDflt
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #dae7fe;
  white-space: nowrap;
  font-family: sans-serif;
}

/* td background color and font for the 'owner' section of the table */
td.ownerTla
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #E5DBDB;
  white-space: nowrap;
  font-family: sans-serif;
  font-style: italic;
}

/* Directory view/File view (all): line count entry for files with
   high coverage rate */
td.coverNumHi
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #a7fc9d;
  white-space: nowrap;
  font-family: sans-serif;
}

td.owner_coverNumHi
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #82E0AA;
  white-space: nowrap;
  font-family: sans-serif;
}

/* Directory view/File view (all): percentage entry for files with
   medium coverage rate */
td.coverPerMed
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #ffea20;
  font-weight: bold;
  font-family: sans-serif;
}

td.owner_coverPerMed
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #F9E79F;
  font-weight: bold;
  font-family: sans-serif;
}

/* Directory view/File view (all): line count entry for files with
   medium coverage rate */
td.coverNumMed
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #ffea20;
  white-space: nowrap;
  font-family: sans-serif;
}

td.owner_coverNumMed
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #F9E79F;
  white-space: nowrap;
  font-family: sans-serif;
}

/* Directory view/File view (all): percentage entry for files with
   low coverage rate */
td.coverPerLo
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #ff0000;
  font-weight: bold;
  font-family: sans-serif;
}

td.owner_coverPerLo
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #EC7063;
  font-weight: bold;
  font-family: sans-serif;
}

/* Directory view/File view (all): line count entry for files with
   low coverage rate */
td.coverNumLo
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #ff0000;
  white-space: nowrap;
  font-family: sans-serif;
}

td.owner_coverNumLo
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #EC7063;
  white-space: nowrap;
  font-family: sans-serif;
}

/* File view (all): "show/hide details" link format */
a.detail:link
{
  color: #b8d0ff;
  font-size:80%;
}

/* File view (all): "show/hide details" link - visited format */
a.detail:visited
{
  color: #b8d0ff;
  font-size:80%;
}

/* File view (all): "show/hide details" link - activated format */
a.detail:active
{
  color: #ffffff;
  font-size:80%;
}

/* File view (detail): test name entry */
td.testName
{
  text-align: right;
  padding-right: 10px;
  background-color: #dae7fe;
  font-family: sans-serif;
}

/* File view (detail): test percentage entry */
td.testPer
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #dae7fe;
  font-family: sans-serif;
}

/* File view (detail): test lines count entry */
td.testNum
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #dae7fe;
  font-family: sans-serif;
}

/* Test case descriptions: test name format*/
dt
{
  font-family: sans-serif;
  font-weight: bold;
}

/* Test case descriptions: description table body */
td.testDescription
{
  padding-top: 10px;
  padding-left: 30px;
  padding-bottom: 10px;
  padding-right: 30px;
  background-color: #dae7fe;
}

/* Source code view: function entry */
td.coverFn
{
  text-align: left;
  padding-left: 10px;
  padding-right: 20px;
  color: #284fa8;
  background-color: #dae7fe;
  font-family: monospace;
}

/* Source code view: function entry zero count*/
td.coverFnLo
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #ff0000;
  font-weight: bold;
  font-family: sans-serif;
}

/* Source code view: function entry nonzero count*/
td.coverFnHi
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #dae7fe;
  font-weight: bold;
  font-family: sans-serif;
}

td.coverFnAlias
{
  text-align: right;
  padding-left: 10px;
  padding-right: 20px;
  color: #284fa8;
  /* make this a slightly different color than the leader - otherwise,
     otherwise the alias is hard to distinguish in the table */
  background-color: #E5DBDB; /* very light pale grey/blue */
  font-family: monospace;
}

/* Source code view: function entry zero count*/
td.coverFnAliasLo
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #EC7063; /* lighter red */
  font-family: sans-serif;
}

/* Source code view: function entry nonzero count*/
td.coverFnAliasHi
{
  text-align: right;
  padding-left: 10px;
  padding-right: 10px;
  background-color: #dae7fe;
  font-weight: bold;
  font-family: sans-serif;
}

/* Source code view: source code format */
pre.source
{
  font-family: monospace;
  white-space: pre;
  margin-top: 2px;
}

/* elided/removed code */
span.elidedSource
{
  font-family: sans-serif;
  /*font-size: 8pt; */
  font-style: italic;
  background-color: lightgrey;
}

/* Source code view: line number format */
span.lineNum
{
  background-color: #efe383;
}

/* Source code view: line number format when there are deleted
   lines in the corresponding location */
span.lineNumWithDelete
{
  foreground-color: #efe383;
  background-color: lightgrey;
}

/* Source code view: format for Cov legend */
span.coverLegendCov
{
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 2px;
  background-color: #cad7fe;
}

/* Source code view: format for NoCov legend */
span.coverLegendNoCov
{
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 2px;
  background-color: #ff6230;
}

/* Source code view: format for the source code heading line */
pre.sourceHeading
{
  white-space: pre;
  font-family: monospace;
  font-weight: bold;
  margin: 0px;
}

/* All views: header legend value for low rate */
td.headerValueLegL
{
  font-family: sans-serif;
  text-align: center;
  white-space: nowrap;
  padding-left: 4px;
  padding-right: 2px;
  background-color: #ff0000;
  font-size: 80%;
}

/* All views: header legend value for med rate */
td.headerValueLegM
{
  font-family: sans-serif;
  text-align: center;
  white-space: nowrap;
  padding-left: 2px;
  padding-right: 2px;
  background-color: #ffea20;
  font-size: 80%;
}

/* All views: header legend value for hi rate */
td.headerValueLegH
{
  font-family: sans-serif;
  text-align: center;
  white-space: nowrap;
  padding-left: 2px;
  padding-right: 4px;
  background-color: #a7fc9d;
  font-size: 80%;
}

/* All views except source code view: legend format for low coverage */
span.coverLegendCovLo
{
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 2px;
  background-color: #ff0000;
}

/* All views except source code view: legend format for med coverage */
span.coverLegendCovMed
{
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 2px;
  background-color: #ffea20;
}

/* All views except source code view: legend format for hi coverage */
span.coverLegendCovHi
{
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 2px;
  background-color: #a7fc9d;
}

a.branchTla:link
{
  color: #000000;
}

a.branchTla:visited
{
  color: #000000;
}

a.mcdcTla:link
{
  color: #000000;
}

a.mcdcTla:visited
{
  color: #000000;
}

/* Source code view/table entry background: format for lines classified as "Uncovered New Code (+ => 0):
Newly added code is not tested" */
td.tlaUNC
{
  text-align: right;
  background-color: #FF6230;
}
td.tlaBgUNC {
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Uncovered New Code (+ => 0):
Newly added code is not tested" */
span.tlaUNC
{
  text-align: left;
  background-color: #FF6230;
}
span.tlaBgUNC {
  background-color: #FF6230;
}
a.tlaBgUNC {
  background-color: #FF6230;
  color: #000000;
}

td.headerCovTableHeadUNC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Lost Baseline Coverage (1 => 0):
Unchanged code is no longer tested" */
td.tlaLBC
{
  text-align: right;
  background-color: #FF6230;
}
td.tlaBgLBC {
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Lost Baseline Coverage (1 => 0):
Unchanged code is no longer tested" */
span.tlaLBC
{
  text-align: left;
  background-color: #FF6230;
}
span.tlaBgLBC {
  background-color: #FF6230;
}
a.tlaBgLBC {
  background-color: #FF6230;
  color: #000000;
}

td.headerCovTableHeadLBC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Uncovered Included Code (# => 0):
Previously unused code is untested" */
td.tlaUIC
{
  text-align: right;
  background-color: #FF6230;
}
td.tlaBgUIC {
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Uncovered Included Code (# => 0):
Previously unused code is untested" */
span.tlaUIC
{
  text-align: left;
  background-color: #FF6230;
}
span.tlaBgUIC {
  background-color: #FF6230;
}
a.tlaBgUIC {
  background-color: #FF6230;
  color: #000000;
}

td.headerCovTableHeadUIC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Uncovered Baseline Code (0 => 0):
Unchanged code was untested before, is untested now" */
td.tlaUBC
{
  text-align: right;
  background-color: #FF6230;
}
td.tlaBgUBC {
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Uncovered Baseline Code (0 => 0):
Unchanged code was untested before, is untested now" */
span.tlaUBC
{
  text-align: left;
  background-color: #FF6230;
}
span.tlaBgUBC {
  background-color: #FF6230;
}
a.tlaBgUBC {
  background-color: #FF6230;
  color: #000000;
}

td.headerCovTableHeadUBC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FF6230;
}

/* Source code view/table entry background: format for lines classified as "Gained Baseline Coverage (0 => 1):
Unchanged code is tested now" */
td.tlaGBC
{
  text-align: right;
  background-color: #CAD7FE;
}
td.tlaBgGBC {
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Gained Baseline Coverage (0 => 1):
Unchanged code is tested now" */
span.tlaGBC
{
  text-align: left;
  background-color: #CAD7FE;
}
span.tlaBgGBC {
  background-color: #CAD7FE;
}
a.tlaBgGBC {
  background-color: #CAD7FE;
  color: #000000;
}

td.headerCovTableHeadGBC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Gained Included Coverage (# => 1):
Previously unused code is tested now" */
td.tlaGIC
{
  text-align: right;
  background-color: #CAD7FE;
}
td.tlaBgGIC {
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Gained Included Coverage (# => 1):
Previously unused code is tested now" */
span.tlaGIC
{
  text-align: left;
  background-color: #CAD7FE;
}
span.tlaBgGIC {
  background-color: #CAD7FE;
}
a.tlaBgGIC {
  background-color: #CAD7FE;
  color: #000000;
}

td.headerCovTableHeadGIC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Gained New Coverage (+ => 1):
Newly added code is tested" */
td.tlaGNC
{
  text-align: right;
  background-color: #CAD7FE;
}
td.tlaBgGNC {
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Gained New Coverage (+ => 1):
Newly added code is tested" */
span.tlaGNC
{
  text-align: left;
  background-color: #CAD7FE;
}
span.tlaBgGNC {
  background-color: #CAD7FE;
}
a.tlaBgGNC {
  background-color: #CAD7FE;
  color: #000000;
}

td.headerCovTableHeadGNC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Covered Baseline Code (1 => 1):
Unchanged code was tested before and is still tested" */
td.tlaCBC
{
  text-align: right;
  background-color: #CAD7FE;
}
td.tlaBgCBC {
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Covered Baseline Code (1 => 1):
Unchanged code was tested before and is still tested" */
span.tlaCBC
{
  text-align: left;
  background-color: #CAD7FE;
}
span.tlaBgCBC {
  background-color: #CAD7FE;
}
a.tlaBgCBC {
  background-color: #CAD7FE;
  color: #000000;
}

td.headerCovTableHeadCBC {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #CAD7FE;
}

/* Source code view/table entry background: format for lines classified as "Excluded Uncovered Baseline (0 => #):
Previously untested code is unused now" */
td.tlaEUB
{
  text-align: right;
  background-color: #FFFFFF;
}
td.tlaBgEUB {
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Excluded Uncovered Baseline (0 => #):
Previously untested code is unused now" */
span.tlaEUB
{
  text-align: left;
  background-color: #FFFFFF;
}
span.tlaBgEUB {
  background-color: #FFFFFF;
}
a.tlaBgEUB {
  background-color: #FFFFFF;
  color: #000000;
}

td.headerCovTableHeadEUB {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Excluded Covered Baseline (1 => #):
Previously tested code is unused now" */
td.tlaECB
{
  text-align: right;
  background-color: #FFFFFF;
}
td.tlaBgECB {
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Excluded Covered Baseline (1 => #):
Previously tested code is unused now" */
span.tlaECB
{
  text-align: left;
  background-color: #FFFFFF;
}
span.tlaBgECB {
  background-color: #FFFFFF;
}
a.tlaBgECB {
  background-color: #FFFFFF;
  color: #000000;
}

td.headerCovTableHeadECB {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Deleted Uncovered Baseline (0 => -):
Previously untested code has been deleted" */
td.tlaDUB
{
  text-align: right;
  background-color: #FFFFFF;
}
td.tlaBgDUB {
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Deleted Uncovered Baseline (0 => -):
Previously untested code has been deleted" */
span.tlaDUB
{
  text-align: left;
  background-color: #FFFFFF;
}
span.tlaBgDUB {
  background-color: #FFFFFF;
}
a.tlaBgDUB {
  background-color: #FFFFFF;
  color: #000000;
}

td.headerCovTableHeadDUB {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Deleted Covered Baseline (1 => -):
Previously tested code has been deleted" */
td.tlaDCB
{
  text-align: right;
  background-color: #FFFFFF;
}
td.tlaBgDCB {
  background-color: #FFFFFF;
}

/* Source code view/table entry background: format for lines classified as "Deleted Covered Baseline (1 => -):
Previously tested code has been deleted" */
span.tlaDCB
{
  text-align: left;
  background-color: #FFFFFF;
}
span.tlaBgDCB {
  background-color: #FFFFFF;
}
a.tlaBgDCB {
  background-color: #FFFFFF;
  color: #000000;
}

td.headerCovTableHeadDCB {
  text-align: center;
  padding-right: 6px;
  padding-left: 6px;
  padding-bottom: 0px;
  font-family: sans-serif;
  white-space: nowrap;
  background-color: #FFFFFF;
}

/* Source code view: format for date/owner bin that is not hit */
span.missBins
{
  background-color: #ff0000 /* red */
}
