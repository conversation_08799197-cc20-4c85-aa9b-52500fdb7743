PODS:
  - app_settings (5.1.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_udid (0.0.1):
    - Flutter
    - SAMKeychain
  - flutter_video_info (1.1.0):
    - Flutter
  - flutter_vlc_player (3.0.3):
    - Flutter
    - MobileVLCKit (~> 3.6.0b12)
  - gallery_saver (0.0.1):
    - Flutter
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - image_save (0.0.1):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - MobileVLCKit (3.6.1b1)
  - MSAL (1.2.5):
    - MSAL/app-lib (= 1.2.5)
  - MSAL/app-lib (1.2.5)
  - msal_flutter (1.0.2):
    - Flutter
    - MSAL (~> 1.2.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SAMKeychain (1.5.3)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.0):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - share (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player (0.0.1):
    - Flutter
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_udid (from `.symlinks/plugins/flutter_udid/ios`)
  - flutter_video_info (from `.symlinks/plugins/flutter_video_info/ios`)
  - flutter_vlc_player (from `.symlinks/plugins/flutter_vlc_player/ios`)
  - gallery_saver (from `.symlinks/plugins/gallery_saver/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - image_save (from `.symlinks/plugins/image_save/ios`)
  - msal_flutter (from `.symlinks/plugins/msal_flutter/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player (from `.symlinks/plugins/video_player/ios`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - libwebp
    - MobileVLCKit
    - MSAL
    - SAMKeychain
    - SDWebImage
    - Sentry
    - SwiftyGif

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_udid:
    :path: ".symlinks/plugins/flutter_udid/ios"
  flutter_video_info:
    :path: ".symlinks/plugins/flutter_video_info/ios"
  flutter_vlc_player:
    :path: ".symlinks/plugins/flutter_vlc_player/ios"
  gallery_saver:
    :path: ".symlinks/plugins/gallery_saver/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  image_save:
    :path: ".symlinks/plugins/image_save/ios"
  msal_flutter:
    :path: ".symlinks/plugins/msal_flutter/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player:
    :path: ".symlinks/plugins/video_player/ios"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"

SPEC CHECKSUMS:
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  connectivity_plus: ddd7f30999e1faaef5967c23d5b6d503d10434db
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  emoji_picker_flutter: fe2e6151c5b548e975d546e6eeb567daf0962a58
  file_picker: 09aa5ec1ab24135ccd7a1621c46c84134bfd6655
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  flutter_udid: a2482c67a61b9c806ef59dd82ed8d007f1b7ac04
  flutter_video_info: a932e1b0da369bba95c3a14b7f61d5f8b0d03280
  flutter_vlc_player: 68553dec38187c14a7800e6d27a30bf9b25b53bc
  gallery_saver: 9fc173c9f4fcc48af53b2a9ebea1b643255be542
  image_gallery_saver: cb43cc43141711190510e92c460eb1655cd343cb
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  image_save: 66ab41fa2c60bc9569e55563ea287cced7b79fd1
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  MobileVLCKit: 2d9c7c373393ae43086aeeff890bf0b1afc15c5c
  MSAL: 5149daaa19228c2c27d81987634da15b50981cef
  msal_flutter: 6affbfc5214d05ab2654adadd0c6f11714c57ddf
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  pasteboard: 982969ebaa7c78af3e6cc7761e8f5e77565d9ce0
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  SAMKeychain: 483e1c9f32984d50ca961e26818a534283b4cd5c
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 187f9b6b06f00f36b4930ec7ea9f34c254095d15
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  share_plus: 8875f4f2500512ea181eef553c3e27dba5135aad
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player: 9cc823b1d9da7e8427ee591e8438bfbcde500e6e
  video_thumbnail: c4e2a3c539e247d4de13cd545344fd2d26ffafd1

PODFILE CHECKSUM: 7be2f5f74864d463a8ad433546ed1de7e0f29aef

COCOAPODS: 1.13.0
