/*
 * Created Date: 4/12/2023 16:21:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:29:42
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:example/constants/url/url.constants.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:injectable/injectable.dart';

@module
abstract class UrlModule {

  @singleton
  @Named('kAuthUrl')
  String get kAuthUrl => "${Constants.baseUrl}${UrlConstants.kAuthAPI}";

  @Singleton(env: kFlavorDevs)
  @Named('testByEnv')
  String get testByEnv => "TestByEnv";
}
