/*
 * Created Date: 5/12/2023 09:47:29
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 8th January 2024 16:38:44
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/configs/app/app.configs.dart';
import 'package:gp_core_v2/base/configs/bloc/talker_observer.dart';
import 'package:gp_core_v2/base/configs/log/log.configs.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:injectable/injectable.dart';
import 'package:talker/talker.dart';

import '../../presentation/test/bloc/test_event.dart';

@module
abstract class AppModule {
  @preResolve
  Future<bool> get init async {
    _initIgnoreUsecases();

    return Future.value(true);
  }

  @Singleton(env: kFlavorDevs)
  Talker get talker => Talker(
        settings: TalkerSettings(
          enabled: LogConfig.kEnableTalkerLog,
          maxHistoryItems: 100,
          useHistory: true,
          useConsoleLogs: true,
        ),
        logger: TalkerLogger(),
        observer: GPTalkerObserver(),
      );

  void _initIgnoreUsecases() {
    final appUseCaseManagement = GetIt.I.get<AppUseCaseManagement>();
    final eventsToAdd = [TestEvent];

    // Only add events that are not already in the list to prevent duplicates
    for (final event in eventsToAdd) {
      if (!appUseCaseManagement.ignoreEvents.contains(event)) {
        appUseCaseManagement.ignoreEvents.add(event);
      }
    }
  }
}
