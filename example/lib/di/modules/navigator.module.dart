/*
 * Created Date: 5/01/2024 13:46:14
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 13th March 2025 09:48:51
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';

@module
abstract class NavigatorModule {
  @singleton
  @Named('kAppNavigatorKey')
  GlobalKey<NavigatorState> get kAppNavigatorKey =>
      GlobalKey(debugLabel: 'kAppNavigatorKey');
}
