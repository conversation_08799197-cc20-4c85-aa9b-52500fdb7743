/*
 * Created Date: 4/11/2023 16:14:8
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 10:59:11
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/constants/di.constants.dart';

import 'app.component.config.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

final GetIt getIt = GetIt.instance;

@InjectableInit(
  externalPackageModulesBefore: [
    ExternalModule(GpCoreV2PackageModule),
  ],
)
Future configureInjection() => getIt.init(
      environmentFilter: NoEnvOrContainsAny(
        {
          kFlavorSaasDevelopment.name,
        },
      ),
    );
