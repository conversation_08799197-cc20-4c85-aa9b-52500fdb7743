// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dio/dio.dart' as _i361;
import 'package:example/data/data_source/remote/auth.service.dart' as _i904;
import 'package:example/data/repository/auth_repo_impl.dart' as _i1004;
import 'package:example/di/modules/app.module.dart' as _i238;
import 'package:example/di/modules/navigator.module.dart' as _i117;
import 'package:example/di/modules/url.module.dart' as _i684;
import 'package:example/domain/repository/auth_repo.dart' as _i858;
import 'package:example/domain/usecase/auth_check_mail.usecase.dart' as _i651;
import 'package:example/mapper/entity/mapper.dart' as _i357;
import 'package:example/navigator/app_navigator/app_navigator_getx_route_impl.dart'
    as _i669;
import 'package:example/navigator/app_navigator/app_navigator_go_route_impl.dart'
    as _i18;
import 'package:example/navigator/app_navigator/app_navigator_impl.dart'
    as _i433;
import 'package:example/navigator/model/example_app_info_route.dart' as _i907;
import 'package:example/presentation/test/bloc/test_bloc.dart' as _i979;
import 'package:flutter/material.dart' as _i409;
import 'package:get_it/get_it.dart' as _i174;
import 'package:gp_core_v2/base/navigator/navigator.dart' as _i994;
import 'package:gp_core_v2/gp_core_v2.dart' as _i250;
import 'package:injectable/injectable.dart' as _i526;
import 'package:talker/talker.dart' as _i993;

const String _SaasDevelop = 'SaasDevelop';
const String _SaasQa = 'SaasQa';
const String _OnPremiseDevelop = 'OnPremiseDevelop';
const String _OnPremiseQa = 'OnPremiseQa';

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    await _i250.GpCoreV2PackageModule().init(gh);
    final appModule = _$AppModule();
    final navigatorModule = _$NavigatorModule();
    final urlModule = _$UrlModule();
    await gh.factoryAsync<bool>(
      () => appModule.init,
      preResolve: true,
    );
    gh.singleton<_i409.GlobalKey<_i409.NavigatorState>>(
      () => navigatorModule.kAppNavigatorKey,
      instanceName: 'kAppNavigatorKey',
    );
    gh.singleton<String>(
      () => urlModule.kAuthUrl,
      instanceName: 'kAuthUrl',
    );
    gh.singleton<String>(
      () => urlModule.testByEnv,
      instanceName: 'testByEnv',
      registerFor: {
        _SaasDevelop,
        _SaasQa,
        _OnPremiseDevelop,
        _OnPremiseQa,
      },
    );
    gh.singleton<_i993.Talker>(
      () => appModule.talker,
      registerFor: {
        _SaasDevelop,
        _SaasQa,
        _OnPremiseDevelop,
        _OnPremiseQa,
      },
    );
    gh.factory<_i979.TestBloc>(() =>
        _i979.TestBloc(authCheckMailUseCase: gh<_i651.AuthCheckMailUseCase>()));
    gh.lazySingleton<_i904.AuthService>(
      () => _i904.AuthService(
        gh<_i361.Dio>(instanceName: 'kDio'),
        baseUrl: gh<String>(instanceName: 'kAuthUrl'),
      ),
      instanceName: 'kAuthService',
    );
    gh.lazySingleton<_i858.AuthRepository>(
      () => _i1004.AuthRepositoryImpl(
          gh<_i904.AuthService>(instanceName: 'kAuthService')),
      instanceName: 'kAuthRepository',
    );
    gh.singleton<_i357.GPMapper>(
      () => _i357.GPMapper(),
      instanceName: 'kGPMapper',
    );
    gh.factory<_i651.AuthCheckMailUseCase>(() => _i651.AuthCheckMailUseCase(
        gh<_i858.AuthRepository>(instanceName: 'kAuthRepository')));
    return this;
  }

// initializes the registration of NavigateWithGoRouter-scope dependencies inside of GetIt
  _i174.GetIt initNavigateWithGoRouterScope({_i174.ScopeDisposeFunc? dispose}) {
    return _i526.GetItHelper(this).initScope(
      'NavigateWithGoRouter',
      dispose: dispose,
      init: (_i526.GetItHelper gh) {
        gh.lazySingleton<_i994.GPAppNavigator<_i907.ExampleAppInfoRoute>>(
          () => _i18.ExampleGoRouteNavigatorImpl(
              gh<_i409.GlobalKey<_i409.NavigatorState>>(
                  instanceName: 'kAppNavigatorKey')),
          instanceName: 'kAppNavigator',
        );
      },
    );
  }

// initializes the registration of NavigateWithFlutter-scope dependencies inside of GetIt
  _i174.GetIt initNavigateWithFlutterScope({_i174.ScopeDisposeFunc? dispose}) {
    return _i526.GetItHelper(this).initScope(
      'NavigateWithFlutter',
      dispose: dispose,
      init: (_i526.GetItHelper gh) {
        gh.lazySingleton<_i994.GPAppNavigator<_i907.ExampleAppInfoRoute>>(
          () => _i433.ExampleNavigatorImpl(
              gh<_i409.GlobalKey<_i409.NavigatorState>>(
                  instanceName: 'kAppNavigatorKey')),
          instanceName: 'kAppNavigator',
        );
      },
    );
  }

// initializes the registration of NavigateWithGetX-scope dependencies inside of GetIt
  _i174.GetIt initNavigateWithGetXScope({_i174.ScopeDisposeFunc? dispose}) {
    return _i526.GetItHelper(this).initScope(
      'NavigateWithGetX',
      dispose: dispose,
      init: (_i526.GetItHelper gh) {
        gh.lazySingleton<_i994.GPAppNavigator<_i907.ExampleAppInfoRoute>>(
          () => _i669.ExampleGetXRouteNavigatorImpl(
              gh<_i409.GlobalKey<_i409.NavigatorState>>(
                  instanceName: 'kAppNavigatorKey')),
          instanceName: 'kAppNavigator',
        );
      },
    );
  }
}

class _$AppModule extends _i238.AppModule {}

class _$NavigatorModule extends _i117.NavigatorModule {}

class _$UrlModule extends _i684.UrlModule {}
