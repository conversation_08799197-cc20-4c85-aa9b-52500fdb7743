import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:example/domain/entity/auth.entity.dart';

import 'auth_entity_mapper.auto_mappr.dart';

@AutoMappr([
  MapType<AuthCheckMailResponse, AuthCheckMailEntity>(
    fields: [
      Field('salt', ignore: true),
    ],
  ),
])
class AuthCheckMailMapper extends $AuthCheckMailMapper {
  const AuthCheckMailMapper();
}