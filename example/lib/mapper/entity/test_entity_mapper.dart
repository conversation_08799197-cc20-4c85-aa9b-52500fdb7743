import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';

import '../../domain/entity/test.entity.dart';
import 'test_entity_mapper.auto_mappr.dart';

@AutoMappr([
  MapType<UserDto, User>(),
])

/// {@template package:example/mapper/entity/test_entity_mapper.dart}
/// Available mappings:
/// - `UserDto` → `User`.
/// {@endtemplate}
class TestMapper extends $TestMapper {
  const TestMapper();
}
