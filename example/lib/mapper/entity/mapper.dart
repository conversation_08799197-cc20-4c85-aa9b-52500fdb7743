/*
 * Created Date: 5/12/2023 16:01:6
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 8th January 2024 16:38:49
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:injectable/injectable.dart';

import 'auth/auth_entity_mapper.dart';
import 'mapper.auto_mappr.dart';
import 'test_entity_mapper.dart';

@Singleton(order: DiConstants.kDomainMapperOrder)
@Named('kGPMapper')
@AutoMappr(
  [],
  delegates: [
    AuthCheckMailMapper(),
    TestMapper(),
  ],
)

/// {@template package:example/mapper/entity/mapper.dart}
/// Available mappings:
/// {@endtemplate}
///
/// Used delegates: [_i1.AuthCheckMailMapper], [_i2.TestMapper]
final class GPMapper extends $GPMapper {}
