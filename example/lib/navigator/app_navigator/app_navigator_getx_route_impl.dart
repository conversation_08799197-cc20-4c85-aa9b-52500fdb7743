import 'package:flutter/material.dart' as material;
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/configs/log/log.configs.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/navigator/navigator.dart';
import 'package:injectable/injectable.dart';

import '../mapper/example_getx_route_info_mapper.dart';
import '../model/example_app_info_route.dart';

@LazySingleton(
  as: GPAppNavigator,
  order: DiConstants.kDomainPresentationOrder,
  scope: 'NavigateWithGetX',
)
@Named('kAppNavigator')
// Apply default Flutter navigation
final class ExampleGetXRouteNavigatorImpl
    extends GPAppNavigator<ExampleAppInfoRoute> {
  ExampleGetXRouteNavigatorImpl(
    @Named('kAppNavigatorKey') super.navigatorKey,
  );

  @override
  GPAppPopupNavigator get appPopupNavigator => GPAppPopupNavigatorImpl(this);

  @override
  GPAppSnackbarNavigator get appSnackbarNavigator =>
      GPAppGetXSnackbarNavigatorImpl(this);

  @override
  GPBaseRouteInfoMapper<material.RouteSettings, ExampleAppInfoRoute>
      get routeInfoMapper => AppGetXRouteInfoMapper();

  @override
  Object? parseArguments<T extends Object?>(
    ExampleAppInfoRoute appRouteInfo,
    Object? arguments,
  ) {
    dynamic routeData = routeInfoMapper.map(appRouteInfo);

    try {
      // null safety cast
      arguments ??= arguments ?? routeData.arguments;
    } catch (ex, stackTrace) {
      if (LogConfig.kLogOnNavigatorPushError) {
        logDebug('$runtimeType push with arguments error $ex, \n $stackTrace');
      }
    }

    return arguments;
  }

  @override
  bool canPop(material.BuildContext context) {
    // GetX check canPop when `Get.back()`
    return true;
  }

  @override
  void pop<T extends Object?>(
    material.BuildContext context, {
    T? result,
    bool useRootNavigator = false,
  }) {
    Get.back(result: result, canPop: true);
  }

  @override
  void popUntil<T extends Object?>(
    material.BuildContext context,
    ExampleAppInfoRoute appRouteInfo,
  ) {
    Get.until((route) {
      return route.settings.name == routeInfoMapper.map(appRouteInfo).name;
    });
  }

  @override
  Future<T?> popAndPush<T extends Object?, R extends Object?>(
    material.BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  }) async {
    final routeSettings = routeInfoMapper.map(appRouteInfo);
    final location = routeSettings.name ?? '';
    assert(location.isNotEmpty == true);

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    return Get.offAndToNamed(
      location,
      arguments: arguments,
    );
  }

  @override
  Future<T?> push<T extends Object?, R extends Object?>(
    material.BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  }) async {
    final routeSettings = routeInfoMapper.map(appRouteInfo);
    final location = routeSettings.name ?? '';
    assert(location.isNotEmpty == true);

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    return Get.toNamed(
      location,
      arguments: arguments,
    );
  }

  @override
  Future<T?> replace<T extends Object?>(
    material.BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    Object? arguments,
  }) async {
    final routeSettings = routeInfoMapper.map(appRouteInfo);
    final location = routeSettings.name ?? '';
    assert(location.isNotEmpty == true);

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    Get.offNamed(location, arguments: arguments);

    return null;
  }
}
