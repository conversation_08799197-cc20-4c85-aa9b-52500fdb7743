import 'package:flutter/material.dart';
import 'package:gp_core/utils/log.dart';
import 'package:gp_core_v2/base/configs/log/log.configs.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/navigator/navigator.dart';
import 'package:injectable/injectable.dart';

import '../mapper/example_app_navigator_route_info_mapper.dart';
import '../model/example_app_info_route.dart';

// Apply default Flutter navigation
@LazySingleton(
  as: GPAppNavigator,
  order: DiConstants.kDomainPresentationOrder,
  scope: 'NavigateWithFlutter',
)
@Named('kAppNavigator')
final class ExampleNavigatorImpl extends GPAppNavigator<ExampleAppInfoRoute> {
  ExampleNavigatorImpl(
    @Named('kAppNavigatorKey') super.navigatorKey,
  );

  @override
  GPAppPopupNavigator get appPopupNavigator => GPAppPopupNavigatorImpl(this);

  @override
  GPAppSnackbarNavigator get appSnackbarNavigator =>
      GPAppSnackbarNavigatorImpl(this);

  @override
  GPBaseRouteInfoMapper<RouteSettings, ExampleAppInfoRoute>
      get routeInfoMapper => AppNavigatorRouteInfoMapper();

  @override
  Object? parseArguments<T extends Object?>(
    ExampleAppInfoRoute appRouteInfo,
    Object? arguments,
  ) {
    dynamic routeData = routeInfoMapper.map(appRouteInfo);

    try {
      // null safety cast
      arguments ??= arguments ?? routeData.arguments;
    } catch (ex, stackTrace) {
      if (LogConfig.kLogOnNavigatorPushError) {
        logDebug('$runtimeType push with arguments error $ex, \n $stackTrace');
      }
    }

    return arguments;
  }

  @override
  bool canPop(BuildContext context) {
    return Navigator.of(context).canPop();
  }

  @override
  void pop<T extends Object?>(
    BuildContext context, {
    T? result,
    bool useRootNavigator = false,
  }) {
    Navigator.of(context, rootNavigator: useRootNavigator).pop(result);
  }

  @override
  void popUntil<T extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo,
  ) {
    Navigator.of(context).popUntil((route) {
      return route.settings.name == routeInfoMapper.map(appRouteInfo).name;
    });
  }

  @override
  Future<T?> popAndPush<T extends Object?, R extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  }) {
    if (canPop(context)) {
      pop(context, result: result, useRootNavigator: useRootNavigator);
    }

    final routeSettings = routeInfoMapper.map(appRouteInfo);
    final location = routeSettings.name ?? '';
    assert(location.isNotEmpty == true);

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    return Navigator.of(context).pushNamed(
      location,
      arguments: arguments,
    );
  }

  @override
  Future<T?> push<T extends Object?, R extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  }) async {
    final routeSettings = routeInfoMapper.map(appRouteInfo);
    final location = routeSettings.name ?? '';
    assert(location.isNotEmpty == true);

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    return Navigator.of(context).pushNamed(
      location,
      arguments: arguments,
    );
  }

  @override
  Future<T?> replace<T extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    Object? arguments,
  }) {
    final routeSettings = routeInfoMapper.map(appRouteInfo);
    final location = routeSettings.name ?? '';
    assert(location.isNotEmpty == true);

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    return Navigator.of(context).pushReplacementNamed(
      location,
      arguments: arguments,
    );
  }
}
