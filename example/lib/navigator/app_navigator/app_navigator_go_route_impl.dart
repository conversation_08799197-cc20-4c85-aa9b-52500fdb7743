import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:gp_core/utils/log.dart';
import 'package:gp_core_v2/base/configs/log/log.configs.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/navigator/navigator.dart';
import 'package:injectable/injectable.dart';

import '../mapper/example_go_route_info_mapper.dart';
import '../model/example_app_info_route.dart';

// Apply default Flutter navigation
@LazySingleton(
  as: GPAppNavigator,
  order: DiConstants.kDomainPresentationOrder,
  scope: 'NavigateWithGoRouter',
)
@Named('kAppNavigator')
final class ExampleGoRouteNavigatorImpl
    extends GPAppNavigator<ExampleAppInfoRoute> {
  ExampleGoRouteNavigatorImpl(
    @Named('kAppNavigatorKey') super.navigatorKey,
  );

  @override
  GPAppPopupNavigator get appPopupNavigator => GPAppPopupNavigatorImpl(this);

  @override
  GPAppSnackbarNavigator get appSnackbarNavigator =>
      GPAppSnackbarNavigatorImpl(this);

  @override
  GPBaseRouteInfoMapper<GoRouteData, ExampleAppInfoRoute> get routeInfoMapper =>
      AppGoRouteInfoMapper();

  @override
  Object? parseArguments<T extends Object?>(
    ExampleAppInfoRoute appRouteInfo,
    Object? arguments,
  ) {
    dynamic routeData = routeInfoMapper.map(appRouteInfo);

    try {
      // null safety cast
      arguments ??= arguments ?? routeData.$extra;
    } catch (ex, stackTrace) {
      if (LogConfig.kLogOnNavigatorPushError) {
        logDebug('$runtimeType push with arguments error $ex, \n $stackTrace');
      }
    }

    return arguments;
  }

  @override
  bool canPop(BuildContext context) {
    return context.canPop();
  }

  @override
  void pop<T extends Object?>(
    BuildContext context, {
    T? result,
    bool useRootNavigator = false,
  }) {
    context.pop(result);
  }

  @override
  void popUntil<T extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo,
  ) {
    // doNothing
    final String routePath = routeInfoMapper.map(appRouteInfo).location;

    while (GoRouter.of(context)
            .routerDelegate
            .currentConfiguration
            .matches
            .last
            .matchedLocation !=
        routePath) {
      if (!context.canPop()) {
        return;
      }
      context.pop();
    }
  }

  @override
  Future<T?> popAndPush<T extends Object?, R extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  }) async {
    if (canPop(context)) {
      pop(context, result: result, useRootNavigator: useRootNavigator);
    }

    dynamic output;

    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      output = context.push(
        routeInfoMapper.map(appRouteInfo).location,
        extra: arguments,
      );
    });

    return output;
  }

  @override
  Future<T?> push<T extends Object?, R extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  }) async {
    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    return context.push(
      routeInfoMapper.map(appRouteInfo).location,
      extra: arguments,
    );
  }

  @override
  Future<T?> replace<T extends Object?>(
    BuildContext context,
    ExampleAppInfoRoute appRouteInfo, {
    Object? arguments,
  }) async {
    // null safety cast
    arguments ??= parseArguments(appRouteInfo, arguments);

    context.replace(
      routeInfoMapper.map(appRouteInfo).location,
      extra: arguments,
    );

    return null;
  }
}
