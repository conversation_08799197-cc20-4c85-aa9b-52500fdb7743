/*
 * Created Date: Wednesday, 22nd January 2025, 10:35:15
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Saturday, 25th January 2025 11:07:27
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:example/domain/entity/test.entity.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../domain/entity/test/assignee.entity.dart';
import '../../presentation/presentation.dart';
import '../model/example_app_info_route.dart';

part 'go_router.route.g.dart';

/// Route hiển thị màn hình Test,
@TypedGoRoute<ExampleTestRouteData>(
  path: kExampleInitial,
  name: 'ExampleTestPage',
)
class ExampleTestRouteData extends GoRouteData with _$ExampleTestRouteData {
  const ExampleTestRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return TestPage();
  }
}

/// Route hiển thị màn hình đăng nhập,
@TypedGoRoute<ExampleLoginRouteData>(
  path: kExampleLogin,
  name: 'ExampleLoginPage',
)
class ExampleLoginRouteData extends GoRouteData with _$ExampleLoginRouteData {
  const ExampleLoginRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ExampleLoginPage();
  }
}

/// Route hiển thị màn hình Home
@TypedGoRoute<ExampleHomeRouteData>(
  path: kExampleHome,
  name: 'ExampleHomePage',
)
class ExampleHomeRouteData extends GoRouteData with _$ExampleHomeRouteData {
  const ExampleHomeRouteData();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ExampleHomePage();
  }
}

/// Route hiển thị màn hình User
@TypedGoRoute<ExampleUserRouteData>(
  path: kExampleUserDetails,
  name: 'ExampleUserPage',
)
class ExampleUserRouteData extends GoRouteData with _$ExampleUserRouteData {
  const ExampleUserRouteData({
    required this.$extra,
  });

  final User $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ExampleUserPage(user: $extra);
  }
}

/// Route hiển thị màn hình Assignee
@TypedGoRoute<ExampleAssigneeRouteData>(
  path: kExampleAssigneeDetails,
  name: 'ExampleAssigneePage',
)
class ExampleAssigneeRouteData extends GoRouteData
    with _$ExampleAssigneeRouteData {
  const ExampleAssigneeRouteData({
    required this.$extra,
  });

  final AssigneeEntity $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ExampleAssigneePage(assigneeEntity: $extra);
  }
}
