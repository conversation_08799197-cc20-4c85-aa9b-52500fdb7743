// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'go_router.route.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [
      $exampleTestRouteData,
      $exampleLoginRouteData,
      $exampleHomeRouteData,
      $exampleUserRouteData,
      $exampleAssigneeRouteData,
    ];

RouteBase get $exampleTestRouteData => GoRouteData.$route(
      path: '/',
      name: 'ExampleTestPage',
      factory: _$ExampleTestRouteData._fromState,
    );

mixin _$ExampleTestRouteData on GoRouteData {
  static ExampleTestRouteData _fromState(GoRouterState state) =>
      const ExampleTestRouteData();

  @override
  String get location => GoRouteData.$location(
        '/',
      );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $exampleLoginRouteData => GoRouteData.$route(
      path: '/login',
      name: 'ExampleLoginPage',
      factory: _$ExampleLoginRouteData._fromState,
    );

mixin _$ExampleLoginRouteData on GoRouteData {
  static ExampleLoginRouteData _fromState(GoRouterState state) =>
      const ExampleLoginRouteData();

  @override
  String get location => GoRouteData.$location(
        '/login',
      );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $exampleHomeRouteData => GoRouteData.$route(
      path: '/home',
      name: 'ExampleHomePage',
      factory: _$ExampleHomeRouteData._fromState,
    );

mixin _$ExampleHomeRouteData on GoRouteData {
  static ExampleHomeRouteData _fromState(GoRouterState state) =>
      const ExampleHomeRouteData();

  @override
  String get location => GoRouteData.$location(
        '/home',
      );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

RouteBase get $exampleUserRouteData => GoRouteData.$route(
      path: '/user',
      name: 'ExampleUserPage',
      factory: _$ExampleUserRouteData._fromState,
    );

mixin _$ExampleUserRouteData on GoRouteData {
  static ExampleUserRouteData _fromState(GoRouterState state) =>
      ExampleUserRouteData(
        $extra: state.extra as User,
      );

  ExampleUserRouteData get _self => this as ExampleUserRouteData;

  @override
  String get location => GoRouteData.$location(
        '/user',
      );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

RouteBase get $exampleAssigneeRouteData => GoRouteData.$route(
      path: '/assignee',
      name: 'ExampleAssigneePage',
      factory: _$ExampleAssigneeRouteData._fromState,
    );

mixin _$ExampleAssigneeRouteData on GoRouteData {
  static ExampleAssigneeRouteData _fromState(GoRouterState state) =>
      ExampleAssigneeRouteData(
        $extra: state.extra as AssigneeEntity,
      );

  ExampleAssigneeRouteData get _self => this as ExampleAssigneeRouteData;

  @override
  String get location => GoRouteData.$location(
        '/assignee',
      );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}
