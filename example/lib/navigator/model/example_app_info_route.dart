import 'package:example/domain/entity/test.entity.dart';
import 'package:example/domain/entity/test/assignee.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:gp_core_v2/base/navigator/model/route/app_route_info.dart';

part 'example_app_info_route.freezed.dart';

const kExampleInitial = '/';
const kExampleLogin = '/login';
const kExampleHome = '/home';
const kExampleUserDetails = '/user';
const kExampleAssigneeDetails = '/assignee';

abstract class _ExampleAppInfoRouteDefaultParams {
  String get route;
}

@freezed
abstract class ExampleAppInfoRoute extends GPAppRouteInfo
    with _$ExampleAppInfoRoute {
  const ExampleAppInfoRoute._();

  ///
  @Implements<_ExampleAppInfoRouteDefaultParams>()
  const factory ExampleAppInfoRoute.initial({
    @Default(kExampleInitial) String route,
  }) = ExampleInitialRoute;

  ///
  @Implements<_ExampleAppInfoRouteDefaultParams>()
  const factory ExampleAppInfoRoute.login({
    @Default(kExampleLogin) String route,
  }) = ExampleLoginRoute;

  ///
  @Implements<_ExampleAppInfoRouteDefaultParams>()
  const factory ExampleAppInfoRoute.home({
    @Default(kExampleHome) String route,
  }) = ExampleHomeRoute;

  ///
  @Implements<_ExampleAppInfoRouteDefaultParams>()
  const factory ExampleAppInfoRoute.userProfile({
    @Default(kExampleUserDetails) String route,
    required User user,
  }) = ExampleUserRoute;

  ///
  @Implements<_ExampleAppInfoRouteDefaultParams>()
  const factory ExampleAppInfoRoute.assigneeDetails({
    @Default(kExampleAssigneeDetails) String route,
    required AssigneeEntity entity,
  }) = ExampleAssigneeRoute;
}
