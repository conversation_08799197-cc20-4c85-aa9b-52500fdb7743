// ignore_for_file: library_private_types_in_public_api

import 'package:go_router/go_router.dart';
import 'package:gp_core_v2/base/navigator/mapper/base_route_info_mapper.dart';

import '../go_router/go_router.route.dart';
import '../model/example_app_info_route.dart';

typedef _T = GoRouteData;
typedef _R = ExampleAppInfoRoute;

// Apply GoRouter for navigation
final class AppGoRouteInfoMapper extends GPBaseRouteInfoMapper<_T, _R> {
  @override
  _T map(_R infoRoute) {
    return switch (infoRoute) {
      ExampleInitialRoute() => ExampleTestRouteData(),
      ExampleLoginRoute() => ExampleLoginRouteData(),
      ExampleHomeRoute() => ExampleHomeRouteData(),
      ExampleUserRoute(user: final user) => ExampleUserRouteData($extra: user),
      ExampleAssigneeRoute(entity: final entity) =>
        ExampleAssigneeRouteData($extra: entity),
      _R() => throw UnimplementedError(),
    };
  }
}
