// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/widgets.dart';
import 'package:gp_core_v2/base/navigator/mapper/base_route_info_mapper.dart';

import '../model/example_app_info_route.dart';

typedef _T = RouteSettings;
typedef _R = ExampleAppInfoRoute;

// Apply default Flutter navigation
final class AppNavigatorRouteInfoMapper extends GPBaseRouteInfoMapper<_T, _R> {
  @override
  _T map(_R infoRoute) {
    return switch (infoRoute) {
      ExampleInitialRoute() => RouteSettings(name: infoRoute.route),
      ExampleLoginRoute() => RouteSettings(name: infoRoute.route),
      ExampleHomeRoute() => RouteSettings(name: infoRoute.route),
      ExampleUserRoute(user: final user) => RouteSettings(
          name: infoRoute.route,
          arguments: user,
        ),
      ExampleAssigneeRoute(entity: final assigneeEntity) => RouteSettings(
          name: infoRoute.route,
          arguments: assigneeEntity,
        ),
      _R() => throw UnimplementedError(),
    };
  }
}
