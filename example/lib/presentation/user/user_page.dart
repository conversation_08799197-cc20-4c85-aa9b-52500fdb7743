import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/presentation/utils.dart';
import 'package:flutter/material.dart';

import '../../domain/entity/test.entity.dart';
import '../../domain/entity/test/assignee.entity.dart';

const _mockAssigneeJson = {
  'id': 1,
  'displayName': '<PERSON><PERSON><PERSON>',
  'lang': 'vi',
  'fullName': '<PERSON><PERSON><PERSON>',
  'cover': 'https://i.pravatar.cc/128',
  'avatar': 'https://i.pravatar.cc/1024',
  'email': '<EMAIL>',
  'linkProfile': 'https://example.com/profile',
  'info': {
    'work': [
      {
        'company': 'Company A',
        'department': 'Department A',
        'title': 'Software Engineer',
        'departmentId': '1',
        'departments': ['1', '2', '3'],
        'departmentIds': ['1', '2', '3'],
        'roleId': '1',
        'privacy': 1,
      },
    ],
  },
  'workspaceAccount': 1,
  'workspaceId': '1',
  'phoneNumber': '**********',
  'avatarThumbPattern': 'https://example.com/avatar-thumb.jpg',
  'coverThumbPattern': 'https://example.com/cover-thumb.jpg',
  'userDepartment': 'Department A',
  'userRole': 'Software Engineer',
};

class ExampleUserPage extends StatelessWidget {
  const ExampleUserPage({
    required this.user,
    super.key,
  });

  final User user;

  void navigateToUserProfile(BuildContext context) {
    context.appNavigator().push(
          context,
          ExampleAppInfoRoute.assigneeDetails(
            entity: AssigneeEntity.fromJson(_mockAssigneeJson),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('User details'),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 100, 16, 16),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    width: 100,
                    child: Text('Username:'),
                  ),
                  Expanded(
                    child: Text(user.name),
                  )
                ],
              ),
              Row(
                children: [
                  SizedBox(
                    width: 100,
                    child: Text('User tag:'),
                  ),
                  Expanded(
                    child: Text(user.tag ?? ''),
                  )
                ],
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => navigateToUserProfile(context),
                child: Text('Assignee Details'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
