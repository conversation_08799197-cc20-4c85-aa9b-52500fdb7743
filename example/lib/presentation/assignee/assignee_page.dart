import 'package:flutter/material.dart';
import 'package:flutter_json_viewer/flutter_json_viewer.dart';

import '../../domain/entity/test/assignee.entity.dart';

class ExampleAssigneePage extends StatelessWidget {
  const ExampleAssigneePage({
    required this.assigneeEntity,
    super.key,
  });

  final AssigneeEntity assigneeEntity;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('User details'),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(16, 100, 16, 16),
        child: SingleChildScrollView(
          child: <PERSON>umn(
            children: [
              <PERSON><PERSON><PERSON><PERSON><PERSON>(assigneeEntity.toJson()),
            ],
          ),
        ),
      ),
    );
  }
}
