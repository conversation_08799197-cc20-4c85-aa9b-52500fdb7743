import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/utils/log.dart';
import 'package:gp_core_v2/base/bloc/common/common.dart';
import 'package:gp_core_v2/base/exception/base/app_exception_wrapper.dart';
import 'package:gp_core_v2/base/utils/extension/widget/bloc_extension.dart';

import '../shared/shared.dart';
import 'bloc/bloc.dart';
import 'test_page_behavior.dart';

class TestPage extends StatelessWidget with TestPageBehaviorMixin, TalkerMixin {
  const TestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: BlocProvider<TestBloc>(
        create: (context) => GetIt.I<TestBloc>(),
        child: BlocBuilder<TestBloc, TestState>(
          // buildWhen: (previous, current) => previous != current,
          builder: (BuildContext context, state) {
            return SingleChildScrollView(
              child: Column(children: [
                Text("Current State: ${state.test}"),
                TextButton(
                    onPressed: () => debugDumpRenderTree(),
                    // debugDumpApp(),
                    child: const Text("debugDumpRenderTree")),
                TextButton(
                    onPressed: () => test(context),
                    child: const Text("TestEvent")),
                TextButton(
                    onPressed: () => testCounter(context),
                    child: const Text("TestCounterEvent")),
                TextButton(
                  onPressed: () => authCheckEmailRequest(context),
                  child: Row(
                    children: [
                      const Text("email check"),
                      Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: BlocBuilder<CommonBloc, CommonState>(
                          builder: (BuildContext context, state) {
                            logDebug("commonState -> $state");
                            if (state.isLoading) {
                              return const CircularProgressIndicator();
                            }
                            return const Text("CurrentState: NoLoading");
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                TextButton(
                    onPressed: () => testError(context),
                    child: const Text("Handle error")),
                const Text("handle common state ").withBlocCommonBuilder(),
                const Text("handle common state with overrideErrorString")
                    .withBlocCommonBuilder(
                  overrideErrorString: "overrideErrorString",
                ),
                const Text("handle common state with listener")
                    .withBlocCommonListener(
                  listener: (_, state) {
                    logDebug(
                        'handle common state with listener: ${state.appExceptionWrapper?.errorMapped}');
                  },
                ),
                TextButton(
                  onPressed: () => authCheckEmailRequestWithUseCase(context),
                  child: const Text('authCheckEmailRequestWithUseCase')
                      .withBlocCommonBuilder(),
                ),
                TextButton(
                  onPressed: () => navigateToLogin(context),
                  child: const Text("Go to login page"),
                )
              ]),
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {},
        child: const Icon(Icons.developer_mode),
      ),
    );
  }
}
