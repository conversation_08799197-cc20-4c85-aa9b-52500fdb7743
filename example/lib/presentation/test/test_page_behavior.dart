import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/presentation/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../navigator/model/example_app_info_route.dart';
import 'bloc/test_bloc.dart';
import 'bloc/test_event.dart';

/// Toàn bộ behavior của `TestPage`
abstract class TestPageBehavior {
  void authCheckEmailRequest(BuildContext context);

  void authCheckEmailRequestWithUseCase(BuildContext context);

  void test(BuildContext context);

  void testCounter(BuildContext context);

  void testError(BuildContext context);

  void testErrorWithCatching(BuildContext context);

  void navigateToLogin(BuildContext context);
}

mixin TestPageBehaviorMixin on Widget implements TestPageBehavior {
  @override
  void authCheckEmailRequest(BuildContext context) {
    context.read<TestBloc>().add(
          AuthEmailCheck(
            AuthCheckEmailRequest('<EMAIL>', ''),
          ),
        );
  }

  @override
  void authCheckEmailRequestWithUseCase(BuildContext context) {
    context.read<TestBloc>().add(
          AuthEmailCheckWithRunCatching(
            AuthCheckEmailRequest('<EMAIL>', ''),
          ),
        );
  }

  @override
  void test(BuildContext context) {
    context.read<TestBloc>().add(const TestEvent());
  }

  @override
  void testCounter(BuildContext context) {
    // fixed params
    context.read<TestBloc>().add(TestCounterEvent(100));
  }

  @override
  void testError(BuildContext context) {
    context.read<TestBloc>().add(const TestError());
  }

  @override
  void testErrorWithCatching(BuildContext context) {
    context.read<TestBloc>().add(const TestError());
  }

  @override
  void navigateToLogin(BuildContext context) {
    context.appNavigator().push(
          context,
          ExampleAppInfoRoute.login(),
        );
  }
}
