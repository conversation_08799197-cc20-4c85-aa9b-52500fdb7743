import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'talker_behavior.dart';

mixin TalkerMixin on Widget implements TalkerBehavior{
  
  @override
  void navigateToTalkerScreen(BuildContext context) {
    if (GetIt.I.isRegistered<Talker>()) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => TalkerScreen(talker: GetIt.I.get<Talker>()),
        ),
      );
    }
  }
}
