import 'package:example/presentation/utils.dart';
import 'package:flutter/material.dart';

import '../../navigator/model/example_app_info_route.dart';

class ExampleLoginPage extends StatelessWidget {
  const ExampleLoginPage({super.key});

  void navigateToHome(BuildContext context) {
    context.appNavigator().popAndPush(
          context,
          ExampleAppInfoRoute.home(),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Login'),
      ),
      body: Safe<PERSON>rea(
        child: Padding(
          padding: EdgeInsets.fromLTRB(16, 100, 16, 16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Email',
                  ),
                ),
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Password',
                  ),
                ),
                const SizedBox(height: 16),
                Elevated<PERSON>utton(
                  onPressed: () => navigateToHome(context),
                  child: Text('Login'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
