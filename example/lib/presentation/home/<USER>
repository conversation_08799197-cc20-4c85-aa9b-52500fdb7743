import 'package:example/presentation/presentation.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/navigator/model/popup/app_popup_info.dart';
import 'package:gp_core_v2/base/navigator/model/snackbar/app_snackbar_info.dart';

import '../../domain/entity/test.entity.dart';
import '../../navigator/model/example_app_info_route.dart';

/// Toàn bộ behavior của `HomePage`
abstract class HomeBehavior {
  void navigateToUserProfile(BuildContext context);

  // ---------- SnackBar ---------- \\
  void showNormalSnackBar({required BuildContext context});

  void showSuccessSnackBar({required BuildContext context});

  void showErrorSnackBar({required BuildContext context});

  // ---------- SnackBar ---------- \\
  void showDialogWithOneBtn({required BuildContext context});

  void showDialogWithTwoBtn({required BuildContext context});

  void showDialogWithTwoVerticalBtn({required BuildContext context});

  // ---------- BottomSheet ---------- \\
  void showSimpleBottomSheet({required BuildContext context});

  void showBottomSheetWithOneBtn({required BuildContext context});

  void showBottomSheetWithTwoBtn({required BuildContext context});

  void showBottomSheetWithTwoVerticalBtn({required BuildContext context});
}

mixin HomeBehaviorMixin on Widget implements HomeBehavior {
  @override
  void navigateToUserProfile(BuildContext context) {
    context.appNavigator().push(
          context,
          ExampleAppInfoRoute.userProfile(
            user: User(
              id: 1,
              name: 'Nguyen Manh Toan',
              tag: '<EMAIL>',
            ),
          ),
        );
  }

  @override
  void showNormalSnackBar({
    required BuildContext context,
  }) {
    context.appNavigator().showSnackBar(
          context: context,
          snackbarInfo: GPAppSnackBarInfo(
            contentWidget: Text(
              'Normal snackbar',
              style: TextStyle(color: Colors.white),
            ),
            snackBarType: GPSnackBarType.normal,
          ),
        );
  }

  @override
  void showSuccessSnackBar({
    required BuildContext context,
  }) {
    context.appNavigator().showSnackBar(
          context: context,
          snackbarInfo: GPAppSnackBarInfo(
            contentWidget: Text(
              'Normal snackbar',
              style: TextStyle(color: Colors.white),
            ),
            snackBarType: GPSnackBarType.success,
          ),
        );
  }

  @override
  void showErrorSnackBar({
    required BuildContext context,
  }) {
    context.appNavigator().showSnackBar(
          context: context,
          snackbarInfo: GPAppSnackBarInfo(
            contentWidget: Text(
              'Error snackbar',
              style: TextStyle(color: Colors.white),
            ),
            snackBarType: GPSnackBarType.error,
          ),
        );
  }

  // ---------- Dialog ---------- \\
  @override
  void showDialogWithOneBtn({required BuildContext context}) {
    context.appNavigator().showDialog(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
          ),
        );
  }

  @override
  void showDialogWithTwoBtn({required BuildContext context}) {
    context.appNavigator().showDialog(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultNegativeModel(
              displayName: 'Cancel',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            secondBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
          ),
        );
  }

  @override
  void showDialogWithTwoVerticalBtn({required BuildContext context}) {
    context.appNavigator().showDialog(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            secondBtn: GPAppPopupInfoBtn.defaultNegativeModel(
              displayName: 'Cancel',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            buttonLayout: GPAppPopupButtonLayout.vertical,
          ),
        );
  }

  @override
  void showBottomSheetWithOneBtn({required BuildContext context}) {
    context.appNavigator().showBaseBottomSheet(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
          ),
        );
  }

  // ---------- BottomSheet ---------- \\
  @override
  void showBottomSheetWithTwoBtn({required BuildContext context}) {
    context.appNavigator().showBaseBottomSheet(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultNegativeModel(
              displayName: 'Cancel',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            secondBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
          ),
        );
  }

  @override
  void showSimpleBottomSheet({required BuildContext context}) {
    context.appNavigator().showBaseBottomSheet(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            iconSrc: Assets
                .PACKAGES_GP_ASSETS_IMAGES_SVG_CHILD_CALENDAR_NOTI_ROOM_SVG,
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultNegativeModel(
              displayName: 'Cancel',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            secondBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
          ),
        );
  }

  @override
  void showBottomSheetWithTwoVerticalBtn({required BuildContext context}) {
    context.appNavigator().showBaseBottomSheet(
          context: context,
          popupInfo: GPAppPopupInfo(
            title: 'GPBaseInfoWidget',
            content: 'A dialog is a type of modal window that\n'
                'appears in front of app content to\n'
                'provide critical information, or prompt\n'
                'for a decision to be made.',
            firstBtn: GPAppPopupInfoBtn.defaultPositiveModel(
              displayName: 'OK',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            secondBtn: GPAppPopupInfoBtn.defaultNegativeModel(
              displayName: 'Cancel',
              onClick: (context) => context.appNavigator().pop(context),
            ),
            buttonLayout: GPAppPopupButtonLayout.vertical,
          ),
        );
  }
}
