import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

import 'home_behavior.dart';

class ExampleHomePage extends StatelessWidget with HomeBehaviorMixin {
  const ExampleHomePage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Home'),
      ),
      body: SafeArea(
        child: Builder(builder: (context) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    ElevatedButton(
                      onPressed: () => navigateToUserProfile(context),
                      child: Text("View user profile"),
                    ),
                    const SizedBox(height: 16),

                    /// snackbar
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        "Snackbar",
                        style: textStyle(GPTypography.headingXLarge),
                      ),
                    ).paddingOnly(left: 16),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => showNormalSnackBar(context: context),
                      child: Text("Show normal snackbar"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => showSuccessSnackBar(context: context),
                      child: Text("Show success snackbar"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => showErrorSnackBar(context: context),
                      child: Text("Show error snackbar"),
                    ),

                    /// Dialog
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        "Dialog",
                        style: textStyle(GPTypography.headingXLarge),
                      ),
                    ).paddingOnly(left: 16),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => showDialogWithOneBtn(context: context),
                      child: Text("Show Dialog with one button"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => showDialogWithTwoBtn(context: context),
                      child: Text("Show Dialog with two button"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          showDialogWithTwoVerticalBtn(context: context),
                      child: Text("Show Dialog with two button vertical"),
                    ),

                    /// BottomSheet
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        "BottomSheet",
                        style: textStyle(GPTypography.headingXLarge),
                      ),
                    ).paddingOnly(left: 16),

                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => showSimpleBottomSheet(context: context),
                      child: Text("Show Simple BottomSheet"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          showBottomSheetWithOneBtn(context: context),
                      child: Text("Show BottomSheet with one button"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          showBottomSheetWithTwoBtn(context: context),
                      child: Text("Show BottomSheet with two button"),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () =>
                          showBottomSheetWithTwoVerticalBtn(context: context),
                      child: Text("Show BottomSheet with two button vertical"),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
