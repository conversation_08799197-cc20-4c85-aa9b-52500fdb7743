import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:injectable/injectable.dart';

import '../../data/model/auth/request/auth_check_email_request.dart';
import '../../data/model/auth/response/auth/auth_check_mail_response.dart';

@Named('kAuthRepository')
abstract class AuthRepository {
  Future<ApiResponseV2<AuthCheckMailResponse>> checkEmail({
    required AuthCheckEmailRequest checkEmailRequest,
  });
}
