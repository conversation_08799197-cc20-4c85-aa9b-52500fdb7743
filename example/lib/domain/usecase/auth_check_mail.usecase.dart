import 'package:example/data/model/auth/request/request.dart';
import 'package:example/data/model/auth/response/auth/auth.dart';
import 'package:example/domain/repository/auth_repo.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/usecase/base/base_future_use_case.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:injectable/injectable.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class AuthCheckMailUseCase extends GPBaseFutureUseCase<AuthCheckEmailRequest,
    ApiResponseV2<AuthCheckMailResponse>> {
  const AuthCheckMailUseCase(
    @Named('kAuthRepository') this._authRepository,
  );

  final AuthRepository _authRepository;

  @override
  Future<ApiResponseV2<AuthCheckMailResponse>> buildUseCase(
      AuthCheckEmailRequest input) async {
    return await _authRepository.checkEmail(checkEmailRequest: input);
  }
}
