// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'assignee_info.entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$InfoEntity {
  List<WorkEntity>? get work;

  /// Create a copy of InfoEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InfoEntityCopyWith<InfoEntity> get copyWith =>
      _$InfoEntityCopyWithImpl<InfoEntity>(this as InfoEntity, _$identity);

  /// Serializes this InfoEntity to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InfoEntity &&
            const DeepCollectionEquality().equals(other.work, work));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(work));

  @override
  String toString() {
    return 'InfoEntity(work: $work)';
  }
}

///
abstract mixin class $InfoEntityCopyWith<$Res> {
  factory $InfoEntityCopyWith(
          InfoEntity value, $Res Function(InfoEntity) _then) =
      _$InfoEntityCopyWithImpl;
  @useResult
  $Res call({List<WorkEntity>? work});
}

///
class _$InfoEntityCopyWithImpl<$Res> implements $InfoEntityCopyWith<$Res> {
  _$InfoEntityCopyWithImpl(this._self, this._then);

  final InfoEntity _self;
  final $Res Function(InfoEntity) _then;

  /// Create a copy of InfoEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? work = freezed,
  }) {
    return _then(_self.copyWith(
      work: freezed == work
          ? _self.work
          : work // ignore: cast_nullable_to_non_nullable
              as List<WorkEntity>?,
    ));
  }
}

///

@JsonSerializable(explicitToJson: true)
class _InfoEntity implements InfoEntity {
  _InfoEntity({final List<WorkEntity>? work}) : _work = work;
  factory _InfoEntity.fromJson(Map<String, dynamic> json) =>
      _$InfoEntityFromJson(json);

  final List<WorkEntity>? _work;
  @override
  List<WorkEntity>? get work {
    final value = _work;
    if (value == null) return null;
    if (_work is EqualUnmodifiableListView) return _work;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of InfoEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InfoEntityCopyWith<_InfoEntity> get copyWith =>
      __$InfoEntityCopyWithImpl<_InfoEntity>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InfoEntityToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InfoEntity &&
            const DeepCollectionEquality().equals(other._work, _work));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_work));

  @override
  String toString() {
    return 'InfoEntity(work: $work)';
  }
}

///
abstract mixin class _$InfoEntityCopyWith<$Res>
    implements $InfoEntityCopyWith<$Res> {
  factory _$InfoEntityCopyWith(
          _InfoEntity value, $Res Function(_InfoEntity) _then) =
      __$InfoEntityCopyWithImpl;
  @override
  @useResult
  $Res call({List<WorkEntity>? work});
}

///
class __$InfoEntityCopyWithImpl<$Res> implements _$InfoEntityCopyWith<$Res> {
  __$InfoEntityCopyWithImpl(this._self, this._then);

  final _InfoEntity _self;
  final $Res Function(_InfoEntity) _then;

  /// Create a copy of InfoEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? work = freezed,
  }) {
    return _then(_InfoEntity(
      work: freezed == work
          ? _self._work
          : work // ignore: cast_nullable_to_non_nullable
              as List<WorkEntity>?,
    ));
  }
}

// dart format on
