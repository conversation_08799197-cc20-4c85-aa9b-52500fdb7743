// ignore_for_file: invalid_annotation_target

/*
 * Created Date: Wednesday, 3rd April 2024, 11:30:23
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th April 2024 10:53:45
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs
// ignore_for_file: use_if_null_to_convert_nulls_to_bools

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core/core.dart';

import 'assignee_info.entity.dart';

part 'assignee.entity.freezed.dart';
part 'assignee.entity.g.dart';

@Freezed(
  fromJson: true,
  toJson: true,
)
abstract class AssigneeEntity with _$AssigneeEntity {
  @JsonSerializable(explicitToJson: true)
  factory AssigneeEntity({
    required int id,
    required String displayName,
    String? lang,
    String? fullName,
    String? cover,
    String? avatar,
    String? email,
    String? linkProfile,
    InfoEntity? info,
    int? workspaceAccount,
    String? workspaceId,
    String? phoneNumber,
    String? avatarThumbPattern,
    String? coverThumbPattern,
    String? userDepartment,
    String? userRole,
  }) = _AssigneeEntity;

  factory AssigneeEntity.fromJson(Map<String, dynamic> json) =>
      _$AssigneeEntityFromJson(json);
}

extension AssigneeEntityExt on AssigneeEntity {
  String get displayNameUserMe {
    if (id.toString() == Constants.userId()) {
      return fullName ?? displayName;
    } else {
      return displayName;
    }
  }

  bool get isCurrentUser {
    return Constants.userId() == id.toString();
  }

  String get role {
    String title;
    try {
      title = info?.work?.first.title ?? userRole ?? '';
    } catch (e) {
      title = '';
    }
    return title;
  }

  String get department {
    String result;
    try {
      result = info?.work?.first.department ?? userDepartment ?? '';
    } catch (e) {
      result = '';
    }

    return result;
  }

  String get displayLastName {
    final list = displayName.split(' ');
    if (list.isNotEmpty) {
      return list.last;
    }
    return displayName;
  }

  bool get needToFetch =>
      displayName.isEmpty ||
      (avatar?.isNotEmpty == true && avatarThumbPattern?.isNotEmpty == true);

  String? get avatarThumb =>
      avatarThumbPattern?.isNotEmpty == true ? avatarThumbPattern : avatar;
}
