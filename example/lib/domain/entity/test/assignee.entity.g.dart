// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assignee.entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AssigneeEntity _$AssigneeEntityFromJson(Map<String, dynamic> json) =>
    _AssigneeEntity(
      id: (json['id'] as num).toInt(),
      displayName: json['displayName'] as String,
      lang: json['lang'] as String?,
      fullName: json['fullName'] as String?,
      cover: json['cover'] as String?,
      avatar: json['avatar'] as String?,
      email: json['email'] as String?,
      linkProfile: json['linkProfile'] as String?,
      info: json['info'] == null
          ? null
          : InfoEntity.fromJson(json['info'] as Map<String, dynamic>),
      workspaceAccount: (json['workspaceAccount'] as num?)?.toInt(),
      workspaceId: json['workspaceId'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      avatarThumbPattern: json['avatarThumbPattern'] as String?,
      coverThumbPattern: json['coverThumbPattern'] as String?,
      userDepartment: json['userDepartment'] as String?,
      userRole: json['userRole'] as String?,
    );

Map<String, dynamic> _$AssigneeEntityToJson(_AssigneeEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'displayName': instance.displayName,
      'lang': instance.lang,
      'fullName': instance.fullName,
      'cover': instance.cover,
      'avatar': instance.avatar,
      'email': instance.email,
      'linkProfile': instance.linkProfile,
      'info': instance.info?.toJson(),
      'workspaceAccount': instance.workspaceAccount,
      'workspaceId': instance.workspaceId,
      'phoneNumber': instance.phoneNumber,
      'avatarThumbPattern': instance.avatarThumbPattern,
      'coverThumbPattern': instance.coverThumbPattern,
      'userDepartment': instance.userDepartment,
      'userRole': instance.userRole,
    };
