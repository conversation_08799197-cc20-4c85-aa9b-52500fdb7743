// ignore_for_file: invalid_annotation_target

/*
 * Created Date: Wednesday, 3rd April 2024, 13:56:03
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th April 2024 10:53:36
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:freezed_annotation/freezed_annotation.dart';

import 'assignee_work.entity.dart';

part 'assignee_info.entity.freezed.dart';
part 'assignee_info.entity.g.dart';

@Freezed(
  fromJson: true,
  toJson: true,
)
abstract class InfoEntity with _$InfoEntity {
  @JsonSerializable(explicitToJson: true)
  factory InfoEntity({
    List<WorkEntity>? work,
  }) = _InfoEntity;

  factory InfoEntity.fromJson(Map<String, dynamic> json) =>
      _$InfoEntityFromJson(json);
}
