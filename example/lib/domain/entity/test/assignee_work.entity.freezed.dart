// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'assignee_work.entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$WorkEntity {
  String? get company;
  String? get department;
  String? get title;
  String? get departmentId;
  List<dynamic>? get departments;
  List<dynamic>? get departmentIds;
  String? get roleId;
  int? get privacy;

  /// Create a copy of WorkEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $WorkEntityCopyWith<WorkEntity> get copyWith =>
      _$WorkEntityCopyWithImpl<WorkEntity>(this as WorkEntity, _$identity);

  /// Serializes this WorkEntity to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is WorkEntity &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.departmentId, departmentId) ||
                other.departmentId == departmentId) &&
            const DeepCollectionEquality()
                .equals(other.departments, departments) &&
            const DeepCollectionEquality()
                .equals(other.departmentIds, departmentIds) &&
            (identical(other.roleId, roleId) || other.roleId == roleId) &&
            (identical(other.privacy, privacy) || other.privacy == privacy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      company,
      department,
      title,
      departmentId,
      const DeepCollectionEquality().hash(departments),
      const DeepCollectionEquality().hash(departmentIds),
      roleId,
      privacy);

  @override
  String toString() {
    return 'WorkEntity(company: $company, department: $department, title: $title, departmentId: $departmentId, departments: $departments, departmentIds: $departmentIds, roleId: $roleId, privacy: $privacy)';
  }
}

///
abstract mixin class $WorkEntityCopyWith<$Res> {
  factory $WorkEntityCopyWith(
          WorkEntity value, $Res Function(WorkEntity) _then) =
      _$WorkEntityCopyWithImpl;
  @useResult
  $Res call(
      {String? company,
      String? department,
      String? title,
      String? departmentId,
      List<dynamic>? departments,
      List<dynamic>? departmentIds,
      String? roleId,
      int? privacy});
}

///
class _$WorkEntityCopyWithImpl<$Res> implements $WorkEntityCopyWith<$Res> {
  _$WorkEntityCopyWithImpl(this._self, this._then);

  final WorkEntity _self;
  final $Res Function(WorkEntity) _then;

  /// Create a copy of WorkEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? company = freezed,
    Object? department = freezed,
    Object? title = freezed,
    Object? departmentId = freezed,
    Object? departments = freezed,
    Object? departmentIds = freezed,
    Object? roleId = freezed,
    Object? privacy = freezed,
  }) {
    return _then(_self.copyWith(
      company: freezed == company
          ? _self.company
          : company // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _self.department
          : department // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      departmentId: freezed == departmentId
          ? _self.departmentId
          : departmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      departments: freezed == departments
          ? _self.departments
          : departments // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      departmentIds: freezed == departmentIds
          ? _self.departmentIds
          : departmentIds // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      roleId: freezed == roleId
          ? _self.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as String?,
      privacy: freezed == privacy
          ? _self.privacy
          : privacy // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

///

@JsonSerializable(explicitToJson: true)
class _WorkEntity implements WorkEntity {
  _WorkEntity(
      {this.company,
      this.department,
      this.title,
      this.departmentId,
      final List<dynamic>? departments,
      final List<dynamic>? departmentIds,
      this.roleId,
      this.privacy})
      : _departments = departments,
        _departmentIds = departmentIds;
  factory _WorkEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkEntityFromJson(json);

  @override
  final String? company;
  @override
  final String? department;
  @override
  final String? title;
  @override
  final String? departmentId;
  final List<dynamic>? _departments;
  @override
  List<dynamic>? get departments {
    final value = _departments;
    if (value == null) return null;
    if (_departments is EqualUnmodifiableListView) return _departments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<dynamic>? _departmentIds;
  @override
  List<dynamic>? get departmentIds {
    final value = _departmentIds;
    if (value == null) return null;
    if (_departmentIds is EqualUnmodifiableListView) return _departmentIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? roleId;
  @override
  final int? privacy;

  /// Create a copy of WorkEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WorkEntityCopyWith<_WorkEntity> get copyWith =>
      __$WorkEntityCopyWithImpl<_WorkEntity>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$WorkEntityToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WorkEntity &&
            (identical(other.company, company) || other.company == company) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.departmentId, departmentId) ||
                other.departmentId == departmentId) &&
            const DeepCollectionEquality()
                .equals(other._departments, _departments) &&
            const DeepCollectionEquality()
                .equals(other._departmentIds, _departmentIds) &&
            (identical(other.roleId, roleId) || other.roleId == roleId) &&
            (identical(other.privacy, privacy) || other.privacy == privacy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      company,
      department,
      title,
      departmentId,
      const DeepCollectionEquality().hash(_departments),
      const DeepCollectionEquality().hash(_departmentIds),
      roleId,
      privacy);

  @override
  String toString() {
    return 'WorkEntity(company: $company, department: $department, title: $title, departmentId: $departmentId, departments: $departments, departmentIds: $departmentIds, roleId: $roleId, privacy: $privacy)';
  }
}

///
abstract mixin class _$WorkEntityCopyWith<$Res>
    implements $WorkEntityCopyWith<$Res> {
  factory _$WorkEntityCopyWith(
          _WorkEntity value, $Res Function(_WorkEntity) _then) =
      __$WorkEntityCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? company,
      String? department,
      String? title,
      String? departmentId,
      List<dynamic>? departments,
      List<dynamic>? departmentIds,
      String? roleId,
      int? privacy});
}

///
class __$WorkEntityCopyWithImpl<$Res> implements _$WorkEntityCopyWith<$Res> {
  __$WorkEntityCopyWithImpl(this._self, this._then);

  final _WorkEntity _self;
  final $Res Function(_WorkEntity) _then;

  /// Create a copy of WorkEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? company = freezed,
    Object? department = freezed,
    Object? title = freezed,
    Object? departmentId = freezed,
    Object? departments = freezed,
    Object? departmentIds = freezed,
    Object? roleId = freezed,
    Object? privacy = freezed,
  }) {
    return _then(_WorkEntity(
      company: freezed == company
          ? _self.company
          : company // ignore: cast_nullable_to_non_nullable
              as String?,
      department: freezed == department
          ? _self.department
          : department // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      departmentId: freezed == departmentId
          ? _self.departmentId
          : departmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      departments: freezed == departments
          ? _self._departments
          : departments // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      departmentIds: freezed == departmentIds
          ? _self._departmentIds
          : departmentIds // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
      roleId: freezed == roleId
          ? _self.roleId
          : roleId // ignore: cast_nullable_to_non_nullable
              as String?,
      privacy: freezed == privacy
          ? _self.privacy
          : privacy // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

// dart format on
