// ignore_for_file: invalid_annotation_target

/*
 * Created Date: Wednesday, 3rd April 2024, 13:54:22
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 3rd April 2024 13:57:46
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:freezed_annotation/freezed_annotation.dart';

part 'assignee_work.entity.freezed.dart';
part 'assignee_work.entity.g.dart';

@Freezed(
  fromJson: true,
  toJson: true,
)
abstract class WorkEntity with _$WorkEntity {
  @JsonSerializable(explicitToJson: true)
  factory WorkEntity({
    String? company,
    String? department,
    String? title,
    String? departmentId,
    List<dynamic>? departments,
    List<dynamic>? departmentIds,
    String? roleId,
    int? privacy,
  }) = _WorkEntity;

  factory WorkEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkEntityFromJson(json);
}
