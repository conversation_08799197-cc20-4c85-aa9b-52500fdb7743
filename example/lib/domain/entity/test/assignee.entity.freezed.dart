// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'assignee.entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$AssigneeEntity {
  int get id;
  String get displayName;
  String? get lang;
  String? get fullName;
  String? get cover;
  String? get avatar;
  String? get email;
  String? get linkProfile;
  InfoEntity? get info;
  int? get workspaceAccount;
  String? get workspaceId;
  String? get phoneNumber;
  String? get avatarThumbPattern;
  String? get coverThumbPattern;
  String? get userDepartment;
  String? get userRole;

  /// Create a copy of AssigneeEntity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AssigneeEntityCopyWith<AssigneeEntity> get copyWith =>
      _$AssigneeEntityCopyWithImpl<AssigneeEntity>(
          this as AssigneeEntity, _$identity);

  /// Serializes this AssigneeEntity to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AssigneeEntity &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.lang, lang) || other.lang == lang) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.linkProfile, linkProfile) ||
                other.linkProfile == linkProfile) &&
            (identical(other.info, info) || other.info == info) &&
            (identical(other.workspaceAccount, workspaceAccount) ||
                other.workspaceAccount == workspaceAccount) &&
            (identical(other.workspaceId, workspaceId) ||
                other.workspaceId == workspaceId) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.avatarThumbPattern, avatarThumbPattern) ||
                other.avatarThumbPattern == avatarThumbPattern) &&
            (identical(other.coverThumbPattern, coverThumbPattern) ||
                other.coverThumbPattern == coverThumbPattern) &&
            (identical(other.userDepartment, userDepartment) ||
                other.userDepartment == userDepartment) &&
            (identical(other.userRole, userRole) ||
                other.userRole == userRole));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      displayName,
      lang,
      fullName,
      cover,
      avatar,
      email,
      linkProfile,
      info,
      workspaceAccount,
      workspaceId,
      phoneNumber,
      avatarThumbPattern,
      coverThumbPattern,
      userDepartment,
      userRole);

  @override
  String toString() {
    return 'AssigneeEntity(id: $id, displayName: $displayName, lang: $lang, fullName: $fullName, cover: $cover, avatar: $avatar, email: $email, linkProfile: $linkProfile, info: $info, workspaceAccount: $workspaceAccount, workspaceId: $workspaceId, phoneNumber: $phoneNumber, avatarThumbPattern: $avatarThumbPattern, coverThumbPattern: $coverThumbPattern, userDepartment: $userDepartment, userRole: $userRole)';
  }
}

///
abstract mixin class $AssigneeEntityCopyWith<$Res> {
  factory $AssigneeEntityCopyWith(
          AssigneeEntity value, $Res Function(AssigneeEntity) _then) =
      _$AssigneeEntityCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      String displayName,
      String? lang,
      String? fullName,
      String? cover,
      String? avatar,
      String? email,
      String? linkProfile,
      InfoEntity? info,
      int? workspaceAccount,
      String? workspaceId,
      String? phoneNumber,
      String? avatarThumbPattern,
      String? coverThumbPattern,
      String? userDepartment,
      String? userRole});

  $InfoEntityCopyWith<$Res>? get info;
}

///
class _$AssigneeEntityCopyWithImpl<$Res>
    implements $AssigneeEntityCopyWith<$Res> {
  _$AssigneeEntityCopyWithImpl(this._self, this._then);

  final AssigneeEntity _self;
  final $Res Function(AssigneeEntity) _then;

  /// Create a copy of AssigneeEntity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? displayName = null,
    Object? lang = freezed,
    Object? fullName = freezed,
    Object? cover = freezed,
    Object? avatar = freezed,
    Object? email = freezed,
    Object? linkProfile = freezed,
    Object? info = freezed,
    Object? workspaceAccount = freezed,
    Object? workspaceId = freezed,
    Object? phoneNumber = freezed,
    Object? avatarThumbPattern = freezed,
    Object? coverThumbPattern = freezed,
    Object? userDepartment = freezed,
    Object? userRole = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      displayName: null == displayName
          ? _self.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      lang: freezed == lang
          ? _self.lang
          : lang // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _self.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _self.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      linkProfile: freezed == linkProfile
          ? _self.linkProfile
          : linkProfile // ignore: cast_nullable_to_non_nullable
              as String?,
      info: freezed == info
          ? _self.info
          : info // ignore: cast_nullable_to_non_nullable
              as InfoEntity?,
      workspaceAccount: freezed == workspaceAccount
          ? _self.workspaceAccount
          : workspaceAccount // ignore: cast_nullable_to_non_nullable
              as int?,
      workspaceId: freezed == workspaceId
          ? _self.workspaceId
          : workspaceId // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarThumbPattern: freezed == avatarThumbPattern
          ? _self.avatarThumbPattern
          : avatarThumbPattern // ignore: cast_nullable_to_non_nullable
              as String?,
      coverThumbPattern: freezed == coverThumbPattern
          ? _self.coverThumbPattern
          : coverThumbPattern // ignore: cast_nullable_to_non_nullable
              as String?,
      userDepartment: freezed == userDepartment
          ? _self.userDepartment
          : userDepartment // ignore: cast_nullable_to_non_nullable
              as String?,
      userRole: freezed == userRole
          ? _self.userRole
          : userRole // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AssigneeEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InfoEntityCopyWith<$Res>? get info {
    if (_self.info == null) {
      return null;
    }

    return $InfoEntityCopyWith<$Res>(_self.info!, (value) {
      return _then(_self.copyWith(info: value));
    });
  }
}

///

@JsonSerializable(explicitToJson: true)
class _AssigneeEntity implements AssigneeEntity {
  _AssigneeEntity(
      {required this.id,
      required this.displayName,
      this.lang,
      this.fullName,
      this.cover,
      this.avatar,
      this.email,
      this.linkProfile,
      this.info,
      this.workspaceAccount,
      this.workspaceId,
      this.phoneNumber,
      this.avatarThumbPattern,
      this.coverThumbPattern,
      this.userDepartment,
      this.userRole});
  factory _AssigneeEntity.fromJson(Map<String, dynamic> json) =>
      _$AssigneeEntityFromJson(json);

  @override
  final int id;
  @override
  final String displayName;
  @override
  final String? lang;
  @override
  final String? fullName;
  @override
  final String? cover;
  @override
  final String? avatar;
  @override
  final String? email;
  @override
  final String? linkProfile;
  @override
  final InfoEntity? info;
  @override
  final int? workspaceAccount;
  @override
  final String? workspaceId;
  @override
  final String? phoneNumber;
  @override
  final String? avatarThumbPattern;
  @override
  final String? coverThumbPattern;
  @override
  final String? userDepartment;
  @override
  final String? userRole;

  /// Create a copy of AssigneeEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AssigneeEntityCopyWith<_AssigneeEntity> get copyWith =>
      __$AssigneeEntityCopyWithImpl<_AssigneeEntity>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AssigneeEntityToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AssigneeEntity &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.lang, lang) || other.lang == lang) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.linkProfile, linkProfile) ||
                other.linkProfile == linkProfile) &&
            (identical(other.info, info) || other.info == info) &&
            (identical(other.workspaceAccount, workspaceAccount) ||
                other.workspaceAccount == workspaceAccount) &&
            (identical(other.workspaceId, workspaceId) ||
                other.workspaceId == workspaceId) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.avatarThumbPattern, avatarThumbPattern) ||
                other.avatarThumbPattern == avatarThumbPattern) &&
            (identical(other.coverThumbPattern, coverThumbPattern) ||
                other.coverThumbPattern == coverThumbPattern) &&
            (identical(other.userDepartment, userDepartment) ||
                other.userDepartment == userDepartment) &&
            (identical(other.userRole, userRole) ||
                other.userRole == userRole));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      displayName,
      lang,
      fullName,
      cover,
      avatar,
      email,
      linkProfile,
      info,
      workspaceAccount,
      workspaceId,
      phoneNumber,
      avatarThumbPattern,
      coverThumbPattern,
      userDepartment,
      userRole);

  @override
  String toString() {
    return 'AssigneeEntity(id: $id, displayName: $displayName, lang: $lang, fullName: $fullName, cover: $cover, avatar: $avatar, email: $email, linkProfile: $linkProfile, info: $info, workspaceAccount: $workspaceAccount, workspaceId: $workspaceId, phoneNumber: $phoneNumber, avatarThumbPattern: $avatarThumbPattern, coverThumbPattern: $coverThumbPattern, userDepartment: $userDepartment, userRole: $userRole)';
  }
}

///
abstract mixin class _$AssigneeEntityCopyWith<$Res>
    implements $AssigneeEntityCopyWith<$Res> {
  factory _$AssigneeEntityCopyWith(
          _AssigneeEntity value, $Res Function(_AssigneeEntity) _then) =
      __$AssigneeEntityCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      String displayName,
      String? lang,
      String? fullName,
      String? cover,
      String? avatar,
      String? email,
      String? linkProfile,
      InfoEntity? info,
      int? workspaceAccount,
      String? workspaceId,
      String? phoneNumber,
      String? avatarThumbPattern,
      String? coverThumbPattern,
      String? userDepartment,
      String? userRole});

  @override
  $InfoEntityCopyWith<$Res>? get info;
}

///
class __$AssigneeEntityCopyWithImpl<$Res>
    implements _$AssigneeEntityCopyWith<$Res> {
  __$AssigneeEntityCopyWithImpl(this._self, this._then);

  final _AssigneeEntity _self;
  final $Res Function(_AssigneeEntity) _then;

  /// Create a copy of AssigneeEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? displayName = null,
    Object? lang = freezed,
    Object? fullName = freezed,
    Object? cover = freezed,
    Object? avatar = freezed,
    Object? email = freezed,
    Object? linkProfile = freezed,
    Object? info = freezed,
    Object? workspaceAccount = freezed,
    Object? workspaceId = freezed,
    Object? phoneNumber = freezed,
    Object? avatarThumbPattern = freezed,
    Object? coverThumbPattern = freezed,
    Object? userDepartment = freezed,
    Object? userRole = freezed,
  }) {
    return _then(_AssigneeEntity(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      displayName: null == displayName
          ? _self.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      lang: freezed == lang
          ? _self.lang
          : lang // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _self.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      avatar: freezed == avatar
          ? _self.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      linkProfile: freezed == linkProfile
          ? _self.linkProfile
          : linkProfile // ignore: cast_nullable_to_non_nullable
              as String?,
      info: freezed == info
          ? _self.info
          : info // ignore: cast_nullable_to_non_nullable
              as InfoEntity?,
      workspaceAccount: freezed == workspaceAccount
          ? _self.workspaceAccount
          : workspaceAccount // ignore: cast_nullable_to_non_nullable
              as int?,
      workspaceId: freezed == workspaceId
          ? _self.workspaceId
          : workspaceId // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarThumbPattern: freezed == avatarThumbPattern
          ? _self.avatarThumbPattern
          : avatarThumbPattern // ignore: cast_nullable_to_non_nullable
              as String?,
      coverThumbPattern: freezed == coverThumbPattern
          ? _self.coverThumbPattern
          : coverThumbPattern // ignore: cast_nullable_to_non_nullable
              as String?,
      userDepartment: freezed == userDepartment
          ? _self.userDepartment
          : userDepartment // ignore: cast_nullable_to_non_nullable
              as String?,
      userRole: freezed == userRole
          ? _self.userRole
          : userRole // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AssigneeEntity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InfoEntityCopyWith<$Res>? get info {
    if (_self.info == null) {
      return null;
    }

    return $InfoEntityCopyWith<$Res>(_self.info!, (value) {
      return _then(_self.copyWith(info: value));
    });
  }
}

// dart format on
