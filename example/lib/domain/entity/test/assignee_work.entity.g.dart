// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'assignee_work.entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_WorkEntity _$WorkEntityFromJson(Map<String, dynamic> json) => _WorkEntity(
      company: json['company'] as String?,
      department: json['department'] as String?,
      title: json['title'] as String?,
      departmentId: json['departmentId'] as String?,
      departments: json['departments'] as List<dynamic>?,
      departmentIds: json['departmentIds'] as List<dynamic>?,
      roleId: json['roleId'] as String?,
      privacy: (json['privacy'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WorkEntityToJson(_WorkEntity instance) =>
    <String, dynamic>{
      'company': instance.company,
      'department': instance.department,
      'title': instance.title,
      'departmentId': instance.departmentId,
      'departments': instance.departments,
      'departmentIds': instance.departmentIds,
      'roleId': instance.roleId,
      'privacy': instance.privacy,
    };
