/*
 * Created Date: 2/12/2023 10:56:3
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 8th January 2024 14:29:36
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';
import 'dart:developer';

import 'package:example/di/component/app.component.config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/bloc/common/common_bloc.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';
import 'package:gp_core_v2/base/navigator/observer/gp_navigator_observer.dart';
import 'package:talker_bloc_logger/talker_bloc_logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

import 'di/component/app.component.dart' as app;
import 'domain/entity/entity.dart';
import 'navigator/go_router/go_router.route.dart';
import 'navigator/model/example_app_info_route.dart';
import 'presentation/presentation.dart';

const _appNavigator = AppNavigator.getx;

final _appRouter = GoRouter(
  debugLogDiagnostics: kDebugMode,
  routes: $appRoutes,
  initialLocation: kExampleInitial,
  navigatorKey:
      GetIt.I<GlobalKey<NavigatorState>>(instanceName: 'kAppNavigatorKey'),
  observers: [
    GPNavigatorObserver(),
    if (GetIt.I.isRegistered<Talker>())
      TalkerRouteObserver(GetIt.I.get<Talker>()),
  ],
);

void _initNavigatorScope() {
  switch (_appNavigator) {
    case AppNavigator.flutter:
      GetIt.I.initNavigateWithFlutterScope();
      break;
    case AppNavigator.goRouter:
      GetIt.I.initNavigateWithGoRouterScope();
      break;
    case AppNavigator.getx:
      GetIt.I.initNavigateWithGetXScope();
      break;
  }
}

void main(List<String> args) async {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    await app.configureInjection();

    if (GetIt.I.isRegistered<Talker>()) {
      Bloc.observer = TalkerBlocObserver(talker: GetIt.instance<Talker>());
    } else {
      log("Talker is not registered");
    }

    _initNavigatorScope();

    runApp(const MyWidget());
  }, (Object error, StackTrace stackTrace) {
    log("App error: $error, stackTrace: $stackTrace");
  });
}

class MyWidget extends StatelessWidget {
  const MyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<CommonBloc>(
      create: (context) => GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
      child: RepositoryProvider.value(
        value: GetIt.I<GPAppNavigator<ExampleAppInfoRoute>>(
            instanceName: 'kAppNavigator'),
        child: _AppWidget(),
      ),
    );
  }
}

class _AppWidget extends StatelessWidget {
  const _AppWidget();

  Route<dynamic> _onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case kExampleInitial:
        return MaterialPageRoute(
          builder: (context) => const TestPage(),
        );
      case kExampleHome:
        return MaterialPageRoute(
          builder: (context) => const ExampleHomePage(),
        );
      case kExampleLogin:
        return MaterialPageRoute(
          builder: (context) => const ExampleLoginPage(),
        );
      case kExampleUserDetails:
        return MaterialPageRoute(
          builder: (context) => ExampleUserPage(
            user: settings.arguments as User,
          ),
        );
      case kExampleAssigneeDetails:
        return MaterialPageRoute(
          builder: (context) => ExampleAssigneePage(
            assigneeEntity: settings.arguments as AssigneeEntity,
          ),
        );

      default:
        return MaterialPageRoute(
          builder: (context) => Text('Not found page'),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return switch (_appNavigator) {
      AppNavigator.flutter => MaterialApp(
          showPerformanceOverlay: false,
          debugShowMaterialGrid: false,
          debugShowCheckedModeBanner: false,
          onGenerateRoute: _onGenerateRoute,
          initialRoute: kExampleInitial,
        ),
      AppNavigator.goRouter => MaterialApp.router(
          showPerformanceOverlay: false,
          debugShowMaterialGrid: false,
          debugShowCheckedModeBanner: false,
          routerConfig: _appRouter,
        ),
      AppNavigator.getx => GetMaterialApp(
          showPerformanceOverlay: false,
          debugShowMaterialGrid: false,
          debugShowCheckedModeBanner: false,
          onGenerateRoute: _onGenerateRoute,
          initialRoute: kExampleInitial,
        ),
    };
  }
}
