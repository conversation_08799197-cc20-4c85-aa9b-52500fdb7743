/*
 * Created Date: 2/12/2023 10:56:3
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 21st February 2024 14:31:44
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:dio/dio.dart' hide Headers;
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/constants/server/http_keys.constants.dart';
import 'package:gp_core_v2/base/usecase/model/model.dart';
import 'package:injectable/injectable.dart';
import 'package:retrofit/retrofit.dart';

import '../../../constants/url/url.constants.dart';
import '../../model/auth/request/auth_check_email_request.dart';
import '../../model/auth/response/auth/auth_check_mail_response.dart';

part 'auth.service.g.dart';

@LazySingleton(order: DiConstants.kDataServiceOrder)
@Named('kAuthService')
@RestApi()
abstract class AuthService {
  @FactoryMethod()
  factory AuthService(
    @Named('kDio') Dio dio, {
    @Named('kAuthUrl') String? baseUrl,
  }) = _AuthService;

  @POST(UrlConstants.kCheckEmailPath)
  @Headers({
    HttpKeyConstants.kContentType: HttpKeyConstants.kApplicationJson,
  })
  Future<ApiResponseV2<AuthCheckMailResponse>> checkEmail({
    @Body() required AuthCheckEmailRequest checkEmailRequest,
  });
}
