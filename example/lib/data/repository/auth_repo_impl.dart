import 'package:example/data/data_source/remote/auth.service.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:example/domain/repository/auth_repo.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: AuthRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kAuthRepository')
final class AuthRepositoryImpl implements AuthRepository {
  const AuthRepositoryImpl(
    @Named('kAuthService') this.authService,
  );

  final AuthService authService;

  @override
  Future<ApiResponseV2<AuthCheckMailResponse>> checkEmail({
    required AuthCheckEmailRequest checkEmailRequest,
  }) async {
    return authService.checkEmail(checkEmailRequest: checkEmailRequest);
  }
}
