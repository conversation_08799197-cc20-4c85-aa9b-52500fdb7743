import 'package:flutter_test/flutter_test.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';

void main() {
  group('AuthCheckEmailRequest', () {
    const testEmail = '<EMAIL>';
    const testPhoneNumber = '+84123456789';

    test('should create instance with correct properties', () {
      // Arrange & Act
      final request = AuthCheckEmailRequest(testEmail, testPhoneNumber);

      // Assert
      expect(request.email, testEmail);
      expect(request.phoneNumber, testPhoneNumber);
    });

    test('should serialize to JSON correctly', () {
      // Arrange
      final request = AuthCheckEmailRequest(testEmail, testPhoneNumber);
      final expectedJson = {
        'email': testEmail,
        'phone_number': testPhoneNumber,
      };

      // Act
      final json = request.toJson();

      // Assert
      expect(json, expectedJson);
    });

    test('should deserialize from JSON correctly', () {
      // Arrange
      final json = {
        'email': testEmail,
        'phone_number': testPhoneNumber,
      };

      // Act
      final request = AuthCheckEmailRequest.fromJson(json);

      // Assert
      expect(request.email, testEmail);
      expect(request.phoneNumber, testPhoneNumber);
    });

    test('should handle JSON serialization round trip', () {
      // Arrange
      final originalRequest = AuthCheckEmailRequest(testEmail, testPhoneNumber);

      // Act
      final json = originalRequest.toJson();
      final deserializedRequest = AuthCheckEmailRequest.fromJson(json);

      // Assert
      expect(deserializedRequest.email, originalRequest.email);
      expect(deserializedRequest.phoneNumber, originalRequest.phoneNumber);
    });
  });
}
