import 'package:flutter_test/flutter_test.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';

void main() {
  group('AuthCheckMailResponse', () {
    const testUserId = 105883395;
    const testNewDomain = false;
    const testSalt = 'SnYf6x';

    test('should create instance with required properties only', () {
      // Arrange & Act
      final response = AuthCheckMailResponse(userId: testUserId);

      // Assert
      expect(response.userId, testUserId);
      expect(response.newDomain, isNull);
      expect(response.salt, isNull);
    });

    test('should create instance with all properties', () {
      // Arrange & Act
      final response = AuthCheckMailResponse(
        userId: testUserId,
        newDomain: testNewDomain,
        salt: testSalt,
      );

      // Assert
      expect(response.userId, testUserId);
      expect(response.newDomain, testNewDomain);
      expect(response.salt, testSalt);
    });

    test('should serialize to JSON correctly with all properties', () {
      // Arrange
      final response = AuthCheckMailResponse(
        userId: testUserId,
        newDomain: testNewDomain,
        salt: testSalt,
      );
      final expectedJson = {
        'user_id': testUserId,
        'new_domain': testNewDomain,
        'salt': testSalt,
      };

      // Act
      final json = response.toJson();

      // Assert
      expect(json, expectedJson);
    });

    test('should serialize to JSON correctly with required properties only', () {
      // Arrange
      final response = AuthCheckMailResponse(userId: testUserId);
      final expectedJson = {
        'user_id': testUserId,
        'new_domain': null,
        'salt': null,
      };

      // Act
      final json = response.toJson();

      // Assert
      expect(json, expectedJson);
    });

    test('should deserialize from JSON correctly with all properties', () {
      // Arrange
      final json = {
        'user_id': testUserId,
        'new_domain': testNewDomain,
        'salt': testSalt,
      };

      // Act
      final response = AuthCheckMailResponse.fromJson(json);

      // Assert
      expect(response.userId, testUserId);
      expect(response.newDomain, testNewDomain);
      expect(response.salt, testSalt);
    });

    test('should deserialize from JSON correctly with required properties only', () {
      // Arrange
      final json = {
        'user_id': testUserId,
      };

      // Act
      final response = AuthCheckMailResponse.fromJson(json);

      // Assert
      expect(response.userId, testUserId);
      expect(response.newDomain, isNull);
      expect(response.salt, isNull);
    });

    test('should handle JSON serialization round trip', () {
      // Arrange
      final originalResponse = AuthCheckMailResponse(
        userId: testUserId,
        newDomain: testNewDomain,
        salt: testSalt,
      );

      // Act
      final json = originalResponse.toJson();
      final deserializedResponse = AuthCheckMailResponse.fromJson(json);

      // Assert
      expect(deserializedResponse.userId, originalResponse.userId);
      expect(deserializedResponse.newDomain, originalResponse.newDomain);
      expect(deserializedResponse.salt, originalResponse.salt);
    });

    test('should handle mock data correctly', () {
      // Arrange - Using the same data as in mock file
      final json = {
        'user_id': 105883395,
        'new_domain': false,
        'salt': 'SnYf6x',
      };

      // Act
      final response = AuthCheckMailResponse.fromJson(json);

      // Assert
      expect(response.userId, 105883395);
      expect(response.newDomain, false);
      expect(response.salt, 'SnYf6x');
    });
  });
}
