import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:example/domain/usecase/auth_check_mail.usecase.dart';
import 'package:example/domain/entity/auth.entity.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:dio/dio.dart';

import '../helpers/test_helper.mocks.dart';
import '../helpers/test_constants.dart';

void main() {
  group('Domain Layer Integration Tests', () {
    late MockAuthRepository mockAuthRepository;
    late AuthCheckMailUseCase authCheckMailUseCase;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      authCheckMailUseCase = AuthCheckMailUseCase(mockAuthRepository);
    });

    group('Complete Domain Flow', () {
      test('should complete full domain flow from use case to repository successfully', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Assert - Verify complete flow
        expect(result, isA<ApiResponseV2<AuthCheckMailResponse>>());
        expect(result.status, 'success');
        expect(result.data, isNotNull);
        expect(result.data.userId, TestConstants.testUserId);
        expect(result.data.newDomain, TestConstants.testNewDomain);
        expect(result.data.salt, TestConstants.testSalt);
        
        // Verify repository was called through use case
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle domain business logic correctly', () async {
        // Arrange - Test different business scenarios
        final newUserRequest = AuthCheckEmailRequest('<EMAIL>', '+***********');
        final existingUserRequest = AuthCheckEmailRequest('<EMAIL>', '+***********');
        
        final newUserResponse = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(
            userId: 12345,
            newDomain: true,
            salt: 'new_salt',
          ),
        );
        
        final existingUserResponse = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(
            userId: 67890,
            newDomain: false,
            salt: 'existing_salt',
          ),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: newUserRequest))
            .thenAnswer((_) async => newUserResponse);
        when(mockAuthRepository.checkEmail(checkEmailRequest: existingUserRequest))
            .thenAnswer((_) async => existingUserResponse);

        // Act
        final newUserResult = await authCheckMailUseCase.buildUseCase(newUserRequest);
        final existingUserResult = await authCheckMailUseCase.buildUseCase(existingUserRequest);

        // Assert - Verify business logic handling
        // New user scenario
        expect(newUserResult.data.newDomain, true);
        expect(newUserResult.data.userId, 12345);
        expect(newUserResult.data.salt, 'new_salt');
        
        // Existing user scenario
        expect(existingUserResult.data.newDomain, false);
        expect(existingUserResult.data.userId, 67890);
        expect(existingUserResult.data.salt, 'existing_salt');
        
        // Verify both repository calls were made
        verify(mockAuthRepository.checkEmail(checkEmailRequest: newUserRequest)).called(1);
        verify(mockAuthRepository.checkEmail(checkEmailRequest: existingUserRequest)).called(1);
      });

      test('should propagate errors correctly through domain layers', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final dioException = DioException(
          requestOptions: RequestOptions(path: ''),
          type: DioExceptionType.connectionTimeout,
          message: 'Connection timeout',
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenThrow(dioException);

        // Act & Assert
        expect(
          () async => await authCheckMailUseCase.buildUseCase(request),
          throwsA(isA<DioException>()),
        );
        
        // Verify error propagation through layers
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle concurrent domain operations correctly', () async {
        // Arrange
        final requests = [
          AuthCheckEmailRequest('<EMAIL>', '+***********'),
          AuthCheckEmailRequest('<EMAIL>', '+***********'),
          AuthCheckEmailRequest('<EMAIL>', '+84333333333'),
        ];
        
        final responses = requests.asMap().entries.map((entry) {
          return ApiResponseV2<AuthCheckMailResponse>(
            status: 'success',
            data: AuthCheckMailResponse(
              userId: entry.key + 1,
              newDomain: entry.key % 2 == 0,
              salt: 'salt_${entry.key + 1}',
            ),
          );
        }).toList();
        
        // Setup mocks for each request
        for (int i = 0; i < requests.length; i++) {
          when(mockAuthRepository.checkEmail(checkEmailRequest: requests[i]))
              .thenAnswer((_) async => responses[i]);
        }

        // Act
        final futures = requests.map((request) => 
            authCheckMailUseCase.buildUseCase(request)).toList();
        final results = await Future.wait(futures);

        // Assert
        for (int i = 0; i < results.length; i++) {
          expect(results[i].status, 'success');
          expect(results[i].data.userId, i + 1);
          expect(results[i].data.newDomain, i % 2 == 0);
          expect(results[i].data.salt, 'salt_${i + 1}');
          
          // Verify each repository call was made
          verify(mockAuthRepository.checkEmail(checkEmailRequest: requests[i])).called(1);
        }
      });
    });

    group('Domain Entity Integration', () {
      test('should work with domain entities correctly', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final response = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => response);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Assert - Verify domain entity can be created from response
        final domainEntity = AuthCheckMailEntity(
          userId: result.data.userId,
          newDomain: result.data.newDomain,
        );
        
        expect(domainEntity.userId, TestConstants.testUserId);
        expect(domainEntity.newDomain, TestConstants.testNewDomain);
      });

      test('should handle entity transformations correctly', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final response = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(
            userId: 999,
            newDomain: null, // Test null handling
            salt: 'test_salt',
          ),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => response);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Transform to domain entity
        final domainEntity = AuthCheckMailEntity(
          userId: result.data.userId,
          newDomain: result.data.newDomain,
        );

        // Assert
        expect(domainEntity.userId, 999);
        expect(domainEntity.newDomain, isNull);
      });
    });

    group('Error Handling Integration', () {
      test('should handle repository errors gracefully', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final errorResponse = TestConstants.testErrorApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => errorResponse);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Assert
        expect(result.status, 'error');
        expect(result.data.userId, -1);
        
        // Verify error was handled at domain level
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle various exception types in domain layer', () async {
        final request = TestConstants.testAuthCheckEmailRequest;
        
        final exceptions = [
          DioException(requestOptions: RequestOptions(path: ''), type: DioExceptionType.connectionTimeout),
          DioException(requestOptions: RequestOptions(path: ''), type: DioExceptionType.receiveTimeout),
          Exception('Generic domain error'),
          ArgumentError('Invalid domain argument'),
        ];

        for (final exception in exceptions) {
          when(mockAuthRepository.checkEmail(checkEmailRequest: request))
              .thenThrow(exception);

          expect(
            () async => await authCheckMailUseCase.buildUseCase(request),
            throwsA(isA<Object>()),
          );
        }
      });
    });

    group('Performance and Reliability', () {
      test('should handle high-frequency domain operations', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final response = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => response);

        // Act - Simulate high-frequency calls
        final futures = List.generate(10, (index) => 
            authCheckMailUseCase.buildUseCase(request));
        final results = await Future.wait(futures);

        // Assert
        expect(results.length, 10);
        for (final result in results) {
          expect(result.status, 'success');
          expect(result.data.userId, TestConstants.testUserId);
        }
        
        // Verify all calls were made
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(10);
      });

      test('should maintain state isolation between calls', () async {
        // Arrange
        final request1 = AuthCheckEmailRequest('<EMAIL>', '+***********');
        final request2 = AuthCheckEmailRequest('<EMAIL>', '+***********');
        
        final response1 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 1, newDomain: true),
        );
        final response2 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 2, newDomain: false),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request1))
            .thenAnswer((_) async => response1);
        when(mockAuthRepository.checkEmail(checkEmailRequest: request2))
            .thenAnswer((_) async => response2);

        // Act
        final result1 = await authCheckMailUseCase.buildUseCase(request1);
        final result2 = await authCheckMailUseCase.buildUseCase(request2);

        // Assert - Verify state isolation
        expect(result1.data.userId, 1);
        expect(result1.data.newDomain, true);
        
        expect(result2.data.userId, 2);
        expect(result2.data.newDomain, false);
        
        // Results should not affect each other
        expect(result1.data.userId, isNot(result2.data.userId));
        expect(result1.data.newDomain, isNot(result2.data.newDomain));
      });
    });
  });
}
