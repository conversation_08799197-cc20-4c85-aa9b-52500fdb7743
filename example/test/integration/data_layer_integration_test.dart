import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import 'package:example/data/repository/auth_repo_impl.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';

import '../helpers/test_constants.dart';
import '../helpers/test_helper.mocks.dart';

void main() {
  group('Data Layer Integration Tests', () {
    late MockAuthService mockAuthService;
    late AuthRepositoryImpl authRepository;

    setUp(() {
      mockAuthService = MockAuthService();
      authRepository = AuthRepositoryImpl(mockAuthService);
    });

    group('Auth Check Email Flow', () {
      test('should complete full flow from repository to service successfully', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;

        when(mockAuthService.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act
        final result = await authRepository.checkEmail(checkEmailRequest: request);

        // Assert
        expect(result.status, 'success');
        expect(result.data, isNotNull);
        expect(result.data.userId, TestConstants.testUserId);
        expect(result.data.newDomain, TestConstants.testNewDomain);
        expect(result.data.salt, TestConstants.testSalt);

        // Verify the service was called correctly
        verify(mockAuthService.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle service error response correctly', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final errorResponse = TestConstants.testErrorApiResponse;

        when(mockAuthService.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => errorResponse);

        // Act
        final result = await authRepository.checkEmail(checkEmailRequest: request);

        // Assert
        expect(result.status, 'error');
        expect(result.data, isNotNull);
        expect(result.data.userId, -1);

        // Verify the service was called
        verify(mockAuthService.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle service exception correctly', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final exception = DioException(
          requestOptions: RequestOptions(path: ''),
          type: DioExceptionType.connectionTimeout,
        );

        when(mockAuthService.checkEmail(checkEmailRequest: request))
            .thenThrow(exception);

        // Act & Assert
        expect(
          () async => await authRepository.checkEmail(checkEmailRequest: request),
          throwsA(isA<DioException>()),
        );

        // Verify the service was called
        verify(mockAuthService.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle different request data correctly', () async {
        // Arrange
        final request = AuthCheckEmailRequest('<EMAIL>', '+84987654321');
        final customResponse = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(
            userId: 12345,
            newDomain: true,
            salt: 'test_salt',
          ),
        );

        when(mockAuthService.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => customResponse);

        // Act
        final result = await authRepository.checkEmail(checkEmailRequest: request);

        // Assert
        expect(result.status, 'success');
        expect(result.data.userId, 12345);
        expect(result.data.newDomain, true);
        expect(result.data.salt, 'test_salt');

        // Verify the service was called with correct request
        verify(mockAuthService.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle concurrent requests correctly', () async {
        // Arrange
        final request1 = AuthCheckEmailRequest('<EMAIL>', '+84111111111');
        final request2 = AuthCheckEmailRequest('<EMAIL>', '+84222222222');

        final response1 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 1, newDomain: false, salt: 'salt1'),
        );
        final response2 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 2, newDomain: true, salt: 'salt2'),
        );

        when(mockAuthService.checkEmail(checkEmailRequest: request1))
            .thenAnswer((_) async => response1);
        when(mockAuthService.checkEmail(checkEmailRequest: request2))
            .thenAnswer((_) async => response2);

        // Act
        final futures = [
          authRepository.checkEmail(checkEmailRequest: request1),
          authRepository.checkEmail(checkEmailRequest: request2),
        ];
        final results = await Future.wait(futures);

        // Assert
        expect(results[0].data.userId, 1);
        expect(results[0].data.newDomain, false);
        expect(results[0].data.salt, 'salt1');

        expect(results[1].data.userId, 2);
        expect(results[1].data.newDomain, true);
        expect(results[1].data.salt, 'salt2');

        // Verify both services were called
        verify(mockAuthService.checkEmail(checkEmailRequest: request1)).called(1);
        verify(mockAuthService.checkEmail(checkEmailRequest: request2)).called(1);
      });
    });
  });
}
