import 'package:flutter_test/flutter_test.dart';
import 'package:example/constants/url/url.constants.dart';

void main() {
  group('UrlConstants', () {
    group('API Endpoints', () {
      test('should have correct auth API path', () {
        // Assert
        expect(UrlConstants.kAuthAPI, '/auth/v3.1');
      });

      test('should have correct check email path', () {
        // Assert
        expect(UrlConstants.kCheckEmailPath, '/check-email');
      });
    });

    group('Path Validation', () {
      test('auth API path should start with slash', () {
        // Assert
        expect(UrlConstants.kAuthAPI.startsWith('/'), isTrue);
      });

      test('check email path should start with slash', () {
        // Assert
        expect(UrlConstants.kCheckEmailPath.startsWith('/'), isTrue);
      });

      test('auth API path should contain version', () {
        // Assert
        expect(UrlConstants.kAuthAPI.contains('v3.1'), isTrue);
      });

      test('check email path should be kebab-case', () {
        // Assert
        expect(UrlConstants.kCheckEmailPath, '/check-email');
        expect(UrlConstants.kCheckEmailPath.contains('-'), isTrue);
      });
    });

    group('Path Composition', () {
      test('should compose full auth check email URL correctly', () {
        // Arrange
        const baseUrl = 'https://api.example.com';
        
        // Act
        final fullUrl = baseUrl + UrlConstants.kAuthAPI + UrlConstants.kCheckEmailPath;
        
        // Assert
        expect(fullUrl, 'https://api.example.com/auth/v3.1/check-email');
      });

      test('should handle URL composition with different base URLs', () {
        // Arrange
        final testCases = [
          {
            'baseUrl': 'https://api.example.com',
            'expected': 'https://api.example.com/auth/v3.1/check-email'
          },
          {
            'baseUrl': 'http://localhost:8080',
            'expected': 'http://localhost:8080/auth/v3.1/check-email'
          },
          {
            'baseUrl': 'https://staging.api.example.com',
            'expected': 'https://staging.api.example.com/auth/v3.1/check-email'
          },
        ];

        for (final testCase in testCases) {
          // Act
          final fullUrl = testCase['baseUrl']! + 
                         UrlConstants.kAuthAPI + 
                         UrlConstants.kCheckEmailPath;
          
          // Assert
          expect(fullUrl, testCase['expected']);
        }
      });
    });

    group('Constants Properties', () {
      test('should be compile-time constants', () {
        // Assert - These should be compile-time constants
        const authAPI = UrlConstants.kAuthAPI;
        const checkEmailPath = UrlConstants.kCheckEmailPath;
        
        expect(authAPI, isA<String>());
        expect(checkEmailPath, isA<String>());
      });

      test('should be immutable', () {
        // Arrange
        final originalAuthAPI = UrlConstants.kAuthAPI;
        final originalCheckEmailPath = UrlConstants.kCheckEmailPath;
        
        // Act - Try to access multiple times
        final authAPI1 = UrlConstants.kAuthAPI;
        final authAPI2 = UrlConstants.kAuthAPI;
        final checkEmailPath1 = UrlConstants.kCheckEmailPath;
        final checkEmailPath2 = UrlConstants.kCheckEmailPath;
        
        // Assert - Should always return same values
        expect(authAPI1, originalAuthAPI);
        expect(authAPI2, originalAuthAPI);
        expect(checkEmailPath1, originalCheckEmailPath);
        expect(checkEmailPath2, originalCheckEmailPath);
      });

      test('should have non-empty values', () {
        // Assert
        expect(UrlConstants.kAuthAPI.isNotEmpty, isTrue);
        expect(UrlConstants.kCheckEmailPath.isNotEmpty, isTrue);
      });

      test('should not contain whitespace', () {
        // Assert
        expect(UrlConstants.kAuthAPI.trim(), UrlConstants.kAuthAPI);
        expect(UrlConstants.kCheckEmailPath.trim(), UrlConstants.kCheckEmailPath);
        expect(UrlConstants.kAuthAPI.contains(' '), isFalse);
        expect(UrlConstants.kCheckEmailPath.contains(' '), isFalse);
      });
    });

    group('API Versioning', () {
      test('should use correct API version format', () {
        // Assert
        expect(UrlConstants.kAuthAPI.contains('v3.1'), isTrue);
        
        // Extract version part
        final versionPart = UrlConstants.kAuthAPI.split('/').last;
        expect(versionPart, 'v3.1');
      });

      test('should follow semantic versioning pattern', () {
        // Arrange
        final versionRegex = RegExp(r'v\d+\.\d+');
        
        // Assert
        expect(versionRegex.hasMatch(UrlConstants.kAuthAPI), isTrue);
      });
    });

    group('Path Structure', () {
      test('should have correct path segments', () {
        // Arrange
        final authAPISegments = UrlConstants.kAuthAPI.split('/');
        
        // Assert
        expect(authAPISegments.length, 3); // ['', 'auth', 'v3.1']
        expect(authAPISegments[0], ''); // Leading slash creates empty string
        expect(authAPISegments[1], 'auth');
        expect(authAPISegments[2], 'v3.1');
      });

      test('should have single path segment for check email', () {
        // Arrange
        final checkEmailSegments = UrlConstants.kCheckEmailPath.split('/');
        
        // Assert
        expect(checkEmailSegments.length, 2); // ['', 'check-email']
        expect(checkEmailSegments[0], ''); // Leading slash creates empty string
        expect(checkEmailSegments[1], 'check-email');
      });
    });

    group('Edge Cases', () {
      test('should handle string concatenation correctly', () {
        // Act
        final concatenated = UrlConstants.kAuthAPI + UrlConstants.kCheckEmailPath;
        
        // Assert
        expect(concatenated, '/auth/v3.1/check-email');
      });

      test('should handle multiple concatenations', () {
        // Act
        final result = UrlConstants.kAuthAPI + 
                      UrlConstants.kCheckEmailPath + 
                      '?param=value';
        
        // Assert
        expect(result, '/auth/v3.1/check-email?param=value');
      });

      test('should work with string interpolation', () {
        // Act
        final interpolated = '${UrlConstants.kAuthAPI}${UrlConstants.kCheckEmailPath}';
        
        // Assert
        expect(interpolated, '/auth/v3.1/check-email');
      });
    });

    group('Performance', () {
      test('should access constants efficiently', () {
        // Arrange
        final stopwatch = Stopwatch()..start();
        
        // Act
        for (int i = 0; i < 10000; i++) {
          UrlConstants.kAuthAPI;
          UrlConstants.kCheckEmailPath;
        }
        
        stopwatch.stop();
        
        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(10));
      });

      test('should handle rapid string operations', () {
        // Arrange
        final stopwatch = Stopwatch()..start();
        
        // Act
        for (int i = 0; i < 1000; i++) {
          UrlConstants.kAuthAPI + UrlConstants.kCheckEmailPath;
        }
        
        stopwatch.stop();
        
        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(50));
      });
    });

    group('Type Safety', () {
      test('should be String type', () {
        // Assert
        expect(UrlConstants.kAuthAPI, isA<String>());
        expect(UrlConstants.kCheckEmailPath, isA<String>());
      });

      test('should maintain type consistency', () {
        // Act
        final authAPI = UrlConstants.kAuthAPI;
        final checkEmailPath = UrlConstants.kCheckEmailPath;
        
        // Assert
        expect(authAPI.runtimeType, String);
        expect(checkEmailPath.runtimeType, String);
      });
    });

    group('Documentation Compliance', () {
      test('should follow naming conventions', () {
        // Assert - Constants should start with 'k' prefix
        expect(UrlConstants.kAuthAPI.startsWith('k'), isFalse); // This is the value, not the name
        expect(UrlConstants.kCheckEmailPath.startsWith('k'), isFalse); // This is the value, not the name
        
        // The constant names themselves follow the convention (kAuthAPI, kCheckEmailPath)
        // We can't test the names directly, but we can verify the values are correct
        expect(UrlConstants.kAuthAPI, '/auth/v3.1');
        expect(UrlConstants.kCheckEmailPath, '/check-email');
      });

      test('should be RESTful path format', () {
        // Assert
        expect(UrlConstants.kCheckEmailPath.toLowerCase(), UrlConstants.kCheckEmailPath);
        expect(UrlConstants.kCheckEmailPath.contains('_'), isFalse); // Should use kebab-case
      });
    });
  });
}
