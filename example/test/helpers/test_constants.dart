import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';

class TestConstants {
  // Test data for AuthCheckEmailRequest
  static const testEmail = '<EMAIL>';
  static const testPhoneNumber = '+84123456789';

  static AuthCheckEmailRequest get testAuthCheckEmailRequest =>
      AuthCheckEmailRequest(testEmail, testPhoneNumber);

  // Test data for AuthCheckMailResponse
  static const testUserId = 105883395;
  static const testNewDomain = false;
  static const testSalt = 'SnYf6x';

  static AuthCheckMailResponse get testAuthCheckMailResponse =>
      AuthCheckMailResponse(
        userId: testUserId,
        newDomain: testNewDomain,
        salt: testSalt,
      );

  // Test API Response
  static ApiResponseV2<AuthCheckMailResponse> get testSuccessApiResponse =>
      ApiResponseV2<AuthCheckMailResponse>(
        status: 'success',
        data: testAuthCheckMailResponse,
      );

  static AuthCheckMailResponse get testErrorAuthCheckMailResponse =>
      AuthCheckMailResponse(userId: -1);

  static ApiResponseV2<AuthCheckMailResponse> get testErrorApiResponse =>
      ApiResponseV2<AuthCheckMailResponse>(
        status: 'error',
        data: testErrorAuthCheckMailResponse,
      );

  // Mock JSON responses
  static Map<String, dynamic> get mockSuccessResponseJson => {
        'code': 200,
        'message': 'Đã xử lý thành công!',
        'data': {
          'user_id': testUserId,
          'new_domain': testNewDomain,
          'salt': testSalt,
        }
      };

  static Map<String, dynamic> get mockErrorResponseJson => {
        'code': 400,
        'message': 'Có lỗi xảy ra!',
        'data': null,
      };
}
