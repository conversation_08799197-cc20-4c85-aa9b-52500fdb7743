// Mocks generated by Mockito 5.4.5 from annotations
// in example/test/helpers/test_helper.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i14;
import 'dart:ui' as _i24;

import 'package:dio/dio.dart' as _i7;
import 'package:dio/src/adapter.dart' as _i3;
import 'package:dio/src/cancel_token.dart' as _i15;
import 'package:dio/src/dio_mixin.dart' as _i5;
import 'package:dio/src/options.dart' as _i2;
import 'package:dio/src/response.dart' as _i6;
import 'package:dio/src/transformer.dart' as _i4;
import 'package:example/data/data_source/remote/auth.service.dart' as _i16;
import 'package:example/data/model/auth/request/auth_check_email_request.dart'
    as _i18;
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart'
    as _i17;
import 'package:example/domain/repository/auth_repo.dart' as _i19;
import 'package:example/domain/usecase/auth_check_mail.usecase.dart' as _i20;
import 'package:flutter/material.dart' as _i21;
import 'package:flutter/rendering.dart' as _i25;
import 'package:flutter/src/widgets/basic.dart' as _i27;
import 'package:flutter/src/widgets/framework.dart' as _i9;
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart'
    as _i22;
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_popup_navigator.dart'
    as _i12;
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_snackbar_navigator.dart'
    as _i13;
import 'package:gp_core_v2/base/navigator/mapper/base_route_info_mapper.dart'
    as _i11;
import 'package:gp_core_v2/base/navigator/model/popup/app_popup_info.dart'
    as _i23;
import 'package:gp_core_v2/base/navigator/model/route/app_route_info.dart'
    as _i10;
import 'package:gp_core_v2/base/navigator/model/snackbar/app_snackbar_info.dart'
    as _i26;
import 'package:gp_core_v2/base/usecase/model/model.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeBaseOptions_0 extends _i1.SmartFake implements _i2.BaseOptions {
  _FakeBaseOptions_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHttpClientAdapter_1 extends _i1.SmartFake
    implements _i3.HttpClientAdapter {
  _FakeHttpClientAdapter_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTransformer_2 extends _i1.SmartFake implements _i4.Transformer {
  _FakeTransformer_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeInterceptors_3 extends _i1.SmartFake implements _i5.Interceptors {
  _FakeInterceptors_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponse_4<T1> extends _i1.SmartFake implements _i6.Response<T1> {
  _FakeResponse_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDio_5 extends _i1.SmartFake implements _i7.Dio {
  _FakeDio_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeApiResponseV2_6<T> extends _i1.SmartFake
    implements _i8.ApiResponseV2<T> {
  _FakeApiResponseV2_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeException_7 extends _i1.SmartFake implements Exception {
  _FakeException_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGlobalKey_8<T extends _i9.State<_i9.StatefulWidget>>
    extends _i1.SmartFake
    implements _i9.GlobalKey<T> {
  _FakeGlobalKey_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGPBaseRouteInfoMapper_9<T, R extends _i10.GPAppRouteInfo>
    extends _i1.SmartFake
    implements _i11.GPBaseRouteInfoMapper<T, R> {
  _FakeGPBaseRouteInfoMapper_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGPAppPopupNavigator_10 extends _i1.SmartFake
    implements _i12.GPAppPopupNavigator {
  _FakeGPAppPopupNavigator_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGPAppSnackbarNavigator_11 extends _i1.SmartFake
    implements _i13.GPAppSnackbarNavigator {
  _FakeGPAppSnackbarNavigator_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [Dio].
///
/// See the documentation for Mockito's code generation for more information.
class MockDio extends _i1.Mock implements _i7.Dio {
  MockDio() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.BaseOptions get options =>
      (super.noSuchMethod(
            Invocation.getter(#options),
            returnValue: _FakeBaseOptions_0(this, Invocation.getter(#options)),
          )
          as _i2.BaseOptions);

  @override
  set options(_i2.BaseOptions? _options) => super.noSuchMethod(
    Invocation.setter(#options, _options),
    returnValueForMissingStub: null,
  );

  @override
  _i3.HttpClientAdapter get httpClientAdapter =>
      (super.noSuchMethod(
            Invocation.getter(#httpClientAdapter),
            returnValue: _FakeHttpClientAdapter_1(
              this,
              Invocation.getter(#httpClientAdapter),
            ),
          )
          as _i3.HttpClientAdapter);

  @override
  set httpClientAdapter(_i3.HttpClientAdapter? _httpClientAdapter) =>
      super.noSuchMethod(
        Invocation.setter(#httpClientAdapter, _httpClientAdapter),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Transformer get transformer =>
      (super.noSuchMethod(
            Invocation.getter(#transformer),
            returnValue: _FakeTransformer_2(
              this,
              Invocation.getter(#transformer),
            ),
          )
          as _i4.Transformer);

  @override
  set transformer(_i4.Transformer? _transformer) => super.noSuchMethod(
    Invocation.setter(#transformer, _transformer),
    returnValueForMissingStub: null,
  );

  @override
  _i5.Interceptors get interceptors =>
      (super.noSuchMethod(
            Invocation.getter(#interceptors),
            returnValue: _FakeInterceptors_3(
              this,
              Invocation.getter(#interceptors),
            ),
          )
          as _i5.Interceptors);

  @override
  void close({bool? force = false}) => super.noSuchMethod(
    Invocation.method(#close, [], {#force: force}),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<_i6.Response<T>> head<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #head,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #head,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> headUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #headUri,
              [uri],
              {#data: data, #options: options, #cancelToken: cancelToken},
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #headUri,
                  [uri],
                  {#data: data, #options: options, #cancelToken: cancelToken},
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> get<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #get,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #get,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> getUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #getUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #getUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> post<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #post,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #post,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> postUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #postUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #postUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> put<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #put,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #put,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> putUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #putUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #putUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> patch<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patch,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #patch,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> patchUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #patchUri,
              [uri],
              {
                #data: data,
                #options: options,
                #cancelToken: cancelToken,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #patchUri,
                  [uri],
                  {
                    #data: data,
                    #options: options,
                    #cancelToken: cancelToken,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> delete<T>(
    String? path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #delete,
              [path],
              {
                #data: data,
                #queryParameters: queryParameters,
                #options: options,
                #cancelToken: cancelToken,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #delete,
                  [path],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #options: options,
                    #cancelToken: cancelToken,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> deleteUri<T>(
    Uri? uri, {
    Object? data,
    _i2.Options? options,
    _i15.CancelToken? cancelToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #deleteUri,
              [uri],
              {#data: data, #options: options, #cancelToken: cancelToken},
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #deleteUri,
                  [uri],
                  {#data: data, #options: options, #cancelToken: cancelToken},
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<dynamic>> download(
    String? urlPath,
    dynamic savePath, {
    _i2.ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    _i15.CancelToken? cancelToken,
    bool? deleteOnError = true,
    _i2.FileAccessMode? fileAccessMode = _i2.FileAccessMode.write,
    String? lengthHeader = 'content-length',
    Object? data,
    _i2.Options? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #download,
              [urlPath, savePath],
              {
                #onReceiveProgress: onReceiveProgress,
                #queryParameters: queryParameters,
                #cancelToken: cancelToken,
                #deleteOnError: deleteOnError,
                #fileAccessMode: fileAccessMode,
                #lengthHeader: lengthHeader,
                #data: data,
                #options: options,
              },
            ),
            returnValue: _i14.Future<_i6.Response<dynamic>>.value(
              _FakeResponse_4<dynamic>(
                this,
                Invocation.method(
                  #download,
                  [urlPath, savePath],
                  {
                    #onReceiveProgress: onReceiveProgress,
                    #queryParameters: queryParameters,
                    #cancelToken: cancelToken,
                    #deleteOnError: deleteOnError,
                    #fileAccessMode: fileAccessMode,
                    #lengthHeader: lengthHeader,
                    #data: data,
                    #options: options,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<dynamic>>);

  @override
  _i14.Future<_i6.Response<dynamic>> downloadUri(
    Uri? uri,
    dynamic savePath, {
    _i2.ProgressCallback? onReceiveProgress,
    _i15.CancelToken? cancelToken,
    bool? deleteOnError = true,
    _i2.FileAccessMode? fileAccessMode = _i2.FileAccessMode.write,
    String? lengthHeader = 'content-length',
    Object? data,
    _i2.Options? options,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #downloadUri,
              [uri, savePath],
              {
                #onReceiveProgress: onReceiveProgress,
                #cancelToken: cancelToken,
                #deleteOnError: deleteOnError,
                #fileAccessMode: fileAccessMode,
                #lengthHeader: lengthHeader,
                #data: data,
                #options: options,
              },
            ),
            returnValue: _i14.Future<_i6.Response<dynamic>>.value(
              _FakeResponse_4<dynamic>(
                this,
                Invocation.method(
                  #downloadUri,
                  [uri, savePath],
                  {
                    #onReceiveProgress: onReceiveProgress,
                    #cancelToken: cancelToken,
                    #deleteOnError: deleteOnError,
                    #fileAccessMode: fileAccessMode,
                    #lengthHeader: lengthHeader,
                    #data: data,
                    #options: options,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<dynamic>>);

  @override
  _i14.Future<_i6.Response<T>> request<T>(
    String? url, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    _i15.CancelToken? cancelToken,
    _i2.Options? options,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #request,
              [url],
              {
                #data: data,
                #queryParameters: queryParameters,
                #cancelToken: cancelToken,
                #options: options,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #request,
                  [url],
                  {
                    #data: data,
                    #queryParameters: queryParameters,
                    #cancelToken: cancelToken,
                    #options: options,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> requestUri<T>(
    Uri? uri, {
    Object? data,
    _i15.CancelToken? cancelToken,
    _i2.Options? options,
    _i2.ProgressCallback? onSendProgress,
    _i2.ProgressCallback? onReceiveProgress,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #requestUri,
              [uri],
              {
                #data: data,
                #cancelToken: cancelToken,
                #options: options,
                #onSendProgress: onSendProgress,
                #onReceiveProgress: onReceiveProgress,
              },
            ),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(
                  #requestUri,
                  [uri],
                  {
                    #data: data,
                    #cancelToken: cancelToken,
                    #options: options,
                    #onSendProgress: onSendProgress,
                    #onReceiveProgress: onReceiveProgress,
                  },
                ),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i14.Future<_i6.Response<T>> fetch<T>(_i2.RequestOptions? requestOptions) =>
      (super.noSuchMethod(
            Invocation.method(#fetch, [requestOptions]),
            returnValue: _i14.Future<_i6.Response<T>>.value(
              _FakeResponse_4<T>(
                this,
                Invocation.method(#fetch, [requestOptions]),
              ),
            ),
          )
          as _i14.Future<_i6.Response<T>>);

  @override
  _i7.Dio clone({
    _i2.BaseOptions? options,
    _i5.Interceptors? interceptors,
    _i3.HttpClientAdapter? httpClientAdapter,
    _i4.Transformer? transformer,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#clone, [], {
              #options: options,
              #interceptors: interceptors,
              #httpClientAdapter: httpClientAdapter,
              #transformer: transformer,
            }),
            returnValue: _FakeDio_5(
              this,
              Invocation.method(#clone, [], {
                #options: options,
                #interceptors: interceptors,
                #httpClientAdapter: httpClientAdapter,
                #transformer: transformer,
              }),
            ),
          )
          as _i7.Dio);
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i16.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>> checkEmail({
    required _i18.AuthCheckEmailRequest? checkEmailRequest,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#checkEmail, [], {
              #checkEmailRequest: checkEmailRequest,
            }),
            returnValue: _i14.Future<
              _i8.ApiResponseV2<_i17.AuthCheckMailResponse>
            >.value(
              _FakeApiResponseV2_6<_i17.AuthCheckMailResponse>(
                this,
                Invocation.method(#checkEmail, [], {
                  #checkEmailRequest: checkEmailRequest,
                }),
              ),
            ),
          )
          as _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>>);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i19.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>> checkEmail({
    required _i18.AuthCheckEmailRequest? checkEmailRequest,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#checkEmail, [], {
              #checkEmailRequest: checkEmailRequest,
            }),
            returnValue: _i14.Future<
              _i8.ApiResponseV2<_i17.AuthCheckMailResponse>
            >.value(
              _FakeApiResponseV2_6<_i17.AuthCheckMailResponse>(
                this,
                Invocation.method(#checkEmail, [], {
                  #checkEmailRequest: checkEmailRequest,
                }),
              ),
            ),
          )
          as _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>>);
}

/// A class which mocks [AuthCheckMailUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthCheckMailUseCase extends _i1.Mock
    implements _i20.AuthCheckMailUseCase {
  MockAuthCheckMailUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>> buildUseCase(
    _i18.AuthCheckEmailRequest? input,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#buildUseCase, [input]),
            returnValue: _i14.Future<
              _i8.ApiResponseV2<_i17.AuthCheckMailResponse>
            >.value(
              _FakeApiResponseV2_6<_i17.AuthCheckMailResponse>(
                this,
                Invocation.method(#buildUseCase, [input]),
              ),
            ),
          )
          as _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>>);

  @override
  _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>> execute(
    _i18.AuthCheckEmailRequest? input,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#execute, [input]),
            returnValue: _i14.Future<
              _i8.ApiResponseV2<_i17.AuthCheckMailResponse>
            >.value(
              _FakeApiResponseV2_6<_i17.AuthCheckMailResponse>(
                this,
                Invocation.method(#execute, [input]),
              ),
            ),
          )
          as _i14.Future<_i8.ApiResponseV2<_i17.AuthCheckMailResponse>>);

  @override
  Exception mapException(Object? e) =>
      (super.noSuchMethod(
            Invocation.method(#mapException, [e]),
            returnValue: _FakeException_7(
              this,
              Invocation.method(#mapException, [e]),
            ),
          )
          as Exception);

  @override
  void logD(String? message, {DateTime? time}) => super.noSuchMethod(
    Invocation.method(#logD, [message], {#time: time}),
    returnValueForMissingStub: null,
  );

  @override
  void logE(String? message, {DateTime? time}) => super.noSuchMethod(
    Invocation.method(#logE, [message], {#time: time}),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [NavigatorObserver].
///
/// See the documentation for Mockito's code generation for more information.
class MockNavigatorObserver extends _i1.Mock implements _i21.NavigatorObserver {
  MockNavigatorObserver() {
    _i1.throwOnMissingStub(this);
  }

  @override
  void didPush(
    _i21.Route<dynamic>? route,
    _i21.Route<dynamic>? previousRoute,
  ) => super.noSuchMethod(
    Invocation.method(#didPush, [route, previousRoute]),
    returnValueForMissingStub: null,
  );

  @override
  void didPop(_i21.Route<dynamic>? route, _i21.Route<dynamic>? previousRoute) =>
      super.noSuchMethod(
        Invocation.method(#didPop, [route, previousRoute]),
        returnValueForMissingStub: null,
      );

  @override
  void didRemove(
    _i21.Route<dynamic>? route,
    _i21.Route<dynamic>? previousRoute,
  ) => super.noSuchMethod(
    Invocation.method(#didRemove, [route, previousRoute]),
    returnValueForMissingStub: null,
  );

  @override
  void didReplace({
    _i21.Route<dynamic>? newRoute,
    _i21.Route<dynamic>? oldRoute,
  }) => super.noSuchMethod(
    Invocation.method(#didReplace, [], {
      #newRoute: newRoute,
      #oldRoute: oldRoute,
    }),
    returnValueForMissingStub: null,
  );

  @override
  void didChangeTop(
    _i21.Route<dynamic>? topRoute,
    _i21.Route<dynamic>? previousTopRoute,
  ) => super.noSuchMethod(
    Invocation.method(#didChangeTop, [topRoute, previousTopRoute]),
    returnValueForMissingStub: null,
  );

  @override
  void didStartUserGesture(
    _i21.Route<dynamic>? route,
    _i21.Route<dynamic>? previousRoute,
  ) => super.noSuchMethod(
    Invocation.method(#didStartUserGesture, [route, previousRoute]),
    returnValueForMissingStub: null,
  );

  @override
  void didStopUserGesture() => super.noSuchMethod(
    Invocation.method(#didStopUserGesture, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [GPAppNavigator].
///
/// See the documentation for Mockito's code generation for more information.
class MockGPAppNavigator<I extends _i10.GPAppRouteInfo> extends _i1.Mock
    implements _i22.GPAppNavigator<I> {
  MockGPAppNavigator() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i9.GlobalKey<_i21.NavigatorState> get navigatorKey =>
      (super.noSuchMethod(
            Invocation.getter(#navigatorKey),
            returnValue: _FakeGlobalKey_8<_i21.NavigatorState>(
              this,
              Invocation.getter(#navigatorKey),
            ),
          )
          as _i9.GlobalKey<_i21.NavigatorState>);

  @override
  _i11.GPBaseRouteInfoMapper<dynamic, _i10.GPAppRouteInfo>
  get routeInfoMapper =>
      (super.noSuchMethod(
            Invocation.getter(#routeInfoMapper),
            returnValue:
                _FakeGPBaseRouteInfoMapper_9<dynamic, _i10.GPAppRouteInfo>(
                  this,
                  Invocation.getter(#routeInfoMapper),
                ),
          )
          as _i11.GPBaseRouteInfoMapper<dynamic, _i10.GPAppRouteInfo>);

  @override
  _i12.GPAppPopupNavigator get appPopupNavigator =>
      (super.noSuchMethod(
            Invocation.getter(#appPopupNavigator),
            returnValue: _FakeGPAppPopupNavigator_10(
              this,
              Invocation.getter(#appPopupNavigator),
            ),
          )
          as _i12.GPAppPopupNavigator);

  @override
  _i13.GPAppSnackbarNavigator get appSnackbarNavigator =>
      (super.noSuchMethod(
            Invocation.getter(#appSnackbarNavigator),
            returnValue: _FakeGPAppSnackbarNavigator_11(
              this,
              Invocation.getter(#appSnackbarNavigator),
            ),
          )
          as _i13.GPAppSnackbarNavigator);

  @override
  bool canPop(_i9.BuildContext? context) =>
      (super.noSuchMethod(
            Invocation.method(#canPop, [context]),
            returnValue: false,
          )
          as bool);

  @override
  void pop<T extends Object?>(
    _i9.BuildContext? context, {
    T? result,
    bool? useRootNavigator = false,
  }) => super.noSuchMethod(
    Invocation.method(
      #pop,
      [context],
      {#result: result, #useRootNavigator: useRootNavigator},
    ),
    returnValueForMissingStub: null,
  );

  @override
  void popUntil<T extends Object?>(
    _i9.BuildContext? context,
    I? appRouteInfo,
  ) => super.noSuchMethod(
    Invocation.method(#popUntil, [context, appRouteInfo]),
    returnValueForMissingStub: null,
  );

  @override
  _i14.Future<T?> popAndPush<T extends Object?, R extends Object?>(
    _i9.BuildContext? context,
    I? appRouteInfo, {
    R? result,
    bool? useRootNavigator = false,
    Object? arguments,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #popAndPush,
              [context, appRouteInfo],
              {
                #result: result,
                #useRootNavigator: useRootNavigator,
                #arguments: arguments,
              },
            ),
            returnValue: _i14.Future<T?>.value(),
          )
          as _i14.Future<T?>);

  @override
  _i14.Future<T?> push<T extends Object?, R extends Object?>(
    _i9.BuildContext? context,
    I? appRouteInfo, {
    R? result,
    bool? useRootNavigator = false,
    Object? arguments,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #push,
              [context, appRouteInfo],
              {
                #result: result,
                #useRootNavigator: useRootNavigator,
                #arguments: arguments,
              },
            ),
            returnValue: _i14.Future<T?>.value(),
          )
          as _i14.Future<T?>);

  @override
  _i14.Future<T?> replace<T extends Object?>(
    _i9.BuildContext? context,
    I? appRouteInfo, {
    Object? arguments,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #replace,
              [context, appRouteInfo],
              {#arguments: arguments},
            ),
            returnValue: _i14.Future<T?>.value(),
          )
          as _i14.Future<T?>);

  @override
  Object? parseArguments<T extends Object?>(
    I? appRouteInfo,
    Object? arguments,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#parseArguments, [appRouteInfo, arguments]),
          )
          as Object?);

  @override
  _i14.Future<T?> showDialog<T>({
    required _i9.BuildContext? context,
    required _i23.GPAppPopupInfo? popupInfo,
    bool? barrierDismissible = true,
    bool? useSafeArea = true,
    bool? useRootNavigator = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showDialog, [], {
              #context: context,
              #popupInfo: popupInfo,
              #barrierDismissible: barrierDismissible,
              #useSafeArea: useSafeArea,
              #useRootNavigator: useRootNavigator,
            }),
            returnValue: _i14.Future<T?>.value(),
          )
          as _i14.Future<T?>);

  @override
  _i14.Future<T?> showBaseBottomSheet<T>({
    required _i9.BuildContext? context,
    required _i23.GPAppPopupInfo? popupInfo,
    _i24.Color? backgroundColor,
    double? elevation,
    _i25.ShapeBorder? shape,
    _i24.Clip? clipBehavior,
    _i25.BoxConstraints? constraints,
    bool? isScrollControlled = false,
    bool? useRootNavigator = false,
    bool? isDismissible = true,
    bool? enableDrag = true,
    bool? showDragHandle,
    bool? useSafeArea = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#showBaseBottomSheet, [], {
              #context: context,
              #popupInfo: popupInfo,
              #backgroundColor: backgroundColor,
              #elevation: elevation,
              #shape: shape,
              #clipBehavior: clipBehavior,
              #constraints: constraints,
              #isScrollControlled: isScrollControlled,
              #useRootNavigator: useRootNavigator,
              #isDismissible: isDismissible,
              #enableDrag: enableDrag,
              #showDragHandle: showDragHandle,
              #useSafeArea: useSafeArea,
            }),
            returnValue: _i14.Future<T?>.value(),
          )
          as _i14.Future<T?>);

  @override
  _i14.Future<T?> showModalBottomSheet<T>(
    _i9.BuildContext? context,
    _i9.Widget? widget, {
    _i24.Color? backgroundColor,
    double? elevation,
    _i25.ShapeBorder? shape,
    _i24.Clip? clipBehavior,
    _i25.BoxConstraints? constraints,
    bool? isScrollControlled = false,
    bool? useRootNavigator = false,
    bool? isDismissible = true,
    bool? enableDrag = true,
    bool? showDragHandle,
    bool? useSafeArea = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #showModalBottomSheet,
              [context, widget],
              {
                #backgroundColor: backgroundColor,
                #elevation: elevation,
                #shape: shape,
                #clipBehavior: clipBehavior,
                #constraints: constraints,
                #isScrollControlled: isScrollControlled,
                #useRootNavigator: useRootNavigator,
                #isDismissible: isDismissible,
                #enableDrag: enableDrag,
                #showDragHandle: showDragHandle,
                #useSafeArea: useSafeArea,
              },
            ),
            returnValue: _i14.Future<T?>.value(),
          )
          as _i14.Future<T?>);

  @override
  void showSnackBar({
    required _i9.BuildContext? context,
    required _i26.GPAppSnackBarInfo? snackbarInfo,
    bool? closeCurrentSnackBar = true,
    _i27.AnimationStyle? snackBarAnimationStyle,
  }) => super.noSuchMethod(
    Invocation.method(#showSnackBar, [], {
      #context: context,
      #snackbarInfo: snackbarInfo,
      #closeCurrentSnackBar: closeCurrentSnackBar,
      #snackBarAnimationStyle: snackBarAnimationStyle,
    }),
    returnValueForMissingStub: null,
  );

  @override
  void closeNearestBottomSheet() => super.noSuchMethod(
    Invocation.method(#closeNearestBottomSheet, []),
    returnValueForMissingStub: null,
  );

  @override
  void closeAllBottomSheet() => super.noSuchMethod(
    Invocation.method(#closeAllBottomSheet, []),
    returnValueForMissingStub: null,
  );

  @override
  void closeAllDialog() => super.noSuchMethod(
    Invocation.method(#closeAllDialog, []),
    returnValueForMissingStub: null,
  );
}
