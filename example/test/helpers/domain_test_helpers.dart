import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:example/domain/entity/auth.entity.dart';
import 'package:example/domain/entity/test.entity.dart';
import 'package:example/domain/entity/test/assignee.entity.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';

import 'test_helper.mocks.dart';
import 'domain_test_constants.dart';

/// Helper class for domain layer testing
class DomainTestHelpers {
  
  /// Setup mock repository with success response
  static void setupMockRepositorySuccess(
    MockAuthRepository mockRepository,
    AuthCheckEmailRequest request,
    AuthCheckMailResponse response,
  ) {
    final apiResponse = ApiResponseV2<AuthCheckMailResponse>(
      status: 'success',
      data: response,
    );
    
    when(mockRepository.checkEmail(checkEmailRequest: request))
        .thenAnswer((_) async => apiResponse);
  }

  /// Setup mock repository with error response
  static void setupMockRepositoryError(
    MockAuthRepository mockRepository,
    AuthCheckEmailRequest request,
    AuthCheckMailResponse errorResponse,
  ) {
    final apiResponse = ApiResponseV2<AuthCheckMailResponse>(
      status: 'error',
      data: errorResponse,
    );
    
    when(mockRepository.checkEmail(checkEmailRequest: request))
        .thenAnswer((_) async => apiResponse);
  }

  /// Setup mock repository to throw exception
  static void setupMockRepositoryException(
    MockAuthRepository mockRepository,
    AuthCheckEmailRequest request,
    Exception exception,
  ) {
    when(mockRepository.checkEmail(checkEmailRequest: request))
        .thenThrow(exception);
  }

  /// Verify repository was called with correct parameters
  static void verifyRepositoryCall(
    MockAuthRepository mockRepository,
    AuthCheckEmailRequest request, {
    int times = 1,
  }) {
    verify(mockRepository.checkEmail(checkEmailRequest: request)).called(times);
  }

  /// Verify repository was never called
  static void verifyRepositoryNeverCalled(
    MockAuthRepository mockRepository,
    AuthCheckEmailRequest request,
  ) {
    verifyNever(mockRepository.checkEmail(checkEmailRequest: request));
  }

  /// Create test auth entity from response
  static AuthCheckMailEntity createAuthEntityFromResponse(
    AuthCheckMailResponse response,
  ) {
    return AuthCheckMailEntity(
      userId: response.userId,
      newDomain: response.newDomain,
    );
  }

  /// Assert auth entity properties
  static void assertAuthEntity(
    AuthCheckMailEntity entity,
    int expectedUserId,
    bool? expectedNewDomain,
  ) {
    expect(entity.userId, expectedUserId);
    expect(entity.newDomain, expectedNewDomain);
  }

  /// Assert user entity properties
  static void assertUser(
    User user,
    int expectedId,
    String expectedName,
    String? expectedTag,
  ) {
    expect(user.id, expectedId);
    expect(user.name, expectedName);
    expect(user.tag, expectedTag);
  }

  /// Assert assignee entity properties
  static void assertAssignee(
    AssigneeEntity assignee,
    int expectedId,
    String expectedDisplayName, {
    String? expectedEmail,
    String? expectedFullName,
  }) {
    expect(assignee.id, expectedId);
    expect(assignee.displayName, expectedDisplayName);
    if (expectedEmail != null) {
      expect(assignee.email, expectedEmail);
    }
    if (expectedFullName != null) {
      expect(assignee.fullName, expectedFullName);
    }
  }

  /// Test entity equality
  static void assertEntitiesEqual<T>(T entity1, T entity2) {
    if (entity1 is AuthCheckMailEntity && entity2 is AuthCheckMailEntity) {
      expect(DomainTestConstants.compareAuthEntities(entity1, entity2), isTrue);
    } else if (entity1 is User && entity2 is User) {
      expect(DomainTestConstants.compareUsers(entity1, entity2), isTrue);
    } else if (entity1 is AssigneeEntity && entity2 is AssigneeEntity) {
      expect(DomainTestConstants.compareAssignees(entity1, entity2), isTrue);
    } else {
      fail('Unsupported entity type for comparison');
    }
  }

  /// Test entity validation
  static void assertEntityValid<T>(T entity) {
    if (entity is AuthCheckMailEntity) {
      expect(DomainTestConstants.isValidAuthEntity(entity), isTrue);
    } else if (entity is User) {
      expect(DomainTestConstants.isValidUser(entity), isTrue);
    } else if (entity is AssigneeEntity) {
      expect(DomainTestConstants.isValidAssignee(entity), isTrue);
    } else {
      fail('Unsupported entity type for validation');
    }
  }

  /// Create multiple test scenarios for concurrent testing
  static List<Map<String, dynamic>> createConcurrentTestScenarios(int count) {
    return List.generate(count, (index) => {
      'request': AuthCheckEmailRequest(
        'user${index + 1}@test.com',
        '+8411111111${index + 1}',
      ),
      'response': AuthCheckMailResponse(
        userId: index + 1,
        newDomain: index % 2 == 0,
        salt: 'salt_${index + 1}',
      ),
      'entity': AuthCheckMailEntity(
        userId: index + 1,
        newDomain: index % 2 == 0,
      ),
    });
  }

  /// Setup multiple mock repository calls for concurrent testing
  static void setupConcurrentMockCalls(
    MockAuthRepository mockRepository,
    List<Map<String, dynamic>> scenarios,
  ) {
    for (final scenario in scenarios) {
      final request = scenario['request'] as AuthCheckEmailRequest;
      final response = scenario['response'] as AuthCheckMailResponse;
      
      setupMockRepositorySuccess(mockRepository, request, response);
    }
  }

  /// Verify multiple concurrent calls
  static void verifyConcurrentCalls(
    MockAuthRepository mockRepository,
    List<Map<String, dynamic>> scenarios,
  ) {
    for (final scenario in scenarios) {
      final request = scenario['request'] as AuthCheckEmailRequest;
      verifyRepositoryCall(mockRepository, request);
    }
  }

  /// Create error test scenarios
  static List<Exception> createErrorScenarios() {
    return [
      Exception('Generic error'),
      Exception('Invalid argument'),
      Exception('Invalid state'),
      FormatException('Format error'),
    ];
  }

  /// Test error handling for multiple scenarios
  static Future<void> testErrorScenarios(
    MockAuthRepository mockRepository,
    AuthCheckEmailRequest request,
    Future<void> Function() testFunction,
  ) async {
    final errorScenarios = createErrorScenarios();
    
    for (final error in errorScenarios) {
      setupMockRepositoryException(mockRepository, request, error);
      
      expect(testFunction, throwsA(isA<Exception>()));
      
      verifyRepositoryCall(mockRepository, request);
      reset(mockRepository);
    }
  }

  /// Performance test helper
  static Future<void> performanceTest(
    Future<void> Function() testFunction, {
    int iterations = 100,
    Duration maxDuration = const Duration(seconds: 5),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    for (int i = 0; i < iterations; i++) {
      await testFunction();
    }
    
    stopwatch.stop();
    
    expect(stopwatch.elapsed, lessThan(maxDuration),
        reason: 'Performance test took ${stopwatch.elapsed}, expected less than $maxDuration');
  }

  /// Memory leak test helper
  static Future<void> memoryLeakTest(
    Future<void> Function() testFunction, {
    int iterations = 1000,
  }) async {
    // Run test multiple times to check for memory leaks
    for (int i = 0; i < iterations; i++) {
      await testFunction();
      
      // Force garbage collection periodically
      if (i % 100 == 0) {
        // In a real scenario, you might want to check memory usage here
        await Future.delayed(Duration.zero);
      }
    }
  }

  /// Stress test helper
  static Future<void> stressTest(
    Future<void> Function() testFunction, {
    int concurrentOperations = 50,
    int iterations = 10,
  }) async {
    for (int i = 0; i < iterations; i++) {
      final futures = List.generate(
        concurrentOperations,
        (_) => testFunction(),
      );
      
      await Future.wait(futures);
    }
  }

  /// Cleanup helper for tests
  static void cleanup(MockAuthRepository mockRepository) {
    reset(mockRepository);
  }

  /// Setup common test environment
  static MockAuthRepository setupTestEnvironment() {
    final mockRepository = MockAuthRepository();
    return mockRepository;
  }

  /// Teardown test environment
  static void teardownTestEnvironment(MockAuthRepository mockRepository) {
    cleanup(mockRepository);
  }
}
