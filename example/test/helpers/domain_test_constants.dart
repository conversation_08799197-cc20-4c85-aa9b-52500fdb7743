import 'package:example/domain/entity/auth.entity.dart';
import 'package:example/domain/entity/test.entity.dart';
import 'package:example/domain/entity/test/assignee.entity.dart';
import 'package:example/domain/entity/test/assignee_info.entity.dart';
import 'package:example/domain/entity/test/assignee_work.entity.dart';

/// Test constants specifically for domain layer testing
class DomainTestConstants {
  // Auth Domain Entity Constants
  static const authUserId = 12345;
  static const authNewDomain = true;
  
  static AuthCheckMailEntity get testAuthCheckMailEntity =>
      AuthCheckMailEntity(
        userId: authUserId,
        newDomain: authNewDomain,
      );

  static AuthCheckMailEntity get testAuthCheckMailEntityMinimal =>
      AuthCheckMailEntity(userId: authUserId);

  static AuthCheckMailEntity get testAuthCheckMailEntityExisting =>
      AuthCheckMailEntity(
        userId: 67890,
        newDomain: false,
      );

  // User Entity Constants
  static const userId = 1;
  static const userName = '<PERSON>';
  static const userTag = '@johndoe';
  
  static User get testUser => User(
        id: userId,
        name: userName,
        tag: userTag,
      );

  static User get testUserWithoutTag => User(
        id: userId,
        name: userName,
        tag: null,
      );

  // UserDto Constants
  static const userDtoAge = 25;
  
  static UserDto get testUserDto => UserDto(
        id: userId,
        name: userName,
        age: userDtoAge,
      );

  // Assignee Entity Constants
  static const assigneeId = 123;
  static const assigneeDisplayName = 'Jane Smith';
  static const assigneeLang = 'en';
  static const assigneeFullName = 'Jane Elizabeth Smith';
  static const assigneeEmail = '<EMAIL>';
  
  static AssigneeEntity get testAssigneeEntity => AssigneeEntity(
        id: assigneeId,
        displayName: assigneeDisplayName,
        lang: assigneeLang,
        fullName: assigneeFullName,
        email: assigneeEmail,
      );

  static AssigneeEntity get testAssigneeEntityMinimal => AssigneeEntity(
        id: assigneeId,
        displayName: assigneeDisplayName,
      );

  // Work Entity Constants
  static const workCompany = 'Tech Corp';
  static const workDepartment = 'Engineering';
  static const workTitle = 'Senior Developer';
  static const workDepartmentId = 'eng_001';
  static const workRoleId = 'dev_senior';
  static const workPrivacy = 1;
  
  static WorkEntity get testWorkEntity => WorkEntity(
        company: workCompany,
        department: workDepartment,
        title: workTitle,
        departmentId: workDepartmentId,
        roleId: workRoleId,
        privacy: workPrivacy,
      );

  // Info Entity with Work
  static InfoEntity get testInfoEntity => InfoEntity(
        work: [testWorkEntity],
      );

  static InfoEntity get testInfoEntityEmpty => InfoEntity(
        work: [],
      );

  static InfoEntity get testInfoEntityNull => InfoEntity(
        work: null,
      );

  // Complete Assignee with Work Info
  static AssigneeEntity get testAssigneeEntityWithWork => AssigneeEntity(
        id: assigneeId,
        displayName: assigneeDisplayName,
        info: testInfoEntity,
      );

  static AssigneeEntity get testAssigneeEntityWithEmptyWork => AssigneeEntity(
        id: assigneeId,
        displayName: assigneeDisplayName,
        info: testInfoEntityEmpty,
        userRole: 'Fallback Role',
        userDepartment: 'Fallback Department',
      );

  // Test scenarios for different business cases
  static List<AuthCheckMailEntity> get authTestScenarios => [
        // New user scenario
        AuthCheckMailEntity(userId: 1, newDomain: true),
        // Existing user scenario
        AuthCheckMailEntity(userId: 2, newDomain: false),
        // Unknown domain status
        AuthCheckMailEntity(userId: 3, newDomain: null),
        // Edge case - zero user ID
        AuthCheckMailEntity(userId: 0, newDomain: true),
        // Edge case - negative user ID
        AuthCheckMailEntity(userId: -1, newDomain: false),
      ];

  static List<User> get userTestScenarios => [
        // User with tag
        User(id: 1, name: 'User One', tag: '@user1'),
        // User without tag
        User(id: 2, name: 'User Two', tag: null),
        // User with empty tag
        User(id: 3, name: 'User Three', tag: ''),
        // User with long name
        User(id: 4, name: 'User With Very Long Name That Might Cause Issues', tag: '@longname'),
        // User with special characters
        User(id: 5, name: 'Üser Fïve', tag: '@üser5'),
      ];

  static List<AssigneeEntity> get assigneeTestScenarios => [
        // Complete assignee
        AssigneeEntity(
          id: 1,
          displayName: 'Complete User',
          lang: 'en',
          fullName: 'Complete Full User',
          email: '<EMAIL>',
          info: InfoEntity(work: [
            WorkEntity(
              company: 'Test Corp',
              department: 'Testing',
              title: 'Test Engineer',
            )
          ]),
        ),
        // Minimal assignee
        AssigneeEntity(
          id: 2,
          displayName: 'Minimal User',
        ),
        // Assignee with fallback role/department
        AssigneeEntity(
          id: 3,
          displayName: 'Fallback User',
          userRole: 'Manager',
          userDepartment: 'Management',
        ),
        // Assignee with empty work info
        AssigneeEntity(
          id: 4,
          displayName: 'Empty Work User',
          info: InfoEntity(work: []),
          userRole: 'Default Role',
          userDepartment: 'Default Department',
        ),
      ];

  // Error scenarios
  static const errorUserId = -1;
  static const errorMessage = 'Test error message';
  
  static AuthCheckMailEntity get errorAuthEntity => AuthCheckMailEntity(
        userId: errorUserId,
        newDomain: null,
      );

  // Performance test data
  static List<AuthCheckMailEntity> generateAuthEntities(int count) {
    return List.generate(count, (index) => AuthCheckMailEntity(
          userId: index + 1,
          newDomain: index % 2 == 0,
        ));
  }

  static List<User> generateUsers(int count) {
    return List.generate(count, (index) => User(
          id: index + 1,
          name: 'User ${index + 1}',
          tag: '@user${index + 1}',
        ));
  }

  static List<AssigneeEntity> generateAssignees(int count) {
    return List.generate(count, (index) => AssigneeEntity(
          id: index + 1,
          displayName: 'Assignee ${index + 1}',
          email: 'assignee${index + 1}@test.com',
        ));
  }

  // Validation helpers
  static bool isValidAuthEntity(AuthCheckMailEntity entity) {
    return entity.userId > 0;
  }

  static bool isValidUser(User user) {
    return user.id > 0 && user.name.isNotEmpty;
  }

  static bool isValidAssignee(AssigneeEntity assignee) {
    return assignee.id > 0 && assignee.displayName.isNotEmpty;
  }

  // Comparison helpers
  static bool compareAuthEntities(AuthCheckMailEntity a, AuthCheckMailEntity b) {
    return a.userId == b.userId && a.newDomain == b.newDomain;
  }

  static bool compareUsers(User a, User b) {
    return a.id == b.id && a.name == b.name && a.tag == b.tag;
  }

  static bool compareAssignees(AssigneeEntity a, AssigneeEntity b) {
    return a.id == b.id && 
           a.displayName == b.displayName && 
           a.email == b.email;
  }
}
