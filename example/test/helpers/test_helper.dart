import 'package:dio/dio.dart';
import 'package:mockito/annotations.dart';
import 'package:example/data/data_source/remote/auth.service.dart';
import 'package:example/domain/repository/auth_repo.dart';
import 'package:example/domain/usecase/auth_check_mail.usecase.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';

import 'package:flutter/material.dart';

// Generate mocks for testing
@GenerateMocks([
  Dio,
  AuthService,
  AuthRepository,
  AuthCheckMailUseCase,
  NavigatorObserver,
  GPAppNavigator,
])
void main() {}
