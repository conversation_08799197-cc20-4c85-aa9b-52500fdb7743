import 'package:flutter_test/flutter_test.dart';
import 'package:example/domain/entity/test/assignee.entity.dart';
import 'package:example/domain/entity/test/assignee_info.entity.dart';
import 'package:example/domain/entity/test/assignee_work.entity.dart';

void main() {
  group('AssigneeEntity', () {
    const testId = 123;
    const testDisplayName = '<PERSON>';
    const testLang = 'en';
    const testFullName = '<PERSON>';
    const testEmail = '<EMAIL>';

    test('should create AssigneeEntity with required properties only', () {
      // Arrange & Act
      final assignee = AssigneeEntity(
        id: testId,
        displayName: testDisplayName,
      );

      // Assert
      expect(assignee.id, testId);
      expect(assignee.displayName, testDisplayName);
      expect(assignee.lang, isNull);
      expect(assignee.fullName, isNull);
      expect(assignee.email, isNull);
    });

    test('should create AssigneeEntity with all properties', () {
      // Arrange & Act
      final assignee = AssigneeEntity(
        id: testId,
        displayName: testDisplayName,
        lang: testLang,
        fullName: testFullName,
        email: testEmail,
        cover: 'cover_url',
        avatar: 'avatar_url',
        linkProfile: 'profile_link',
        workspaceAccount: 456,
        workspaceId: 'workspace_123',
        phoneNumber: '+**********',
        avatarThumbPattern: 'thumb_pattern',
        coverThumbPattern: 'cover_pattern',
        userDepartment: 'Engineering',
        userRole: 'Developer',
      );

      // Assert
      expect(assignee.id, testId);
      expect(assignee.displayName, testDisplayName);
      expect(assignee.lang, testLang);
      expect(assignee.fullName, testFullName);
      expect(assignee.email, testEmail);
      expect(assignee.cover, 'cover_url');
      expect(assignee.avatar, 'avatar_url');
      expect(assignee.linkProfile, 'profile_link');
      expect(assignee.workspaceAccount, 456);
      expect(assignee.workspaceId, 'workspace_123');
      expect(assignee.phoneNumber, '+**********');
      expect(assignee.avatarThumbPattern, 'thumb_pattern');
      expect(assignee.coverThumbPattern, 'cover_pattern');
      expect(assignee.userDepartment, 'Engineering');
      expect(assignee.userRole, 'Developer');
    });

    test('should serialize to and from JSON correctly', () {
      // Arrange
      final originalAssignee = AssigneeEntity(
        id: testId,
        displayName: testDisplayName,
        lang: testLang,
        fullName: testFullName,
        email: testEmail,
      );

      // Act
      final json = originalAssignee.toJson();
      final deserializedAssignee = AssigneeEntity.fromJson(json);

      // Assert
      expect(deserializedAssignee.id, originalAssignee.id);
      expect(deserializedAssignee.displayName, originalAssignee.displayName);
      expect(deserializedAssignee.lang, originalAssignee.lang);
      expect(deserializedAssignee.fullName, originalAssignee.fullName);
      expect(deserializedAssignee.email, originalAssignee.email);
    });

    test('should handle complex nested info structure', () {
      // Arrange
      final workEntity = WorkEntity(
        company: 'Tech Corp',
        department: 'Engineering',
        title: 'Senior Developer',
        departmentId: 'eng_001',
        roleId: 'dev_senior',
        privacy: 1,
      );

      final infoEntity = InfoEntity(
        work: [workEntity],
      );

      final assignee = AssigneeEntity(
        id: testId,
        displayName: testDisplayName,
        info: infoEntity,
      );

      // Assert
      expect(assignee.info, isNotNull);
      expect(assignee.info!.work, isNotNull);
      expect(assignee.info!.work!.length, 1);
      expect(assignee.info!.work!.first.company, 'Tech Corp');
      expect(assignee.info!.work!.first.department, 'Engineering');
      expect(assignee.info!.work!.first.title, 'Senior Developer');
    });
  });

  group('AssigneeEntity Extensions', () {
    group('displayLastName', () {
      test('should return last name from display name', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Michael Doe',
        );

        // Act & Assert
        expect(assignee.displayLastName, 'Doe');
      });

      test('should return full name if only one word', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John',
        );

        // Act & Assert
        expect(assignee.displayLastName, 'John');
      });

      test('should handle empty display name', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: '',
        );

        // Act & Assert
        expect(assignee.displayLastName, '');
      });

      test('should handle display name with multiple spaces', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John   Michael   Doe',
        );

        // Act & Assert
        expect(assignee.displayLastName, 'Doe');
      });
    });

    group('role getter', () {
      test('should return role from work info when available', () {
        // Arrange
        final workEntity = WorkEntity(title: 'Senior Developer');
        final infoEntity = InfoEntity(work: [workEntity]);
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
          info: infoEntity,
        );

        // Act & Assert
        expect(assignee.role, 'Senior Developer');
      });

      test('should return userRole when work info is not available', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
          userRole: 'Manager',
        );

        // Act & Assert
        expect(assignee.role, 'Manager');
      });

      test('should return empty string when no role information available', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
        );

        // Act & Assert
        expect(assignee.role, '');
      });

      test('should handle empty work list gracefully', () {
        // Arrange
        final infoEntity = InfoEntity(work: []);
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
          info: infoEntity,
          userRole: 'Fallback Role',
        );

        // Act & Assert
        // When work list is empty, it falls back to userRole, but the extension logic
        // catches exception and returns empty string
        expect(assignee.role, '');
      });
    });

    group('department getter', () {
      test('should return department from work info when available', () {
        // Arrange
        final workEntity = WorkEntity(department: 'Engineering');
        final infoEntity = InfoEntity(work: [workEntity]);
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
          info: infoEntity,
        );

        // Act & Assert
        expect(assignee.department, 'Engineering');
      });

      test('should return userDepartment when work info is not available', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
          userDepartment: 'Marketing',
        );

        // Act & Assert
        expect(assignee.department, 'Marketing');
      });

      test('should return empty string when no department information available', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
        );

        // Act & Assert
        expect(assignee.department, '');
      });

      test('should handle null work info gracefully', () {
        // Arrange
        final assignee = AssigneeEntity(
          id: 1,
          displayName: 'John Doe',
          info: InfoEntity(work: null),
          userDepartment: 'HR',
        );

        // Act & Assert
        expect(assignee.department, 'HR');
      });
    });
  });
}
