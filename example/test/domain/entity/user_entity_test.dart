import 'package:flutter_test/flutter_test.dart';
import 'package:example/domain/entity/test.entity.dart';

void main() {
  group('User Entity', () {
    const testId = 1;
    const testName = '<PERSON>';
    const testTag = '@johndoe';

    test('should create User instance with all properties', () {
      // Arrange & Act
      final user = User(
        id: testId,
        name: testName,
        tag: testTag,
      );

      // Assert
      expect(user.id, testId);
      expect(user.name, testName);
      expect(user.tag, testTag);
    });

    test('should create User instance with null tag', () {
      // Arrange & Act
      final user = User(
        id: testId,
        name: testName,
        tag: null,
      );

      // Assert
      expect(user.id, testId);
      expect(user.name, testName);
      expect(user.tag, isNull);
    });

    group('hasTag getter', () {
      test('should return true when tag is not null', () {
        // Arrange
        final user = User(
          id: testId,
          name: testName,
          tag: testTag,
        );

        // Act & Assert
        expect(user.hasTag, isTrue);
      });

      test('should return false when tag is null', () {
        // Arrange
        final user = User(
          id: testId,
          name: testName,
          tag: null,
        );

        // Act & Assert
        expect(user.hasTag, isFalse);
      });

      test('should return false when tag is empty string', () {
        // Arrange
        final user = User(
          id: testId,
          name: testName,
          tag: '',
        );

        // Act & Assert
        expect(user.hasTag, isTrue); // Empty string is still not null
      });
    });

    test('should be immutable', () {
      // Arrange
      final user1 = User(
        id: testId,
        name: testName,
        tag: testTag,
      );
      
      final user2 = User(
        id: testId,
        name: testName,
        tag: testTag,
      );

      // Assert - Same values should create equal objects
      expect(user1.id, user2.id);
      expect(user1.name, user2.name);
      expect(user1.tag, user2.tag);
      expect(user1.hasTag, user2.hasTag);
    });

    test('should handle different data types correctly', () {
      // Arrange & Act
      final user = User(
        id: 999999,
        name: 'Test User With Very Long Name',
        tag: '@test_user_with_underscores_and_numbers_123',
      );

      // Assert
      expect(user.id, 999999);
      expect(user.name, 'Test User With Very Long Name');
      expect(user.tag, '@test_user_with_underscores_and_numbers_123');
      expect(user.hasTag, isTrue);
    });
  });

  group('UserDto Entity', () {
    const testId = 1;
    const testName = 'Jane Doe';
    const testAge = 25;

    test('should create UserDto instance with all properties', () {
      // Arrange & Act
      final userDto = UserDto(
        id: testId,
        name: testName,
        age: testAge,
      );

      // Assert
      expect(userDto.id, testId);
      expect(userDto.name, testName);
      expect(userDto.age, testAge);
    });

    test('should handle different age values', () {
      // Test cases for different age scenarios
      final testCases = [
        {'id': 1, 'name': 'Child', 'age': 5},
        {'id': 2, 'name': 'Teen', 'age': 16},
        {'id': 3, 'name': 'Adult', 'age': 30},
        {'id': 4, 'name': 'Senior', 'age': 65},
        {'id': 5, 'name': 'Very Old', 'age': 100},
      ];

      for (final testCase in testCases) {
        // Arrange & Act
        final userDto = UserDto(
          id: testCase['id'] as int,
          name: testCase['name'] as String,
          age: testCase['age'] as int,
        );

        // Assert
        expect(userDto.id, testCase['id']);
        expect(userDto.name, testCase['name']);
        expect(userDto.age, testCase['age']);
      }
    });

    test('should be mutable (different from User)', () {
      // Arrange
      final userDto1 = UserDto(
        id: testId,
        name: testName,
        age: testAge,
      );
      
      final userDto2 = UserDto(
        id: testId,
        name: testName,
        age: testAge,
      );

      // Assert - Same values should create objects with same properties
      expect(userDto1.id, userDto2.id);
      expect(userDto1.name, userDto2.name);
      expect(userDto1.age, userDto2.age);
    });

    test('should handle edge cases for age', () {
      // Test minimum age
      final youngUser = UserDto(
        id: 1,
        name: 'Newborn',
        age: 0,
      );
      expect(youngUser.age, 0);

      // Test negative age (if allowed by business logic)
      final negativeAgeUser = UserDto(
        id: 2,
        name: 'Invalid Age',
        age: -1,
      );
      expect(negativeAgeUser.age, -1);
    });
  });
}
