import 'package:example/domain/entity/test/assignee_work.entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:example/domain/entity/test/assignee_info.entity.dart';

void main() {
  group('InfoEntity', () {
    group('Constructor', () {
      test('should create InfoEntity with work list', () {
        // Arrange
        final work = [
          WorkEntity(
            company: 'Test Company',
            department: 'Engineering',
            title: 'Software Engineer',
            departmentId: '1',
            departments: ['1', '2'],
            departmentIds: ['1', '2'],
            roleId: '1',
            privacy: 1,
          ),
        ];

        // Act
        final entity = InfoEntity(work: work);

        // Assert
        expect(entity, isA<InfoEntity>());
        expect(entity.work, work);
        expect(entity.work?.length, 1);
        expect(entity.work?.first.company, 'Test Company');
      });

      test('should create InfoEntity with empty work list', () {
        // Arrange & Act
        final entity = InfoEntity(work: []);

        // Assert
        expect(entity.work, isEmpty);
      });

      test('should create InfoEntity with multiple work entries', () {
        // Arrange
        final work = [
          WorkEntity(
            company: 'Company A',
            department: 'Dept A',
            title: 'Title A',
            departmentId: '1',
            departments: ['1'],
            departmentIds: ['1'],
            roleId: '1',
            privacy: 1,
          ),
          WorkEntity(
            company: 'Company B',
            department: 'Dept B',
            title: 'Title B',
            departmentId: '2',
            departments: ['2'],
            departmentIds: ['2'],
            roleId: '2',
            privacy: 2,
          ),
        ];

        // Act
        final entity = InfoEntity(work: work);

        // Assert
        expect(entity.work?.length, 2);
        expect(entity.work?[0].company, 'Company A');
        expect(entity.work?[1].company, 'Company B');
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final work = [
          WorkEntity(
            company: 'Test Company',
            department: 'Engineering',
            title: 'Software Engineer',
            departmentId: '1',
            departments: ['1', '2'],
            departmentIds: ['1', '2'],
            roleId: '1',
            privacy: 1,
          ),
        ];
        final entity = InfoEntity(work: work);

        // Act
        final json = entity.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['work'], isA<List>());
        expect(json['work'].length, 1);
        expect(json['work'][0]['company'], 'Test Company');
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final json = {
          'work': [
            {
              'company': 'Test Company',
              'department': 'Engineering',
              'title': 'Software Engineer',
              'departmentId': '1',
              'departments': ['1', '2'],
              'departmentIds': ['1', '2'],
              'roleId': '1',
              'privacy': 1,
            }
          ]
        };

        // Act
        final entity = InfoEntity.fromJson(json);

        // Assert
        expect(entity.work?.length, 1);
        expect(entity.work?.first.company, 'Test Company');
        expect(entity.work?.first.department, 'Engineering');
        expect(entity.work?.first.title, 'Software Engineer');
      });

      test('should handle empty work list in JSON', () {
        // Arrange
        final json = {'work': <Map<String, dynamic>>[]};

        // Act
        final entity = InfoEntity.fromJson(json);

        // Assert
        expect(entity.work, isEmpty);
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalWork = [
          WorkEntity(
            company: 'Round Trip Company',
            department: 'Round Trip Dept',
            title: 'Round Trip Title',
            departmentId: '99',
            departments: ['99', '100'],
            departmentIds: ['99', '100'],
            roleId: '99',
            privacy: 3,
          ),
        ];
        final originalEntity = InfoEntity(work: originalWork);

        // Act
        final json = originalEntity.toJson();
        final deserializedEntity = InfoEntity.fromJson(json);

        // Assert
        expect(deserializedEntity.work?.length, originalEntity.work?.length);
        expect(deserializedEntity.work?.first.company, originalEntity.work?.first.company);
        expect(deserializedEntity.work?.first.department, originalEntity.work?.first.department);
        expect(deserializedEntity.work?.first.title, originalEntity.work?.first.title);
        expect(deserializedEntity.work?.first.departmentId, originalEntity.work?.first.departmentId);
        expect(deserializedEntity.work?.first.roleId, originalEntity.work?.first.roleId);
        expect(deserializedEntity.work?.first.privacy, originalEntity.work?.first.privacy);
      });
    });

    group('Edge Cases', () {
      test('should handle null values gracefully', () {
        // Arrange
        final work = [
          WorkEntity(
            company: '',
            department: '',
            title: '',
            departmentId: '',
            departments: [],
            departmentIds: [],
            roleId: '',
            privacy: 0,
          ),
        ];

        // Act
        final entity = InfoEntity(work: work);

        // Assert
        expect(entity.work?.first.company, '');
        expect(entity.work?.first.department, '');
        expect(entity.work?.first.title, '');
        expect(entity.work?.first.departments, isEmpty);
        expect(entity.work?.first.departmentIds, isEmpty);
      });

      test('should handle large work lists', () {
        // Arrange
        final work = List.generate(100, (index) => WorkEntity(
          company: 'Company $index',
          department: 'Department $index',
          title: 'Title $index',
          departmentId: '$index',
          departments: ['$index'],
          departmentIds: ['$index'],
          roleId: '$index',
          privacy: index % 5,
        ));

        // Act
        final entity = InfoEntity(work: work);

        // Assert
        expect(entity.work?.length, 100);
        expect(entity.work?.first.company, 'Company 0');
        expect(entity.work?.last.company, 'Company 99');
      });

      test('should handle special characters in work data', () {
        // Arrange
        final work = [
          WorkEntity(
            company: 'Company with "quotes" & symbols',
            department: 'Dept with émojis 🚀',
            title: 'Title with\nnewlines',
            departmentId: 'id-with-dashes',
            departments: ['dept1', 'dept2'],
            departmentIds: ['id1', 'id2'],
            roleId: 'role-id',
            privacy: 1,
          ),
        ];

        // Act
        final entity = InfoEntity(work: work);

        // Assert
        expect(entity.work?.first.company, contains('quotes'));
        expect(entity.work?.first.department, contains('🚀'));
        expect(entity.work?.first.title, contains('\n'));
      });
    });

    group('Type Safety', () {
      test('should maintain type safety', () {
        // Arrange
        final work = [
          WorkEntity(
            company: 'Type Safe Company',
            department: 'Type Safe Dept',
            title: 'Type Safe Title',
            departmentId: '1',
            departments: ['1'],
            departmentIds: ['1'],
            roleId: '1',
            privacy: 1,
          ),
        ];

        // Act
        final entity = InfoEntity(work: work);

        // Assert
        expect(entity, isA<InfoEntity>());
        expect(entity.work, isA<List<WorkEntity>>());
        expect(entity.work?.first, isA<WorkEntity>());
        expect(entity.work?.first.company, isA<String>());
        expect(entity.work?.first.departments, isA<List>());
        expect(entity.work?.first.privacy, isA<int>());
      });
    });

    group('Performance', () {
      test('should handle rapid creation efficiently', () {
        // Arrange
        final work = [
          WorkEntity(
            company: 'Performance Company',
            department: 'Performance Dept',
            title: 'Performance Title',
            departmentId: '1',
            departments: ['1'],
            departmentIds: ['1'],
            roleId: '1',
            privacy: 1,
          ),
        ];

        final stopwatch = Stopwatch()..start();

        // Act
        for (int i = 0; i < 1000; i++) {
          InfoEntity(work: work);
        }

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should handle JSON operations efficiently', () {
        // Arrange
        final work = List.generate(10, (index) => WorkEntity(
          company: 'Company $index',
          department: 'Department $index',
          title: 'Title $index',
          departmentId: '$index',
          departments: ['$index'],
          departmentIds: ['$index'],
          roleId: '$index',
          privacy: index,
        ));
        final entity = InfoEntity(work: work);

        final stopwatch = Stopwatch()..start();

        // Act
        for (int i = 0; i < 100; i++) {
          final json = entity.toJson();
          InfoEntity.fromJson(json);
        }

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });
  });
}
