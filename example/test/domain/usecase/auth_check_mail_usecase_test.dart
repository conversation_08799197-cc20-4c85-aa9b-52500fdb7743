import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:example/domain/usecase/auth_check_mail.usecase.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:dio/dio.dart';

import '../../helpers/test_helper.mocks.dart';
import '../../helpers/test_constants.dart';

void main() {
  group('AuthCheckMailUseCase', () {
    late MockAuthRepository mockAuthRepository;
    late AuthCheckMailUseCase authCheckMailUseCase;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      authCheckMailUseCase = AuthCheckMailUseCase(mockAuthRepository);
    });

    group('buildUseCase', () {
      test('should return success response when repository call succeeds', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Assert
        expect(result, expectedResponse);
        expect(result.status, 'success');
        expect(result.data, isNotNull);
        expect(result.data.userId, TestConstants.testUserId);
        expect(result.data.newDomain, TestConstants.testNewDomain);
        expect(result.data.salt, TestConstants.testSalt);
        
        // Verify repository was called with correct parameters
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should return error response when repository call fails', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final errorResponse = TestConstants.testErrorApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => errorResponse);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Assert
        expect(result, errorResponse);
        expect(result.status, 'error');
        expect(result.data, isNotNull);
        expect(result.data.userId, -1);
        
        // Verify repository was called
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should propagate exception when repository throws', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final exception = Exception('Network error');
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenThrow(exception);

        // Act & Assert
        expect(
          () async => await authCheckMailUseCase.buildUseCase(request),
          throwsA(isA<Exception>()),
        );
        
        // Verify repository was called
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle DioException correctly', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final dioException = DioException(
          requestOptions: RequestOptions(path: ''),
          type: DioExceptionType.connectionTimeout,
          message: 'Connection timeout',
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenThrow(dioException);

        // Act & Assert
        expect(
          () async => await authCheckMailUseCase.buildUseCase(request),
          throwsA(isA<DioException>()),
        );
        
        // Verify repository was called
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle different request parameters correctly', () async {
        // Arrange
        final customRequest = AuthCheckEmailRequest(
          '<EMAIL>',
          '+84987654321',
        );
        final customResponse = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(
            userId: 999,
            newDomain: true,
            salt: 'custom_salt',
          ),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: customRequest))
            .thenAnswer((_) async => customResponse);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(customRequest);

        // Assert
        expect(result.status, 'success');
        expect(result.data.userId, 999);
        expect(result.data.newDomain, true);
        expect(result.data.salt, 'custom_salt');
        
        // Verify repository was called with correct custom request
        verify(mockAuthRepository.checkEmail(checkEmailRequest: customRequest)).called(1);
      });

      test('should handle concurrent calls correctly', () async {
        // Arrange
        final request1 = AuthCheckEmailRequest('<EMAIL>', '+84111111111');
        final request2 = AuthCheckEmailRequest('<EMAIL>', '+84222222222');
        
        final response1 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 1, newDomain: false, salt: 'salt1'),
        );
        final response2 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 2, newDomain: true, salt: 'salt2'),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request1))
            .thenAnswer((_) async => response1);
        when(mockAuthRepository.checkEmail(checkEmailRequest: request2))
            .thenAnswer((_) async => response2);

        // Act
        final futures = [
          authCheckMailUseCase.buildUseCase(request1),
          authCheckMailUseCase.buildUseCase(request2),
        ];
        final results = await Future.wait(futures);

        // Assert
        expect(results[0].data.userId, 1);
        expect(results[0].data.newDomain, false);
        expect(results[0].data.salt, 'salt1');
        
        expect(results[1].data.userId, 2);
        expect(results[1].data.newDomain, true);
        expect(results[1].data.salt, 'salt2');
        
        // Verify both repository calls were made
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request1)).called(1);
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request2)).called(1);
      });

      test('should handle null response data gracefully', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final responseWithMinimalData = ApiResponseV2<AuthCheckMailResponse>(
          status: 'partial_success',
          data: AuthCheckMailResponse(userId: 0),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => responseWithMinimalData);

        // Act
        final result = await authCheckMailUseCase.buildUseCase(request);

        // Assert
        expect(result.status, 'partial_success');
        expect(result.data, isNotNull);
        expect(result.data.userId, 0);
        expect(result.data.newDomain, isNull);
        expect(result.data.salt, isNull);
        
        // Verify repository was called
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });
    });

    group('Use Case Behavior', () {
      test('should be stateless and reusable', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act - Call the use case multiple times
        final result1 = await authCheckMailUseCase.buildUseCase(request);
        final result2 = await authCheckMailUseCase.buildUseCase(request);

        // Assert - Both calls should return the same result
        expect(result1.status, result2.status);
        expect(result1.data.userId, result2.data.userId);
        expect(result1.data.newDomain, result2.data.newDomain);
        expect(result1.data.salt, result2.data.salt);
        
        // Verify repository was called twice
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(2);
      });

      test('should not modify input parameters', () async {
        // Arrange
        final originalEmail = '<EMAIL>';
        final originalPhone = '+84123456789';
        final request = AuthCheckEmailRequest(originalEmail, originalPhone);
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act
        await authCheckMailUseCase.buildUseCase(request);

        // Assert - Input should remain unchanged
        expect(request.email, originalEmail);
        expect(request.phoneNumber, originalPhone);
      });
    });
  });
}
