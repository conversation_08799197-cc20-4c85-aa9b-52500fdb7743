import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:example/domain/repository/auth_repo.dart';
import 'package:example/data/model/auth/request/auth_check_email_request.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
import 'package:dio/dio.dart';

import '../../helpers/test_helper.mocks.dart';
import '../../helpers/test_constants.dart';

void main() {
  group('AuthRepository Contract Tests', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
    });

    group('checkEmail method contract', () {
      test('should accept AuthCheckEmailRequest and return ApiResponseV2<AuthCheckMailResponse>', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act
        final result = await mockAuthRepository.checkEmail(checkEmailRequest: request);

        // Assert
        expect(result, isA<ApiResponseV2<AuthCheckMailResponse>>());
        expect(result.status, isA<String>());
        expect(result.data, isA<AuthCheckMailResponse>());
        
        // Verify method signature compliance
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle named parameter correctly', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act - Using named parameter as per interface contract
        final result = await mockAuthRepository.checkEmail(checkEmailRequest: request);

        // Assert
        expect(result, expectedResponse);
        
        // Verify the named parameter was used correctly
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should be async and return Future', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final expectedResponse = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => expectedResponse);

        // Act
        final future = mockAuthRepository.checkEmail(checkEmailRequest: request);

        // Assert
        expect(future, isA<Future<ApiResponseV2<AuthCheckMailResponse>>>());
        
        final result = await future;
        expect(result, expectedResponse);
      });

      test('should allow throwing exceptions as per contract', () async {
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final exception = DioException(
          requestOptions: RequestOptions(path: ''),
          type: DioExceptionType.connectionTimeout,
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenThrow(exception);

        // Act & Assert
        expect(
          () async => await mockAuthRepository.checkEmail(checkEmailRequest: request),
          throwsA(isA<DioException>()),
        );
        
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
      });

      test('should handle different request types correctly', () async {
        // Test with different email formats
        final testCases = [
          AuthCheckEmailRequest('<EMAIL>', '+84123456789'),
          AuthCheckEmailRequest('<EMAIL>', '+1234567890'),
          AuthCheckEmailRequest('<EMAIL>', '+84987654321'),
        ];

        for (int i = 0; i < testCases.length; i++) {
          final request = testCases[i];
          final response = ApiResponseV2<AuthCheckMailResponse>(
            status: 'success',
            data: AuthCheckMailResponse(userId: i + 1),
          );
          
          when(mockAuthRepository.checkEmail(checkEmailRequest: request))
              .thenAnswer((_) async => response);

          // Act
          final result = await mockAuthRepository.checkEmail(checkEmailRequest: request);

          // Assert
          expect(result.status, 'success');
          expect(result.data.userId, i + 1);
          
          verify(mockAuthRepository.checkEmail(checkEmailRequest: request)).called(1);
        }
      });

      test('should handle concurrent calls as per contract', () async {
        // Arrange
        final request1 = AuthCheckEmailRequest('<EMAIL>', '+84111111111');
        final request2 = AuthCheckEmailRequest('<EMAIL>', '+84222222222');
        
        final response1 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 1),
        );
        final response2 = ApiResponseV2<AuthCheckMailResponse>(
          status: 'success',
          data: AuthCheckMailResponse(userId: 2),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request1))
            .thenAnswer((_) async => response1);
        when(mockAuthRepository.checkEmail(checkEmailRequest: request2))
            .thenAnswer((_) async => response2);

        // Act
        final futures = [
          mockAuthRepository.checkEmail(checkEmailRequest: request1),
          mockAuthRepository.checkEmail(checkEmailRequest: request2),
        ];
        final results = await Future.wait(futures);

        // Assert
        expect(results[0].data.userId, 1);
        expect(results[1].data.userId, 2);
        
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request1)).called(1);
        verify(mockAuthRepository.checkEmail(checkEmailRequest: request2)).called(1);
      });
    });

    group('Repository Interface Compliance', () {
      test('should implement AuthRepository interface', () {
        // Assert
        expect(mockAuthRepository, isA<AuthRepository>());
      });

      test('should have checkEmail method with correct signature', () {
        // This test verifies that the interface contract is maintained
        // The method should exist and be callable with named parameter
        
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final response = TestConstants.testSuccessApiResponse;
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => response);

        // Act & Assert - This will compile only if signature is correct
        expect(
          () async => await mockAuthRepository.checkEmail(checkEmailRequest: request),
          returnsNormally,
        );
      });

      test('should maintain method contract across different implementations', () async {
        // This test ensures that any implementation of AuthRepository
        // will have the same method signature and behavior contract
        
        // Arrange
        final request = TestConstants.testAuthCheckEmailRequest;
        final successResponse = TestConstants.testSuccessApiResponse;
        final errorResponse = TestConstants.testErrorApiResponse;
        
        // Test success case
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => successResponse);
        
        var result = await mockAuthRepository.checkEmail(checkEmailRequest: request);
        expect(result, isA<ApiResponseV2<AuthCheckMailResponse>>());
        expect(result.status, 'success');
        
        // Test error case
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenAnswer((_) async => errorResponse);
        
        result = await mockAuthRepository.checkEmail(checkEmailRequest: request);
        expect(result, isA<ApiResponseV2<AuthCheckMailResponse>>());
        expect(result.status, 'error');
        
        // Test exception case
        when(mockAuthRepository.checkEmail(checkEmailRequest: request))
            .thenThrow(Exception('Test exception'));
        
        expect(
          () async => await mockAuthRepository.checkEmail(checkEmailRequest: request),
          throwsException,
        );
      });

      test('should handle edge cases as per contract', () async {
        // Test with minimal request data
        final minimalRequest = AuthCheckEmailRequest('a@b.c', '1');
        final minimalResponse = ApiResponseV2<AuthCheckMailResponse>(
          status: 'minimal',
          data: AuthCheckMailResponse(userId: 0),
        );
        
        when(mockAuthRepository.checkEmail(checkEmailRequest: minimalRequest))
            .thenAnswer((_) async => minimalResponse);

        // Act
        final result = await mockAuthRepository.checkEmail(checkEmailRequest: minimalRequest);

        // Assert
        expect(result.status, 'minimal');
        expect(result.data.userId, 0);
        expect(result.data.newDomain, isNull);
        expect(result.data.salt, isNull);
        
        verify(mockAuthRepository.checkEmail(checkEmailRequest: minimalRequest)).called(1);
      });
    });

    group('Error Handling Contract', () {
      test('should allow various exception types', () async {
        final request = TestConstants.testAuthCheckEmailRequest;
        
        // Test different exception types that implementations might throw
        final exceptions = [
          DioException(requestOptions: RequestOptions(path: ''), type: DioExceptionType.connectionTimeout),
          DioException(requestOptions: RequestOptions(path: ''), type: DioExceptionType.receiveTimeout),
          Exception('Generic exception'),
          ArgumentError('Invalid argument'),
        ];

        for (final exception in exceptions) {
          when(mockAuthRepository.checkEmail(checkEmailRequest: request))
              .thenThrow(exception);

          expect(
            () async => await mockAuthRepository.checkEmail(checkEmailRequest: request),
            throwsA(isA<Object>()),
          );
        }
      });
    });
  });
}
