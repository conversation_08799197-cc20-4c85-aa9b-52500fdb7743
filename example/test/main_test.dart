import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common_bloc.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';
import 'package:example/main.dart';
import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/presentation/test/bloc/test_bloc.dart';
import 'package:example/mapper/entity/mapper.dart';

import 'helpers/test_helper.mocks.dart';

void main() {
  group('Main App', () {
    late CommonBloc commonBloc;
    late MockGPAppNavigator<ExampleAppInfoRoute> mockAppNavigator;
    late GlobalKey<NavigatorState> navigatorKey;

    setUp(() {
      commonBloc = CommonBloc();
      mockAppNavigator = MockGPAppNavigator();
      navigatorKey = GlobalKey<NavigatorState>();

      // Clear GetIt before each test
      GetIt.I.reset();

      // Register required dependencies
      GetIt.I.registerSingleton<CommonBloc>(
        commonBloc,
        instanceName: 'kCommonBloc',
      );
      GetIt.I.registerSingleton<GPAppNavigator<ExampleAppInfoRoute>>(
        mockAppNavigator,
        instanceName: 'kAppNavigator',
      );
      GetIt.I.registerSingleton<GlobalKey<NavigatorState>>(
        navigatorKey,
        instanceName: 'kAppNavigatorKey',
      );

      // Register additional dependencies needed by TestBloc
      GetIt.I.registerSingleton<GPMapper>(
        GPMapper(),
        instanceName: 'kGPMapper',
      );

      // Register TestBloc with its dependencies
      GetIt.I.registerSingleton<TestBloc>(
        TestBloc(authCheckMailUseCase: MockAuthCheckMailUseCase()),
      );
    });

    tearDown(() {
      commonBloc.close();
      // Close TestBloc if it exists
      if (GetIt.I.isRegistered<TestBloc>()) {
        GetIt.I<TestBloc>().close();
      }
      GetIt.I.reset();
    });

    group('MyWidget', () {
      testWidgets('should render without errors', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(MyWidget), findsOneWidget);
      });

      testWidgets('should provide CommonBloc', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(BlocProvider<CommonBloc>), findsOneWidget);
      });

      testWidgets('should provide GPAppNavigator repository',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(
            find.byType(
                RepositoryProvider<GPAppNavigator<ExampleAppInfoRoute>>),
            findsOneWidget);
      });

      testWidgets('should be a StatelessWidget', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert
        final widget = tester.widget<MyWidget>(find.byType(MyWidget));
        expect(widget, isA<StatelessWidget>());
      });

      testWidgets('should have correct widget hierarchy',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(BlocProvider<CommonBloc>), findsOneWidget);
        expect(
            find.byType(
                RepositoryProvider<GPAppNavigator<ExampleAppInfoRoute>>),
            findsOneWidget);
      });
    });

    group('Widget Integration', () {
      testWidgets(
          'should integrate BlocProvider and RepositoryProvider correctly',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert - Should not throw and should render
        expect(find.byType(MyWidget), findsOneWidget);
      });

      testWidgets('should handle GetIt dependencies correctly',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert - Should access GetIt dependencies without errors
        expect(() => GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
            returnsNormally);
        expect(
            () => GetIt.I<GPAppNavigator<ExampleAppInfoRoute>>(
                instanceName: 'kAppNavigator'),
            returnsNormally);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle missing CommonBloc gracefully',
          (WidgetTester tester) async {
        // Arrange - Remove CommonBloc
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');

        // Act & Assert
        bool exceptionThrown = false;
        try {
          await tester.pumpWidget(const MyWidget());
          await tester.pumpAndSettle();
        } catch (e) {
          exceptionThrown = true;
          expect(e, isA<StateError>());
          expect(e.toString(), contains('kCommonBloc'));
        }
        expect(exceptionThrown, isTrue, reason: 'Expected StateError to be thrown');
      });

      testWidgets('should handle missing AppNavigator gracefully',
          (WidgetTester tester) async {
        // Arrange - Remove AppNavigator
        GetIt.I.unregister<GPAppNavigator<ExampleAppInfoRoute>>(
            instanceName: 'kAppNavigator');

        // Act & Assert
        bool exceptionThrown = false;
        try {
          await tester.pumpWidget(const MyWidget());
          await tester.pumpAndSettle();
        } catch (e) {
          exceptionThrown = true;
          expect(e, isA<StateError>());
          expect(e.toString(), contains('kAppNavigator'));
        }
        expect(exceptionThrown, isTrue, reason: 'Expected StateError to be thrown');
      });

      testWidgets('should handle widget lifecycle correctly',
          (WidgetTester tester) async {
        // Act - Create, dispose, and recreate
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();

        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert - Should not throw
        expect(find.byType(MyWidget), findsOneWidget);
      });
    });

    group('Dependency Injection', () {
      testWidgets('should correctly inject CommonBloc',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert - Check that BlocProvider exists and can access the bloc
        expect(find.byType(BlocProvider<CommonBloc>), findsOneWidget);
        expect(() => GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
            returnsNormally);
      });

      testWidgets('should correctly inject AppNavigator',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert - Check that RepositoryProvider exists and can access the navigator
        expect(
            find.byType(
                RepositoryProvider<GPAppNavigator<ExampleAppInfoRoute>>),
            findsOneWidget);
        expect(
            () => GetIt.I<GPAppNavigator<ExampleAppInfoRoute>>(
                instanceName: 'kAppNavigator'),
            returnsNormally);
      });

      testWidgets('should use correct instance names',
          (WidgetTester tester) async {
        // This test verifies that the correct instance names are used
        // Act
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();

        // Assert - Should be able to access with correct instance names
        expect(() => GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
            returnsNormally);
        expect(
            () => GetIt.I<GPAppNavigator<ExampleAppInfoRoute>>(
                instanceName: 'kAppNavigator'),
            returnsNormally);
      });
    });

    group('Widget Properties', () {
      testWidgets('should have correct key handling',
          (WidgetTester tester) async {
        // Arrange
        const key = Key('test-key');

        // Act
        await tester.pumpWidget(const MyWidget(key: key));
        await tester.pumpAndSettle();

        // Assert
        final widget = tester.widget<MyWidget>(find.byKey(key));
        expect(widget.key, key);
      });

      testWidgets('should support multiple instances',
          (WidgetTester tester) async {
        // This test verifies that multiple MyWidget instances can be created
        // Test first instance
        await tester.pumpWidget(const MyWidget(key: Key('widget1')));
        await tester.pumpAndSettle();
        expect(find.byKey(const Key('widget1')), findsOneWidget);

        // Clear and test second instance
        await tester.pumpWidget(Container());
        await tester.pumpWidget(const MyWidget(key: Key('widget2')));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byKey(const Key('widget2')), findsOneWidget);
      });
    });

    group('State Management', () {
      testWidgets('should provide bloc context correctly',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: const MyWidget(),
          ),
        );
        await tester.pumpAndSettle();

        // Assert - BlocProvider should be accessible
        expect(find.byType(BlocProvider<CommonBloc>), findsOneWidget);
      });

      testWidgets('should provide repository context correctly',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: const MyWidget(),
          ),
        );
        await tester.pumpAndSettle();

        // Assert - RepositoryProvider should be accessible
        expect(
            find.byType(
                RepositoryProvider<GPAppNavigator<ExampleAppInfoRoute>>),
            findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should render efficiently', (WidgetTester tester) async {
        // Act
        final stopwatch = Stopwatch()..start();
        await tester.pumpWidget(const MyWidget());
        await tester.pumpAndSettle();
        stopwatch.stop();

        // Assert - Should render quickly (less than 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should handle rapid rebuilds', (WidgetTester tester) async {
        // Act - Rapid rebuilds
        for (int i = 0; i < 5; i++) {
          await tester.pumpWidget(const MyWidget());
          await tester.pumpAndSettle();
        }

        // Assert - Should not throw
        expect(find.byType(MyWidget), findsOneWidget);
      });
    });
  });
}
