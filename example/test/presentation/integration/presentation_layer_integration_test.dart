import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:example/presentation/test/bloc/test_bloc.dart';
import 'package:example/presentation/test/bloc/test_state.dart';
import 'package:example/presentation/test/bloc/test_event.dart';
import 'package:example/presentation/login/login_page.dart';
import 'package:example/presentation/home/<USER>';


import '../../helpers/test_helper.mocks.dart';
import '../../helpers/test_constants.dart';

void main() {
  group('Presentation Layer Integration Tests', () {
    late MockAuthCheckMailUseCase mockAuthCheckMailUseCase;
    late TestBloc testBloc;

    setUp(() {
      mockAuthCheckMailUseCase = MockAuthCheckMailUseCase();
      testBloc = TestBloc(authCheckMailUseCase: mockAuthCheckMailUseCase);
    });

    tearDown(() {
      testBloc.close();
    });

    Widget createTestApp({Widget? home}) {
      return MaterialApp(
        home: BlocProvider<TestBloc>(
          create: (context) => testBloc,
          child: home ?? const ExampleLoginPage(),
        ),
      );
    }

    group('Login Page Integration', () {
      testWidgets('should display login page correctly', (tester) async {
        await tester.pumpWidget(createTestApp());

        // Verify login page elements
        expect(find.byType(ExampleLoginPage), findsOneWidget);
        expect(find.text('Login'), findsNWidgets(2)); // AppBar + Button
        expect(find.byType(TextFormField), findsNWidgets(2));
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should handle text input in login form', (tester) async {
        await tester.pumpWidget(createTestApp());

        // Enter text in form fields
        final textFields = find.byType(TextFormField);
        await tester.enterText(textFields.first, '<EMAIL>');
        await tester.enterText(textFields.last, 'password123');

        // Verify text was entered
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.text('password123'), findsOneWidget);
      });

      testWidgets('should handle login button interaction', (tester) async {
        await tester.pumpWidget(createTestApp());

        final loginButton = find.byType(ElevatedButton);
        
        // Button tap will fail due to missing dependencies, which is expected
        expect(() async {
          await tester.tap(loginButton);
          await tester.pumpAndSettle();
        }, throwsA(anything));
      });
    });

    group('Home Page Integration', () {
      testWidgets('should display home page correctly', (tester) async {
        await tester.pumpWidget(createTestApp(home: const ExampleHomePage()));

        // Verify home page elements
        expect(find.byType(ExampleHomePage), findsOneWidget);
        expect(find.text('Home'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsNWidgets(11));
      });

      testWidgets('should display all section headers', (tester) async {
        await tester.pumpWidget(createTestApp(home: const ExampleHomePage()));

        // Verify section headers
        expect(find.text('Snackbar'), findsOneWidget);
        expect(find.text('Dialog'), findsOneWidget);
        expect(find.text('BottomSheet'), findsOneWidget);
      });

      testWidgets('should handle button interactions', (tester) async {
        await tester.pumpWidget(createTestApp(home: const ExampleHomePage()));

        // Test a few button taps (they will fail due to missing dependencies)
        final buttons = [
          'View user profile',
          'Show normal snackbar',
          'Show success snackbar',
        ];

        for (final buttonText in buttons) {
          final button = find.text(buttonText);
          expect(button, findsOneWidget);
          
          // Button tap will fail due to missing dependencies, which is expected
          expect(() async {
            await tester.tap(button);
            await tester.pump();
          }, throwsA(anything));
        }
      });

      testWidgets('should be scrollable', (tester) async {
        await tester.pumpWidget(createTestApp(home: const ExampleHomePage()));

        // Verify scrollable content
        expect(find.byType(SingleChildScrollView), findsOneWidget);

        // Test scrolling
        await tester.drag(find.byType(SingleChildScrollView), const Offset(0, -300));
        await tester.pumpAndSettle();

        expect(find.byType(ExampleHomePage), findsOneWidget);
      });
    });

    group('Bloc Integration with UI', () {
      testWidgets('should integrate bloc with UI components', (tester) async {
        // Create a simple test widget that uses the bloc
        final testWidget = MaterialApp(
          home: BlocProvider<TestBloc>(
            create: (context) => testBloc,
            child: Scaffold(
              body: BlocBuilder<TestBloc, TestState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Text('State: ${state.test}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(const TestCounterEvent(100));
                        },
                        child: const Text('Update Counter'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(const TestEvent());
                        },
                        child: const Text('Test Event'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpWidget(testWidget);

        // Verify initial state
        expect(find.text('State: test'), findsOneWidget);

        // Test counter event
        await tester.tap(find.text('Update Counter'));
        await tester.pump();
        expect(find.text('State: 100'), findsOneWidget);

        // Test error event (will throw)
        expect(() async {
          await tester.tap(find.text('Test Event'));
          await tester.pump();
        }, throwsA(anything));
      });

      testWidgets('should handle bloc state changes in UI', (tester) async {
        // Setup mock use case
        when(mockAuthCheckMailUseCase.buildUseCase(any))
            .thenAnswer((_) async => TestConstants.testSuccessApiResponse);

        final testWidget = MaterialApp(
          home: BlocProvider<TestBloc>(
            create: (context) => testBloc,
            child: Scaffold(
              body: BlocBuilder<TestBloc, TestState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Text('Current State: ${state.test}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(
                            AuthEmailCheck(TestConstants.testAuthCheckEmailRequest),
                          );
                        },
                        child: const Text('Auth Check'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpWidget(testWidget);

        // Verify initial state
        expect(find.text('Current State: test'), findsOneWidget);

        // Trigger auth check
        await tester.tap(find.text('Auth Check'));
        await tester.pump();

        // Verify state changed to success
        expect(find.text('Current State: success'), findsOneWidget);

        // Verify use case was called
        verify(mockAuthCheckMailUseCase.buildUseCase(TestConstants.testAuthCheckEmailRequest)).called(1);
      });
    });

    group('Error Handling Integration', () {
      testWidgets('should handle bloc errors gracefully in UI', (tester) async {
        // Setup mock use case to throw error
        when(mockAuthCheckMailUseCase.buildUseCase(any))
            .thenThrow(Exception('Network error'));

        final testWidget = MaterialApp(
          home: BlocProvider<TestBloc>(
            create: (context) => testBloc,
            child: Scaffold(
              body: BlocBuilder<TestBloc, TestState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Text('State: ${state.test}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(
                            AuthEmailCheck(TestConstants.testAuthCheckEmailRequest),
                          );
                        },
                        child: const Text('Trigger Error'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpWidget(testWidget);

        // Trigger error
        expect(() async {
          await tester.tap(find.text('Trigger Error'));
          await tester.pump();
        }, throwsA(anything));
      });

      testWidgets('should handle UI errors gracefully', (tester) async {
        // Test widget that might throw errors
        final errorWidget = MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    throw Exception('UI Error');
                  },
                  child: const Text('Error Button'),
                );
              },
            ),
          ),
        );

        await tester.pumpWidget(errorWidget);

        // Button should be present
        expect(find.text('Error Button'), findsOneWidget);

        // Tapping will throw error
        expect(() async {
          await tester.tap(find.text('Error Button'));
          await tester.pump();
        }, throwsA(anything));
      });
    });

    group('Performance Integration', () {
      testWidgets('should handle rapid UI interactions efficiently', (tester) async {
        final testWidget = MaterialApp(
          home: BlocProvider<TestBloc>(
            create: (context) => testBloc,
            child: Scaffold(
              body: BlocBuilder<TestBloc, TestState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Text('Counter: ${state.test}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(
                            TestCounterEvent(int.tryParse(state.test) ?? 0 + 1),
                          );
                        },
                        child: const Text('Increment'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpWidget(testWidget);

        final stopwatch = Stopwatch()..start();

        // Rapid button taps
        for (int i = 0; i < 10; i++) {
          await tester.tap(find.text('Increment'));
          await tester.pump();
        }

        stopwatch.stop();

        // Should handle rapid interactions efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should handle complex UI rebuilds efficiently', (tester) async {
        await tester.pumpWidget(createTestApp(home: const ExampleHomePage()));

        final stopwatch = Stopwatch()..start();

        // Multiple rebuilds
        for (int i = 0; i < 20; i++) {
          await tester.pump();
        }

        stopwatch.stop();

        // Should handle rebuilds efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });
    });

    group('State Management Integration', () {
      testWidgets('should maintain state consistency across UI interactions', (tester) async {
        final testWidget = MaterialApp(
          home: BlocProvider<TestBloc>(
            create: (context) => testBloc,
            child: Scaffold(
              body: BlocBuilder<TestBloc, TestState>(
                builder: (context, state) {
                  return Column(
                    children: [
                      Text('Value: ${state.test}'),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(const TestCounterEvent(42));
                        },
                        child: const Text('Set 42'),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          context.read<TestBloc>().add(const TestCounterEvent(99));
                        },
                        child: const Text('Set 99'),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpWidget(testWidget);

        // Initial state
        expect(find.text('Value: test'), findsOneWidget);

        // Change to 42
        await tester.tap(find.text('Set 42'));
        await tester.pump();
        expect(find.text('Value: 42'), findsOneWidget);

        // Change to 99
        await tester.tap(find.text('Set 99'));
        await tester.pump();
        expect(find.text('Value: 99'), findsOneWidget);

        // State should be consistent
        expect(find.text('Value: 42'), findsNothing);
        expect(find.text('Value: test'), findsNothing);
      });
    });
  });
}
