import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/presentation/login/login_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gp_core_v2/base/navigator/base_app_navigator/app_navigator.dart';
import 'package:mockito/mockito.dart';

import '../../helpers/test_helper.mocks.dart';

void main() {
  group('ExampleLoginPage', () {
    late MockGPAppNavigator<ExampleAppInfoRoute> mockAppNavigator;

    setUp(() {
      mockAppNavigator = MockGPAppNavigator();

      // Stub the popAndPush method
      when(mockAppNavigator.popAndPush(any, any,
              result: anyNamed('result'),
              useRootNavigator: anyNamed('useRootNavigator'),
              arguments: anyNamed('arguments')))
          .thenAnswer((_) async => null);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: RepositoryProvider<GPAppNavigator<ExampleAppInfoRoute>>(
          create: (context) => mockAppNavigator,
          child: const ExampleLoginPage(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render ExampleLoginPage with all UI elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(ExampleLoginPage), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
      });

      testWidgets('should display app bar title', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Login'), findsAtLeastNWidgets(1));
      });

      testWidgets('should display email and password fields',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TextFormField), findsNWidgets(2));
        expect(find.text('Email'), findsOneWidget);
        expect(find.text('Password'), findsOneWidget);
      });

      testWidgets('should display login button', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(ElevatedButton), findsOneWidget);
        expect(find.text('Login'), findsAtLeastNWidgets(1));
      });

      testWidgets('should have correct widget structure',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(SafeArea), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('should handle login button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        final loginButton = find.byType(ElevatedButton);
        await tester.tap(loginButton);
        await tester.pumpAndSettle();

        // Assert
        verify(mockAppNavigator.popAndPush(any, any,
                result: anyNamed('result'),
                useRootNavigator: anyNamed('useRootNavigator'),
                arguments: anyNamed('arguments')))
            .called(1);
      });

      testWidgets('should navigate to home page when login button is pressed',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        final loginButton = find.byType(ElevatedButton);
        await tester.tap(loginButton);
        await tester.pumpAndSettle();

        // Assert
        verify(mockAppNavigator.popAndPush(
                any, argThat(isA<ExampleAppInfoRoute>()),
                result: anyNamed('result'),
                useRootNavigator: anyNamed('useRootNavigator'),
                arguments: anyNamed('arguments')))
            .called(1);
      });

      testWidgets('should handle text input in email field',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        final emailField = find.byType(TextFormField).first;
        await tester.enterText(emailField, '<EMAIL>');
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('<EMAIL>'), findsOneWidget);
      });

      testWidgets('should handle text input in password field',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        final passwordField = find.byType(TextFormField).last;
        await tester.enterText(passwordField, 'password123');
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('password123'), findsOneWidget);
      });
    });

    group('Widget Properties', () {
      testWidgets('should be a StatelessWidget', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final loginPage =
            tester.widget<ExampleLoginPage>(find.byType(ExampleLoginPage));
        expect(loginPage, isA<StatelessWidget>());
      });

      testWidgets('should have correct padding', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final padding = find.byType(Padding);
        expect(padding, findsAtLeastNWidgets(1));
      });

      testWidgets('should have correct spacing between elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SizedBox), findsAtLeastNWidgets(1));
      });
    });

    group('Navigation', () {
      testWidgets('should call navigateToHome when login button is pressed',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        final loginButton = find.byType(ElevatedButton);
        await tester.tap(loginButton);
        await tester.pumpAndSettle();

        // Assert - Verify navigation was called
        verify(mockAppNavigator.popAndPush(any, any,
                result: anyNamed('result'),
                useRootNavigator: anyNamed('useRootNavigator'),
                arguments: anyNamed('arguments')))
            .called(1);
      });

      testWidgets('should navigate with correct route',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        final loginButton = find.byType(ElevatedButton);
        await tester.tap(loginButton);
        await tester.pumpAndSettle();

        // Assert - Verify the route type
        final captured = verify(mockAppNavigator.popAndPush(
                captureAny, captureAny,
                result: anyNamed('result'),
                useRootNavigator: anyNamed('useRootNavigator'),
                arguments: anyNamed('arguments')))
            .captured;
        expect(captured[1], isA<ExampleAppInfoRoute>());
      });
    });

    group('Error Handling', () {
      testWidgets('should handle missing dependencies gracefully',
          (WidgetTester tester) async {
        // This test ensures the widget doesn't crash with missing dependencies

        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should render without crashing
        expect(find.byType(ExampleLoginPage), findsOneWidget);
      });

      testWidgets('should handle widget lifecycle correctly',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Dispose and recreate
        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Should not throw and should render correctly
        expect(find.byType(ExampleLoginPage), findsOneWidget);
      });
    });
  });
}
