import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common.dart';
import 'package:example/presentation/test/test_page.dart';

void main() {
  group('TestPage', () {
    late CommonBloc commonBloc;

    setUp(() {
      // Create real instances instead of mocks
      commonBloc = CommonBloc();

      // Register real instances in GetIt
      if (GetIt.I.isRegistered<CommonBloc>(instanceName: 'kCommonBloc')) {
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');
      }

      GetIt.I.registerSingleton<CommonBloc>(commonBloc, instanceName: 'kCommonBloc');
    });

    tearDown(() {
      if (GetIt.I.isRegistered<CommonBloc>(instanceName: 'kCommonBloc')) {
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');
      }
      commonBloc.close();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<CommonBloc>.value(value: commonBloc),
          ],
          child: const TestPage(),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render TestPage with all UI elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(TestPage), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
      });

      testWidgets('should display app bar title',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Test Page'), findsOneWidget);
      });

      testWidgets('should display action buttons',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('email check'), findsOneWidget);
        expect(find.text('Handle error'), findsOneWidget);
        expect(find.byType(TextButton), findsAtLeastNWidgets(1));
      });

      testWidgets('should display common state text',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.textContaining('handle common state'), findsAtLeastNWidgets(1));
      });
    });

    group('State Management', () {
      testWidgets('should display initial loading state correctly',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - CommonBloc starts with isLoading: false by default
        expect(find.text('CurrentState: NoLoading'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });

      testWidgets('should handle CommonBloc state changes',
          (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Trigger loading state
        commonBloc.add(const LoadingVisibilityEmitted(isLoading: true));
        await tester.pumpAndSettle();

        // Assert loading state
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('CurrentState: NoLoading'), findsNothing);

        // Trigger non-loading state
        commonBloc.add(const LoadingVisibilityEmitted(isLoading: false));
        await tester.pumpAndSettle();

        // Assert non-loading state
        expect(find.text('CurrentState: NoLoading'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsNothing);
      });
    });

    group('Widget Properties', () {
      testWidgets('should be a StatefulWidget', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final testPage = tester.widget<TestPage>(find.byType(TestPage));
        expect(testPage, isA<StatefulWidget>());
      });

      testWidgets('should have correct widget structure', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsAtLeastNWidgets(1));
      });
    });

    group('Button Interactions', () {
      testWidgets('should handle button taps without errors',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act & Assert - Should not throw
        expect(() async {
          final emailCheckButton = find.text('email check');
          if (emailCheckButton.evaluate().isNotEmpty) {
            await tester.tap(emailCheckButton);
            await tester.pumpAndSettle();
          }
        }, returnsNormally);
      });

      testWidgets('should handle error button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act & Assert - Should not throw
        expect(() async {
          final errorButton = find.text('Handle error');
          if (errorButton.evaluate().isNotEmpty) {
            await tester.tap(errorButton);
            await tester.pumpAndSettle();
          }
        }, returnsNormally);
      });
    });

    group('Integration Tests', () {
      testWidgets('should integrate with CommonBloc properly',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(BlocBuilder<CommonBloc, CommonState>), findsAtLeastNWidgets(1));
      });

      testWidgets('should handle widget lifecycle correctly',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Dispose and recreate
        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();

        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Should not throw and should render correctly
        expect(find.byType(TestPage), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle missing dependencies gracefully',
          (WidgetTester tester) async {
        // This test ensures the widget doesn't crash with missing dependencies
        // The actual TestPage might require additional setup for full functionality
        
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Widget should render without crashing
        expect(find.byType(TestPage), findsOneWidget);
      });
    });
  });
}
