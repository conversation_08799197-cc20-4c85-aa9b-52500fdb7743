import 'package:example/domain/entity/test.entity.dart';
import 'package:example/presentation/user/user_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

// Generate mocks
import '../../helpers/test_helper.mocks.dart' as test_helper_mocks;

void main() {
  group('ExampleUserPage', () {
    late User testUser;
    late test_helper_mocks.MockNavigatorObserver mockNavigatorObserver;

    setUp(() {
      testUser = User(
        id: 1,
        name: 'Test User',
        tag: 'test_tag',
      );
      mockNavigatorObserver = test_helper_mocks.MockNavigatorObserver();
    });

    Widget createTestWidget({User? user}) {
      return MaterialApp(
        home: ExampleUserPage(user: user ?? testUser),
        navigatorObservers: [mockNavigatorObserver],
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render ExampleUserPage with all UI elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(ExampleUserPage), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should display app bar with title',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('User details'), findsOneWidget);
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.title, isA<Text>());
      });

      testWidgets('should display user information rows',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Username:'), findsOneWidget);
        expect(find.text('User tag:'), findsOneWidget);
        expect(find.text('Test User'), findsOneWidget);
        expect(find.text('test_tag'), findsOneWidget);
      });

      testWidgets('should display assignee details button',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Assignee Details'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should have proper layout structure',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(Padding), findsOneWidget);
        expect(
            find.byType(Row), findsNWidgets(2)); // Username and User tag rows
        expect(find.byType(SizedBox),
            findsNWidgets(3)); // 2 for labels + 1 for spacing
        expect(find.byType(Expanded), findsNWidgets(2)); // For user data
      });
    });

    group('User Data Display', () {
      testWidgets('should display user name correctly',
          (WidgetTester tester) async {
        // Arrange
        final user = User(id: 1, name: 'John Doe', tag: 'john');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('John Doe'), findsOneWidget);
      });

      testWidgets('should display user tag correctly',
          (WidgetTester tester) async {
        // Arrange
        final user = User(id: 1, name: 'Jane Smith', tag: 'jane_smith');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('jane_smith'), findsOneWidget);
      });

      testWidgets('should handle null user tag', (WidgetTester tester) async {
        // Arrange
        final user = User(id: 1, name: 'No Tag User', tag: null);

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('No Tag User'), findsOneWidget);
        expect(find.text(''), findsWidgets); // Empty string for null tag
      });

      testWidgets('should handle empty user tag', (WidgetTester tester) async {
        // Arrange
        final user = User(id: 1, name: 'Empty Tag User', tag: '');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Empty Tag User'), findsOneWidget);
        expect(find.text(''), findsWidgets); // Empty string for empty tag
      });

      testWidgets('should handle long user names', (WidgetTester tester) async {
        // Arrange
        final longName = 'A' * 100;
        final user = User(id: 1, name: longName, tag: 'long');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text(longName), findsOneWidget);
      });

      testWidgets('should handle special characters in user data',
          (WidgetTester tester) async {
        // Arrange
        final user = User(
            id: 1,
            name: 'User with émojis 🚀 and "quotes"',
            tag: 'special-chars_123');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('User with émojis 🚀 and "quotes"'), findsOneWidget);
        expect(find.text('special-chars_123'), findsOneWidget);
      });
    });

    group('Navigation', () {
      testWidgets('should handle assignee details button tap',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act
        await tester.tap(find.text('Assignee Details'));
        await tester.pumpAndSettle();

        // Assert - Should attempt navigation (will fail in test without proper setup)
        expect(find.text('Assignee Details'), findsOneWidget);
      });

      testWidgets('should call navigateToUserProfile when button is pressed',
          (WidgetTester tester) async {
        // Arrange
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act & Assert - Should not throw
        expect(() async {
          await tester.tap(find.byType(ElevatedButton));
          await tester.pumpAndSettle();
        }, returnsNormally);
      });
    });

    group('Layout and Styling', () {
      testWidgets('should have proper padding', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final padding = tester.widget<Padding>(find.byType(Padding));
        expect(padding.padding, const EdgeInsets.fromLTRB(16, 100, 16, 16));
      });

      testWidgets('should have proper spacing between elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final sizedBoxes = tester.widgetList<SizedBox>(find.byType(SizedBox));
        final spacingBox = sizedBoxes.firstWhere((box) => box.height == 16);
        expect(spacingBox.height, 16);
      });

      testWidgets('should have proper label width',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final labelBoxes = tester
            .widgetList<SizedBox>(find.byType(SizedBox))
            .where((box) => box.width == 100);
        expect(labelBoxes.length, 2); // Username and User tag labels
      });

      testWidgets('should have scrollable content',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SingleChildScrollView), findsOneWidget);

        // Test scrolling
        await tester.drag(
            find.byType(SingleChildScrollView), const Offset(0, -100));
        await tester.pumpAndSettle();

        // Should still find the page
        expect(find.byType(ExampleUserPage), findsOneWidget);
      });
    });

    group('Widget Properties', () {
      testWidgets('should accept user parameter in constructor',
          (WidgetTester tester) async {
        // Arrange
        final customUser = User(id: 999, name: 'Custom User', tag: 'custom');

        // Act
        await tester.pumpWidget(createTestWidget(user: customUser));
        await tester.pumpAndSettle();

        // Assert
        final userPage =
            tester.widget<ExampleUserPage>(find.byType(ExampleUserPage));
        expect(userPage.user, equals(customUser));
        expect(find.text('Custom User'), findsOneWidget);
      });

      testWidgets('should have optional key parameter',
          (WidgetTester tester) async {
        // Arrange
        const testKey = Key('test_user_page');
        final user = User(id: 1, name: 'Test', tag: 'test');

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: ExampleUserPage(user: user, key: testKey),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.byKey(testKey), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should render efficiently', (WidgetTester tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      testWidgets('should handle rapid user changes',
          (WidgetTester tester) async {
        // Arrange
        final users = List.generate(
            10,
            (index) => User(
                  id: index,
                  name: 'User $index',
                  tag: 'tag$index',
                ));

        final stopwatch = Stopwatch()..start();

        // Act
        for (final user in users) {
          await tester.pumpWidget(createTestWidget(user: user));
          await tester.pump(const Duration(milliseconds: 10));
        }

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        expect(find.text('User 9'), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle user with zero id',
          (WidgetTester tester) async {
        // Arrange
        final user = User(id: 0, name: 'Zero ID User', tag: 'zero');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Zero ID User'), findsOneWidget);
        expect(find.text('zero'), findsOneWidget);
      });

      testWidgets('should handle user with negative id',
          (WidgetTester tester) async {
        // Arrange
        final user = User(id: -1, name: 'Negative ID User', tag: 'negative');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Negative ID User'), findsOneWidget);
        expect(find.text('negative'), findsOneWidget);
      });

      testWidgets('should handle user with very large id',
          (WidgetTester tester) async {
        // Arrange
        final user = User(id: 999999999, name: 'Large ID User', tag: 'large');

        // Act
        await tester.pumpWidget(createTestWidget(user: user));
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Large ID User'), findsOneWidget);
        expect(find.text('large'), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have accessible button', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final button =
            tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.onPressed, isNotNull);
      });

      testWidgets('should have readable text labels',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Username:'), findsOneWidget);
        expect(find.text('User tag:'), findsOneWidget);

        // Text should be readable
        final usernameText = tester.widget<Text>(find.text('Username:'));
        final userTagText = tester.widget<Text>(find.text('User tag:'));
        expect(usernameText.data, 'Username:');
        expect(userTagText.data, 'User tag:');
      });
    });

    group('Type Safety', () {
      testWidgets('should maintain type safety', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final userPage =
            tester.widget<ExampleUserPage>(find.byType(ExampleUserPage));
        expect(userPage, isA<ExampleUserPage>());
        expect(userPage.user, isA<User>());
        expect(userPage.user.id, isA<int>());
        expect(userPage.user.name, isA<String>());
        expect(userPage.user.tag, isA<String?>());
      });
    });

    group('Mock Data Integration', () {
      testWidgets('should handle mock assignee data correctly',
          (WidgetTester tester) async {
        // This test verifies that the mock data structure is valid
        // The actual navigation test would require proper router setup

        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Button should be present and tappable
        final button = find.byType(ElevatedButton);
        expect(button, findsOneWidget);

        final elevatedButton = tester.widget<ElevatedButton>(button);
        expect(elevatedButton.onPressed, isNotNull);
      });
    });
  });
}
