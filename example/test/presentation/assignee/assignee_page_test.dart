import 'package:example/domain/entity/test/assignee.entity.dart';
import 'package:example/domain/entity/test/assignee_info.entity.dart';
import 'package:example/domain/entity/test/assignee_work.entity.dart';
import 'package:example/presentation/assignee/assignee_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_json_viewer/flutter_json_viewer.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ExampleAssigneePage', () {
    late AssigneeEntity testAssigneeEntity;

    setUp(() {
      testAssigneeEntity = AssigneeEntity(
        id: 1,
        displayName: 'Test User',
        lang: 'en',
        fullName: 'Test Full Name',
        cover: 'https://example.com/cover.jpg',
        avatar: 'https://example.com/avatar.jpg',
        email: '<EMAIL>',
        linkProfile: 'https://example.com/profile',
        info: InfoEntity(
          work: [
            WorkEntity(
              company: 'Test Company',
              department: 'Engineering',
              title: 'Software Engineer',
              departmentId: '1',
              departments: ['Engineering'],
              departmentIds: ['1'],
              roleId: 'engineer',
              privacy: 1,
            ),
          ],
        ),
        workspaceAccount: 1,
        workspaceId: '1',
        phoneNumber: '*********',
        avatarThumbPattern: 'https://example.com/avatar-thumb.jpg',
        coverThumbPattern: 'https://example.com/cover-thumb.jpg',
        userDepartment: 'Engineering',
        userRole: 'Software Engineer',
      );
    });

    Widget createTestWidget({AssigneeEntity? assigneeEntity}) {
      return MaterialApp(
        home: ExampleAssigneePage(
          assigneeEntity: assigneeEntity ?? testAssigneeEntity,
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render ExampleAssigneePage with all UI elements',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(ExampleAssigneePage), findsOneWidget);
        expect(find.byType(Scaffold), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(Padding), findsOneWidget);
        expect(find.byType(SingleChildScrollView), findsOneWidget);
        expect(find.byType(Column), findsOneWidget);
        expect(find.byType(JsonViewer), findsOneWidget);
      });

      testWidgets('should display app bar with title',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('User details'), findsOneWidget);
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.title, isA<Text>());
      });

      testWidgets('should display JsonViewer with assignee data',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(JsonViewer), findsOneWidget);
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        expect(jsonViewer.jsonObj, isA<Map<String, dynamic>>());
      });

      testWidgets('should have proper layout structure',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
        expect(scaffold.appBar, isNotNull);
        expect(scaffold.body, isNotNull);
      });
    });

    group('AssigneeEntity Data Display', () {
      testWidgets('should display assignee entity data in JsonViewer',
          (WidgetTester tester) async {
        // Arrange
        final customAssignee = AssigneeEntity(
          id: 999,
          displayName: 'Custom User',
          lang: 'vi',
          fullName: 'Custom Full Name',
          cover: 'https://custom.com/cover.jpg',
          avatar: 'https://custom.com/avatar.jpg',
          email: '<EMAIL>',
          linkProfile: 'https://custom.com/profile',
          info: InfoEntity(work: []),
          workspaceAccount: 2,
          workspaceId: '2',
          phoneNumber: '*********',
          avatarThumbPattern: 'https://custom.com/avatar-thumb.jpg',
          coverThumbPattern: 'https://custom.com/cover-thumb.jpg',
          userDepartment: 'Marketing',
          userRole: 'Manager',
        );

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: customAssignee));
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;
        expect(jsonData['id'], 999);
        expect(jsonData['displayName'], 'Custom User');
        expect(jsonData['email'], '<EMAIL>');
      });

      testWidgets('should handle assignee with complex work info',
          (WidgetTester tester) async {
        // Arrange
        final complexAssignee = AssigneeEntity(
          id: 123,
          displayName: 'Complex User',
          lang: 'en',
          fullName: 'Complex Full Name',
          cover: 'https://example.com/cover.jpg',
          avatar: 'https://example.com/avatar.jpg',
          email: '<EMAIL>',
          linkProfile: 'https://example.com/profile',
          info: InfoEntity(
            work: [
              WorkEntity(
                company: 'Company A',
                department: 'Dept A',
                title: 'Title A',
                departmentId: '1',
                departments: ['Dept A', 'Dept B'],
                departmentIds: ['1', '2'],
                roleId: 'role1',
                privacy: 1,
              ),
              WorkEntity(
                company: 'Company B',
                department: 'Dept C',
                title: 'Title B',
                departmentId: '3',
                departments: ['Dept C'],
                departmentIds: ['3'],
                roleId: 'role2',
                privacy: 2,
              ),
            ],
          ),
          workspaceAccount: 1,
          workspaceId: '1',
          phoneNumber: '*********',
          avatarThumbPattern: 'https://example.com/avatar-thumb.jpg',
          coverThumbPattern: 'https://example.com/cover-thumb.jpg',
          userDepartment: 'Multi Department',
          userRole: 'Multi Role',
        );

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: complexAssignee));
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;
        expect(jsonData['info']['work'], isA<List>());
        expect((jsonData['info']['work'] as List).length, 2);
      });

      testWidgets('should handle assignee with empty work info',
          (WidgetTester tester) async {
        // Arrange
        final emptyWorkAssignee = AssigneeEntity(
          id: 456,
          displayName: 'Empty Work User',
          lang: 'en',
          fullName: 'Empty Work Full Name',
          cover: 'https://example.com/cover.jpg',
          avatar: 'https://example.com/avatar.jpg',
          email: '<EMAIL>',
          linkProfile: 'https://example.com/profile',
          info: InfoEntity(work: []),
          workspaceAccount: 1,
          workspaceId: '1',
          phoneNumber: '*********',
          avatarThumbPattern: 'https://example.com/avatar-thumb.jpg',
          coverThumbPattern: 'https://example.com/cover-thumb.jpg',
          userDepartment: 'No Department',
          userRole: 'No Role',
        );

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: emptyWorkAssignee));
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;
        expect(jsonData['info']['work'], isEmpty);
      });
    });

    group('Layout and Styling', () {
      testWidgets('should have proper padding', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final padding = tester.widget<Padding>(find.byType(Padding));
        expect(padding.padding, const EdgeInsets.fromLTRB(16, 100, 16, 16));
      });

      testWidgets('should have scrollable content',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SingleChildScrollView), findsOneWidget);

        // Test scrolling
        await tester.drag(
            find.byType(SingleChildScrollView), const Offset(0, -100));
        await tester.pumpAndSettle();

        // Should still find the page
        expect(find.byType(ExampleAssigneePage), findsOneWidget);
      });

      testWidgets('should arrange content in column',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final column = tester.widget<Column>(find.byType(Column));
        expect(column.children.length, 1); // Only JsonViewer
      });
    });

    group('Widget Properties', () {
      testWidgets('should accept assigneeEntity parameter in constructor',
          (WidgetTester tester) async {
        // Arrange
        final customAssignee = AssigneeEntity(
          id: 777,
          displayName: 'Constructor Test User',
          lang: 'en',
          fullName: 'Constructor Test Full Name',
          cover: 'https://test.com/cover.jpg',
          avatar: 'https://test.com/avatar.jpg',
          email: '<EMAIL>',
          linkProfile: 'https://test.com/profile',
          info: InfoEntity(work: []),
          workspaceAccount: 1,
          workspaceId: '1',
          phoneNumber: '*********',
          avatarThumbPattern: 'https://test.com/avatar-thumb.jpg',
          coverThumbPattern: 'https://test.com/cover-thumb.jpg',
          userDepartment: 'Test Department',
          userRole: 'Test Role',
        );

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: customAssignee));
        await tester.pumpAndSettle();

        // Assert
        final assigneePage = tester
            .widget<ExampleAssigneePage>(find.byType(ExampleAssigneePage));
        expect(assigneePage.assigneeEntity, equals(customAssignee));
      });

      testWidgets('should have optional key parameter',
          (WidgetTester tester) async {
        // Arrange
        const testKey = Key('test_assignee_page');

        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: ExampleAssigneePage(
              assigneeEntity: testAssigneeEntity,
              key: testKey,
            ),
          ),
        );
        await tester.pumpAndSettle();

        // Assert
        expect(find.byKey(testKey), findsOneWidget);
      });
    });

    group('JsonViewer Integration', () {
      testWidgets('should pass correct JSON data to JsonViewer',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;

        expect(jsonData['id'], testAssigneeEntity.id);
        expect(jsonData['displayName'], testAssigneeEntity.displayName);
        expect(jsonData['email'], testAssigneeEntity.email);
        expect(jsonData['fullName'], testAssigneeEntity.fullName);
      });

      testWidgets('should handle JSON serialization correctly',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj;

        expect(jsonData, isA<Map<String, dynamic>>());
        expect(jsonData, isNotEmpty);
      });
    });

    group('Performance', () {
      testWidgets('should render efficiently', (WidgetTester tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      testWidgets('should handle large JSON data efficiently',
          (WidgetTester tester) async {
        // Arrange
        final largeWorkList = List.generate(
            100,
            (index) => WorkEntity(
                  company: 'Company $index',
                  department: 'Department $index',
                  title: 'Title $index',
                  departmentId: '$index',
                  departments: ['Dept$index'],
                  departmentIds: ['$index'],
                  roleId: 'role$index',
                  privacy: index % 5,
                ));

        final largeAssignee = AssigneeEntity(
          id: 1000,
          displayName: 'Large Data User',
          lang: 'en',
          fullName: 'Large Data Full Name',
          cover: 'https://example.com/cover.jpg',
          avatar: 'https://example.com/avatar.jpg',
          email: '<EMAIL>',
          linkProfile: 'https://example.com/profile',
          info: InfoEntity(work: largeWorkList),
          workspaceAccount: 1,
          workspaceId: '1',
          phoneNumber: '*********',
          avatarThumbPattern: 'https://example.com/avatar-thumb.jpg',
          coverThumbPattern: 'https://example.com/cover-thumb.jpg',
          userDepartment: 'Large Department',
          userRole: 'Large Role',
        );

        final stopwatch = Stopwatch()..start();

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: largeAssignee));
        await tester.pumpAndSettle();

        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        expect(find.byType(JsonViewer), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle assignee with zero id',
          (WidgetTester tester) async {
        // Arrange
        final zeroIdAssignee = testAssigneeEntity.copyWith(id: 0);

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: zeroIdAssignee));
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;
        expect(jsonData['id'], 0);
      });

      testWidgets('should handle assignee with special characters',
          (WidgetTester tester) async {
        // Arrange
        final specialCharAssignee = AssigneeEntity(
          id: 1,
          displayName: 'User with émojis 🚀 and "quotes"',
          lang: 'vi',
          fullName: 'Full Name with\nnewlines',
          cover: 'https://example.com/cover.jpg',
          avatar: 'https://example.com/avatar.jpg',
          email: '<EMAIL>',
          linkProfile: 'https://example.com/profile',
          info: InfoEntity(work: []),
          workspaceAccount: 1,
          workspaceId: '1',
          phoneNumber: '*********',
          avatarThumbPattern: 'https://example.com/avatar-thumb.jpg',
          coverThumbPattern: 'https://example.com/cover-thumb.jpg',
          userDepartment: 'Special Department',
          userRole: 'Special Role',
        );

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: specialCharAssignee));
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;
        expect(jsonData['displayName'], contains('🚀'));
        expect(jsonData['fullName'], contains('\n'));
      });

      testWidgets('should handle assignee with empty strings',
          (WidgetTester tester) async {
        // Arrange
        final emptyStringAssignee = AssigneeEntity(
          id: 1,
          displayName: '',
          lang: '',
          fullName: '',
          cover: '',
          avatar: '',
          email: '',
          linkProfile: '',
          info: InfoEntity(work: []),
          workspaceAccount: 0,
          workspaceId: '',
          phoneNumber: '',
          avatarThumbPattern: '',
          coverThumbPattern: '',
          userDepartment: '',
          userRole: '',
        );

        // Act
        await tester
            .pumpWidget(createTestWidget(assigneeEntity: emptyStringAssignee));
        await tester.pumpAndSettle();

        // Assert
        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        final jsonData = jsonViewer.jsonObj as Map<String, dynamic>;
        expect(jsonData['displayName'], '');
        expect(jsonData['email'], '');
      });
    });

    group('Type Safety', () {
      testWidgets('should maintain type safety', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final assigneePage = tester
            .widget<ExampleAssigneePage>(find.byType(ExampleAssigneePage));
        expect(assigneePage, isA<ExampleAssigneePage>());
        expect(assigneePage.assigneeEntity, isA<AssigneeEntity>());

        final jsonViewer = tester.widget<JsonViewer>(find.byType(JsonViewer));
        expect(jsonViewer.jsonObj, isA<Map<String, dynamic>>());
      });
    });

    group('Accessibility', () {
      testWidgets('should have accessible app bar',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.title, isA<Text>());

        final titleText = appBar.title as Text;
        expect(titleText.data, 'User details');
      });

      testWidgets('should have scrollable content for accessibility',
          (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SingleChildScrollView), findsOneWidget);
      });
    });
  });
}
