import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core_v2/base/bloc/base_event.dart';
import 'package:example/presentation/test/bloc/test_state.dart';
import 'package:example/presentation/test/bloc/test_event.dart';

/// Simple test bloc for testing without dependencies
class SimpleTestBloc extends Bloc<CoreV2BaseEvent, TestState> {
  SimpleTestBloc() : super(const TestState("test")) {
    on<TestEvent>(_onTestEvent);
    on<TestCounterEvent>(_onTestCounterEvent);
  }

  Future<void> _onTestEvent(TestEvent event, Emitter<TestState> emit) async {
    try {
      throw Exception("test exception");
    } catch (e) {
      addError(e);
    }
  }

  Future<void> _onTestCounterEvent(TestCounterEvent event, Emitter<TestState> emit) async {
    emit(TestState(event.counter.toString()));
  }
}
