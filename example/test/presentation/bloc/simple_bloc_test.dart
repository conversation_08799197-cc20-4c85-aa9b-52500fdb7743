import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/bloc/common/common.dart';
import 'package:example/presentation/test/bloc/test_state.dart';
import 'package:example/presentation/test/bloc/test_event.dart';

import 'simple_test_bloc.dart';

void main() {
  group('SimpleTestBloc', () {
    late SimpleTestBloc simpleTestBloc;
    late CommonBloc commonBloc;

    setUp(() {
      commonBloc = CommonBloc();

      // Register CommonBloc in GetIt
      if (GetIt.I.isRegistered<CommonBloc>(instanceName: 'kCommonBloc')) {
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');
      }
      GetIt.I.registerSingleton<CommonBloc>(commonBloc, instanceName: 'kCommonBloc');

      simpleTestBloc = SimpleTestBloc();
    });

    tearDown(() {
      simpleTestBloc.close();
      commonBloc.close();
      if (GetIt.I.isRegistered<CommonBloc>(instanceName: 'kCommonBloc')) {
        GetIt.I.unregister<CommonBloc>(instanceName: 'kCommonBloc');
      }
    });

    test('initial state should be TestState with "test"', () {
      expect(simpleTestBloc.state, const TestState("test"));
    });

    group('TestEvent', () {
      blocTest<SimpleTestBloc, TestState>(
        'should throw exception when TestEvent is added',
        build: () => SimpleTestBloc(),
        act: (bloc) => bloc.add(const TestEvent()),
        errors: () => [isA<Exception>()],
      );
    });

    group('TestCounterEvent', () {
      blocTest<SimpleTestBloc, TestState>(
        'should emit new state with counter value when TestCounterEvent is added',
        build: () => SimpleTestBloc(),
        act: (bloc) => bloc.add(const TestCounterEvent(100)),
        expect: () => [const TestState("100")],
      );

      blocTest<SimpleTestBloc, TestState>(
        'should handle different counter values',
        build: () => SimpleTestBloc(),
        act: (bloc) {
          bloc.add(const TestCounterEvent(50));
          bloc.add(const TestCounterEvent(200));
        },
        expect: () => [
          const TestState("50"),
          const TestState("200"),
        ],
      );

      blocTest<SimpleTestBloc, TestState>(
        'should handle zero and negative counter values',
        build: () => SimpleTestBloc(),
        act: (bloc) {
          bloc.add(const TestCounterEvent(0));
          bloc.add(const TestCounterEvent(-1));
        },
        expect: () => [
          const TestState("0"),
          const TestState("-1"),
        ],
      );
    });

    group('State Management', () {
      test('should maintain state consistency', () {
        expect(simpleTestBloc.state.test, "test");
        
        simpleTestBloc.add(const TestCounterEvent(42));
        
        // Note: State change is async, so we can't test immediately
        expect(simpleTestBloc.state.test, "test"); // Still initial state
      });

      test('should be closable', () {
        expect(() => simpleTestBloc.close(), returnsNormally);
      });
    });

    group('Event Handling', () {
      test('should handle multiple events', () {
        expect(() {
          simpleTestBloc.add(const TestCounterEvent(1));
          simpleTestBloc.add(const TestCounterEvent(2));
          simpleTestBloc.add(const TestCounterEvent(3));
        }, returnsNormally);
      });

      test('should handle events after state changes', () {
        simpleTestBloc.add(const TestCounterEvent(100));
        
        expect(() {
          simpleTestBloc.add(const TestCounterEvent(200));
        }, returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle TestEvent errors gracefully', () {
        expect(() {
          simpleTestBloc.add(const TestEvent());
        }, returnsNormally); // Adding event should not throw, but processing will
      });
    });

    group('Performance', () {
      test('should handle rapid event additions', () {
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 100; i++) {
          simpleTestBloc.add(TestCounterEvent(i));
        }
        
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should create and close efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 10; i++) {
          final bloc = SimpleTestBloc();
          bloc.close();
        }
        
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });

    group('Type Safety', () {
      test('should have correct types', () {
        expect(simpleTestBloc, isA<SimpleTestBloc>());
        expect(simpleTestBloc.state, isA<TestState>());
        expect(simpleTestBloc.stream, isA<Stream<TestState>>());
      });

      test('should handle event types correctly', () {
        expect(() {
          simpleTestBloc.add(const TestEvent());
          simpleTestBloc.add(const TestCounterEvent(42));
        }, returnsNormally);
      });
    });
  });
}
