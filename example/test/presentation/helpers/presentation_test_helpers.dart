import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:example/presentation/test/bloc/test_bloc.dart';
import 'package:example/presentation/test/bloc/test_state.dart';
import 'package:example/presentation/login/login_page.dart';
import 'package:example/data/model/auth/response/auth/auth_check_mail_response.dart';
import 'package:gp_core_v2/base/usecase/model/response/api_response_v2.dart';
// Note: Import mock files as needed in your specific tests

/// Helper class for presentation layer testing
class PresentationTestHelpers {
  
  /// Create a test app with bloc provider
  static Widget createTestApp({
    required TestBloc testBloc,
    Widget? home,
    List<NavigatorObserver>? navigatorObservers,
  }) {
    return MaterialApp(
      navigatorObservers: navigatorObservers ?? [],
      home: BlocProvider<TestBloc>(
        create: (context) => testBloc,
        child: home ?? const ExampleLoginPage(),
      ),
    );
  }

  /// Create a test app with multiple bloc providers
  static Widget createMultiBlocTestApp({
    required List<BlocProvider> providers,
    Widget? home,
    List<NavigatorObserver>? navigatorObservers,
  }) {
    return MaterialApp(
      navigatorObservers: navigatorObservers ?? [],
      home: MultiBlocProvider(
        providers: providers,
        child: home ?? const ExampleLoginPage(),
      ),
    );
  }

  /// Create a simple test widget with bloc builder
  static Widget createBlocBuilderTestWidget({
    required TestBloc testBloc,
    required Widget Function(BuildContext, TestState) builder,
  }) {
    return MaterialApp(
      home: BlocProvider<TestBloc>(
        create: (context) => testBloc,
        child: Scaffold(
          body: BlocBuilder<TestBloc, TestState>(
            builder: builder,
          ),
        ),
      ),
    );
  }

  /// Create a test widget with bloc listener
  static Widget createBlocListenerTestWidget({
    required TestBloc testBloc,
    required void Function(BuildContext, TestState) listener,
    required Widget child,
  }) {
    return MaterialApp(
      home: BlocProvider<TestBloc>(
        create: (context) => testBloc,
        child: Scaffold(
          body: BlocListener<TestBloc, TestState>(
            listener: listener,
            child: child,
          ),
        ),
      ),
    );
  }

  /// Create a test widget with bloc consumer
  static Widget createBlocConsumerTestWidget({
    required TestBloc testBloc,
    required void Function(BuildContext, TestState) listener,
    required Widget Function(BuildContext, TestState) builder,
  }) {
    return MaterialApp(
      home: BlocProvider<TestBloc>(
        create: (context) => testBloc,
        child: Scaffold(
          body: BlocConsumer<TestBloc, TestState>(
            listener: listener,
            builder: builder,
          ),
        ),
      ),
    );
  }

  /// Setup mock auth use case for success response
  /// Note: Import MockAuthCheckMailUseCase from your test_helper.mocks.dart
  static void setupMockAuthUseCaseSuccess(
    dynamic mockUseCase, // Use dynamic to avoid import issues
  ) {
    when(mockUseCase.buildUseCase(any)).thenAnswer(
      (_) async => ApiResponseV2(
        status: 'success',
        data: AuthCheckMailResponse(
          userId: 123,
          newDomain: true,
          salt: 'test_salt',
        ),
      ),
    );
  }

  /// Setup mock auth use case for error response
  static void setupMockAuthUseCaseError(
    dynamic mockUseCase, // Use dynamic to avoid import issues
  ) {
    when(mockUseCase.buildUseCase(any)).thenAnswer(
      (_) async => ApiResponseV2(
        status: 'error',
        data: AuthCheckMailResponse(
          userId: 0,
          newDomain: false,
          salt: '',
        ),
      ),
    );
  }

  /// Setup mock auth use case to throw exception
  static void setupMockAuthUseCaseException(
    dynamic mockUseCase, // Use dynamic to avoid import issues
    Exception exception,
  ) {
    when(mockUseCase.buildUseCase(any)).thenThrow(exception);
  }

  /// Verify that a specific event was added to bloc
  static void verifyEventAdded(
    TestBloc bloc,
    dynamic event, // Use dynamic to avoid import issues
  ) {
    // Note: This is a conceptual verification
    // In practice, you might need to use bloc_test package for better verification
    expect(bloc.state, isA<TestState>());
  }

  /// Pump and settle with custom duration
  static Future<void> pumpAndSettleWithTimeout(
    WidgetTester tester, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    await tester.pumpAndSettle(const Duration(milliseconds: 100));
  }

  /// Find widget by text with retry mechanism
  static Future<Finder> findTextWithRetry(
    WidgetTester tester,
    String text, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      final finder = find.text(text);
      if (tester.any(finder)) {
        return finder;
      }
      await tester.pump(delay);
    }
    return find.text(text);
  }

  /// Tap widget with retry mechanism
  static Future<void> tapWithRetry(
    WidgetTester tester,
    Finder finder, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      try {
        await tester.tap(finder);
        await tester.pump();
        return;
      } catch (e) {
        if (i == maxRetries - 1) rethrow;
        await tester.pump(delay);
      }
    }
  }

  /// Enter text with validation
  static Future<void> enterTextSafely(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    expect(finder, findsOneWidget);
    await tester.enterText(finder, text);
    await tester.pump();
    expect(find.text(text), findsOneWidget);
  }

  /// Scroll to widget if not visible
  static Future<void> scrollToWidget(
    WidgetTester tester,
    Finder finder, {
    Finder? scrollable,
    double delta = 100.0,
  }) async {
    final scrollableFinder = scrollable ?? find.byType(Scrollable);
    
    if (tester.any(finder)) return;
    
    // Try scrolling down
    for (int i = 0; i < 10; i++) {
      await tester.drag(scrollableFinder, Offset(0, -delta));
      await tester.pump();
      if (tester.any(finder)) return;
    }
    
    // Try scrolling up
    for (int i = 0; i < 20; i++) {
      await tester.drag(scrollableFinder, Offset(0, delta));
      await tester.pump();
      if (tester.any(finder)) return;
    }
  }

  /// Wait for specific condition
  static Future<void> waitForCondition(
    WidgetTester tester,
    bool Function() condition, {
    Duration timeout = const Duration(seconds: 5),
    Duration interval = const Duration(milliseconds: 100),
  }) async {
    final stopwatch = Stopwatch()..start();
    
    while (!condition() && stopwatch.elapsed < timeout) {
      await tester.pump(interval);
    }
    
    stopwatch.stop();
    
    if (!condition()) {
      throw TimeoutException(
        'Condition not met within timeout',
        timeout,
      );
    }
  }

  /// Test widget performance
  static Future<Duration> measureWidgetBuildTime(
    WidgetTester tester,
    Widget widget,
  ) async {
    final stopwatch = Stopwatch()..start();
    await tester.pumpWidget(widget);
    stopwatch.stop();
    return stopwatch.elapsed;
  }

  /// Test widget memory usage (conceptual)
  static Future<void> testMemoryUsage(
    WidgetTester tester,
    Widget widget, {
    int iterations = 100,
  }) async {
    // Build and dispose widget multiple times
    for (int i = 0; i < iterations; i++) {
      await tester.pumpWidget(widget);
      await tester.pumpWidget(Container());
      
      // Force garbage collection periodically
      if (i % 10 == 0) {
        await tester.pump();
      }
    }
  }

  /// Create mock navigator observer
  /// Note: Import MockNavigatorObserver from your test_helper.mocks.dart
  static dynamic createMockNavigatorObserver() {
    // Return mock navigator observer
    // Implementation depends on your mock setup
    return null; // Placeholder
  }

  /// Verify navigation occurred
  static void verifyNavigation(
    dynamic observer, // Use dynamic to avoid import issues
    String routeName,
  ) {
    // verify(observer.didPush(any, any)).called(greaterThan(0));
    // Implementation depends on your mock setup
  }

  /// Create test data for forms
  static Map<String, String> createTestFormData() {
    return {
      'email': '<EMAIL>',
      'password': 'password123',
      'username': 'testuser',
      'phone': '+84123456789',
    };
  }

  /// Fill form fields with test data
  static Future<void> fillFormFields(
    WidgetTester tester,
    Map<String, String> data,
  ) async {
    for (final entry in data.entries) {
      final finder = find.byWidgetPredicate(
        (widget) => widget is TextFormField,
      );
      
      if (tester.any(finder)) {
        await enterTextSafely(tester, finder.first, entry.value);
      }
    }
  }

  /// Test accessibility
  static Future<void> testAccessibility(
    WidgetTester tester,
    Widget widget,
  ) async {
    await tester.pumpWidget(widget);
    
    // Test semantic labels
    // Note: Updated for newer Flutter versions
    expect(tester.binding, isNotNull);
    
    // Verify no accessibility issues
    await expectLater(tester, meetsGuideline(androidTapTargetGuideline));
    await expectLater(tester, meetsGuideline(iOSTapTargetGuideline));
    await expectLater(tester, meetsGuideline(labeledTapTargetGuideline));
    await expectLater(tester, meetsGuideline(textContrastGuideline));
  }

  /// Cleanup resources
  static void cleanup() {
    // Cleanup any global state or resources
  }

  /// Reset all mocks
  static void resetMocks(List<Mock> mocks) {
    for (final mock in mocks) {
      reset(mock);
    }
  }

  /// Create test theme
  static ThemeData createTestTheme() {
    return ThemeData(
      primarySwatch: Colors.blue,
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }

  /// Create test app with custom theme
  static Widget createThemedTestApp({
    required Widget child,
    ThemeData? theme,
  }) {
    return MaterialApp(
      theme: theme ?? createTestTheme(),
      home: child,
    );
  }
}

/// Exception for timeout scenarios
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}
