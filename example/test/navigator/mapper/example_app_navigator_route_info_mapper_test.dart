import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:example/navigator/mapper/example_app_navigator_route_info_mapper.dart';
import 'package:example/navigator/model/example_app_info_route.dart';
import 'package:example/domain/entity/test.entity.dart';
import 'package:example/domain/entity/test/assignee.dart';
import 'package:gp_core_v2/base/navigator/mapper/base_route_info_mapper.dart';

void main() {
  group('AppNavigatorRouteInfoMapper', () {
    late AppNavigatorRouteInfoMapper mapper;

    setUp(() {
      mapper = AppNavigatorRouteInfoMapper();
    });

    group('Mapper Interface', () {
      test('should extend GPBaseRouteInfoMapper', () {
        expect(mapper,
            isA<GPBaseRouteInfoMapper<RouteSettings, ExampleAppInfoRoute>>());
      });

      test('should implement map method', () {
        // Arrange
        const route = ExampleAppInfoRoute.initial();

        // Act & Assert
        expect(() => mapper.map(route), returnsNormally);
      });
    });

    group('Initial Route Mapping', () {
      test('should map initial route correctly', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.initial();

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings, isA<RouteSettings>());
        expect(routeSettings.name, kExampleInitial);
        expect(routeSettings.arguments, isNull);
      });

      test('should map initial route with custom path', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.initial(route: '/custom');

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.name, '/custom');
        expect(routeSettings.arguments, isNull);
      });
    });

    group('Login Route Mapping', () {
      test('should map login route correctly', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.login();

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings, isA<RouteSettings>());
        expect(routeSettings.name, kExampleLogin);
        expect(routeSettings.arguments, isNull);
      });

      test('should map login route with custom path', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.login(route: '/custom-login');

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.name, '/custom-login');
        expect(routeSettings.arguments, isNull);
      });
    });

    group('Home Route Mapping', () {
      test('should map home route correctly', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.home();

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings, isA<RouteSettings>());
        expect(routeSettings.name, kExampleHome);
        expect(routeSettings.arguments, isNull);
      });

      test('should map home route with custom path', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.home(route: '/custom-home');

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.name, '/custom-home');
        expect(routeSettings.arguments, isNull);
      });
    });

    group('User Profile Route Mapping', () {
      test('should map user profile route correctly', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        const infoRoute = ExampleAppInfoRoute.userProfile(user: user);

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings, isA<RouteSettings>());
        expect(routeSettings.name, kExampleUserDetails);
        expect(routeSettings.arguments, user);
      });

      test('should map user profile route with custom path', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        const infoRoute = ExampleAppInfoRoute.userProfile(
          route: '/custom-user',
          user: user,
        );

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.name, '/custom-user');
        expect(routeSettings.arguments, user);
      });

      test('should preserve user data in arguments', () {
        // Arrange
        const user = User(id: 123, name: 'John Doe');
        const infoRoute = ExampleAppInfoRoute.userProfile(user: user);

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        final mappedUser = routeSettings.arguments as User;
        expect(mappedUser.id, 123);
        expect(mappedUser.name, 'John Doe');
      });
    });

    group('Assignee Details Route Mapping', () {
      test('should map assignee details route correctly', () {
        // Arrange
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');
        final infoRoute = ExampleAppInfoRoute.assigneeDetails(entity: assignee);

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings, isA<RouteSettings>());
        expect(routeSettings.name, kExampleAssigneeDetails);
        expect(routeSettings.arguments, assignee);
      });

      test('should map assignee details route with custom path', () {
        // Arrange
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');
        final infoRoute = ExampleAppInfoRoute.assigneeDetails(
          route: '/custom-assignee',
          entity: assignee,
        );

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.name, '/custom-assignee');
        expect(routeSettings.arguments, assignee);
      });

      test('should preserve assignee data in arguments', () {
        // Arrange
        final assignee = AssigneeEntity(id: 456, displayName: 'Jane Smith');
        final infoRoute = ExampleAppInfoRoute.assigneeDetails(entity: assignee);

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        final mappedAssignee = routeSettings.arguments as AssigneeEntity;
        expect(mappedAssignee.id, 456);
        expect(mappedAssignee.displayName, 'Jane Smith');
      });
    });

    group('Route Settings Properties', () {
      test('should create RouteSettings with correct type', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.initial();

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings, isA<RouteSettings>());
        expect(routeSettings.name, isA<String>());
      });

      test('should handle routes without arguments', () {
        // Arrange
        const routes = [
          ExampleAppInfoRoute.initial(),
          ExampleAppInfoRoute.login(),
          ExampleAppInfoRoute.home(),
        ];

        // Act & Assert
        for (final route in routes) {
          final routeSettings = mapper.map(route);
          expect(routeSettings.arguments, isNull);
        }
      });

      test('should handle routes with arguments', () {
        // Arrange
        const user = User(id: 1, name: 'Test');
        final assignee = AssigneeEntity(id: 1, displayName: 'Test');
        final routes = [
          ExampleAppInfoRoute.userProfile(user: user),
          ExampleAppInfoRoute.assigneeDetails(entity: assignee),
        ];

        // Act & Assert
        for (final route in routes) {
          final routeSettings = mapper.map(route);
          expect(routeSettings.arguments, isNotNull);
        }
      });
    });

    group('Error Handling', () {
      test('should handle all route types without throwing', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');
        final routes = [
          const ExampleAppInfoRoute.initial(),
          const ExampleAppInfoRoute.login(),
          const ExampleAppInfoRoute.home(),
          const ExampleAppInfoRoute.userProfile(user: user),
          ExampleAppInfoRoute.assigneeDetails(entity: assignee),
        ];

        // Act & Assert
        for (final route in routes) {
          expect(() => mapper.map(route), returnsNormally);
        }
      });

      test('should create valid RouteSettings for all routes', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');
        final routes = [
          const ExampleAppInfoRoute.initial(),
          const ExampleAppInfoRoute.login(),
          const ExampleAppInfoRoute.home(),
          const ExampleAppInfoRoute.userProfile(user: user),
          ExampleAppInfoRoute.assigneeDetails(entity: assignee),
        ];

        // Act & Assert
        for (final route in routes) {
          final routeSettings = mapper.map(route);
          expect(routeSettings, isA<RouteSettings>());
          expect(routeSettings.name, isNotNull);
          expect(routeSettings.name, isA<String>());
        }
      });
    });

    group('Mapping Consistency', () {
      test('should produce consistent results for same input', () {
        // Arrange
        const infoRoute = ExampleAppInfoRoute.initial();

        // Act
        final routeSettings1 = mapper.map(infoRoute);
        final routeSettings2 = mapper.map(infoRoute);

        // Assert
        expect(routeSettings1.name, routeSettings2.name);
        expect(routeSettings1.arguments, routeSettings2.arguments);
      });

      test('should handle multiple mappers independently', () {
        // Arrange
        final mapper1 = AppNavigatorRouteInfoMapper();
        final mapper2 = AppNavigatorRouteInfoMapper();
        const infoRoute = ExampleAppInfoRoute.home();

        // Act
        final routeSettings1 = mapper1.map(infoRoute);
        final routeSettings2 = mapper2.map(infoRoute);

        // Assert
        expect(routeSettings1.name, routeSettings2.name);
        expect(routeSettings1.arguments, routeSettings2.arguments);
      });
    });

    group('Type Safety', () {
      test('should maintain type safety for arguments', () {
        // Arrange
        const user = User(id: 1, name: 'Test User');
        const infoRoute = ExampleAppInfoRoute.userProfile(user: user);

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.arguments, isA<User>());
        expect(routeSettings.arguments, isNot(isA<AssigneeEntity>()));
      });

      test('should maintain type safety for assignee arguments', () {
        // Arrange
        final assignee = AssigneeEntity(id: 1, displayName: 'Test Assignee');
        final infoRoute = ExampleAppInfoRoute.assigneeDetails(entity: assignee);

        // Act
        final routeSettings = mapper.map(infoRoute);

        // Assert
        expect(routeSettings.arguments, isA<AssigneeEntity>());
        expect(routeSettings.arguments, isNot(isA<User>()));
      });
    });
  });
}
