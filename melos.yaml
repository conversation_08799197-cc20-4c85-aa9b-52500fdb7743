name: modular

packages:
  - gp_core_v2/**
  - example/**

scripts:
  formatting:check: melos exec -- dart format --set-exit-if-changed . #flutter format

  analyze: melos exec -- dart analyze --fatal-infos

  test-coverage:
    run: melos exec --dir-exists=test --flutter -- "flutter test --coverage ; genhtml ./coverage/lcov.info --output-directory ./coverage/out"
    description: Run flutter test with coverage and generates coverage report

  generate:
    run: melos exec $MELOS_FILTERS -c 1 --depends-on="build_runner" -- "dart run build_runner build --delete-conflicting-outputs"
    description: Build all generated files.

  check-format:
    exec: dart format --set-exit-if-changed .
    description: Check the format of a specific package in this project.

  format:
    exec: dart format .
    description: Format a specific package in this project.

  version:
    description: Updates version numbers in all build files
    run: bash scripts/version.sh

  test:
    run: melos run test:dart --no-select ; melos run test:flutter --no-select
    description: Run all Dart & Flutter tests in this project.

  test:dart:
    run: melos exec -c 1 --fail-fast -- "dart test test"
    description: Run Dart tests for a specific package in this project.
    # select-package:
    #   flutter: false
    #   dir-exists: test

    packageFilters:
      noPrivate: true
      dirExists: test

  test:flutter:
    run: melos exec -c 1 --fail-fast -- "flutter test test"
    description: Run Flutter tests for a specific package in this project.
    # select-package:
    #   flutter: true
    #   dir-exists: test

    packageFilters:
      noPrivate: true
      dirExists: test

  pana:
    run: melos exec -c 10 -- "../../tool/verify_pub_score.sh"
    # select-package:
    #   no-private: true
    packageFilters:
      noPrivate: true
    description: Checks if the package meets the pub.dev score requirement.

  pu:
    packageFilters:
      flutter: true

    run: bash "script/git/remove_pubspec_overrides.sh" ; melos exec -c 1 flutter pub upgrade --major-versions

    description: Upgrade all dependencies.

  pg:
    packageFilters:
      flutter: true

    run: bash "script/git/remove_pubspec_overrides.sh"; bash "script/flutter/clean.sh"

    description: Clean and Pub get all dependencies (apply for ios)

  fpg:
    packageFilters:
      flutter: true

    run: bash "script/git/remove_pubspec_overrides.sh" ;  melos exec -c 1 flutter pub get

    description: Pub get all dependencies

  bump:
    packageFilters:
      flutter: true

    run: melos exec -c 1 git add . ; melos exec -c 1 git commit -am 'chore:\ Bump'

    description: Add and commit all.

command:
  version:
    releaseUrl: true
    updateGitTagRefs: true
    workspaceChangelog: true
    message: |
      chore: cut package releases

      {new_package_versions}

  # postbootstrap: melos bs

  bootstrap:
    usePubspecOverrides: true
