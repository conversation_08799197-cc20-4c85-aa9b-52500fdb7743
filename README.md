# Gapo Flutter core v2

## GPCoreV3 (gp_core_v2 version 2.0.0)
### Bloc:
    - <PERSON><PERSON> sung `transformer` cho các event:
        - Tr<PERSON><PERSON> các trường hợp user click liên tục vào button, dẫn đến các event được emit liên tục. Tr<PERSON>ớc anh em hay xử lý trên UI <= avoid
        - Ví dụ: khi sử dụng `distinct` cho event, các event giống nhau sẽ được bỏ qua. Ngoài ra, có các method khác như: `throttleTime`, `debounceTime`, `exhaustMap`, `switchMap`, `asyncExpand`... đều là method cơ bản của `rxdart`.
        - Bloc:
            ```dart 
                Bloc:
                on<TestCounterEvent>(
                    _onTestCounterEvent,
                    // ignore the same events
                    transformer: distinct(),
                );
            ```
        - Event:
            ```dart
                final class TestCounterEvent extends CoreV2BaseEvent {
                    const TestCounterEvent(this.counter);

                    final int counter;

                    @override
                    List<Object?> get props => [counter];
                }
            ```

### Presentation:
- Thêm `AppNavigator` wrap lại các phần navigator, giúp việc chuyển đổi giữa các navigation framework dễ dàng hơn. Developer chỉ cần call các phương thức cơ bản như `pop`, `push`,... chứ không cần quan tâm Navigator xử lý như thế nào, sử dụng GetX, GoRouter hay AutoRoute, routermaster hay beamer...
- Sử dụng:
    ```dart
        context.appNavigator().popAndPush(
          context,
          ExampleAppInfoRoute.home(),
        );
    ```
- Chi tiết tại: [AppNavigator](example/lib/navigator/app_navigator/app_navigator_go_route_impl.dart)


## Target:
- Cải thiện chất lượng code, chất lượng sản phẩm:
    - Đảm bảo tính consistency trong toàn bộ project:
        - Tạo thêm các class trung gian như: `middleware`, `DatetimeInterceptor`, `Object Mapper`, `object case-when`.
        - Thêm các phần UI doc generator: storybook.
    - Giảm thiểu số lượng code lặp lại:
        - Thêm các phần generator về:
            - Data: 
                - `Json Serializable`, `Json DeSerializable`.
                - `Object Mapper`: chia rõ DTO và Entity.
                - `equals`, `copywith`.
            - Dependency Injection (DI): `DI generator` giúp quản lý các flavor hiệu quả hơn.
            - Navigation: `route generator`: quản lý tập trung các route, bao gồm cả deepLink.
            - Assets: `Localization generator` (has been done before).
            - UI doc: `story book`.
    - Chia lại sub modules.
    - Thêm các phần code metrics. [E.g: dart code metrics](https://dcm.dev/docs/metrics/)
- Đảm bảo tính ổn định của các features hiện có:
    - Base Project mới chạy song song, hoạt động độc lập, áp dụng cho các feature mới.
    - Làm lại / Bổ sung các phần check `error_code`.

## Challenge:
- Đảm bảo clean code.
- Hạn chế boilerplate khi generated code quá nhiều.

## Bloc
[Bloc](docs/core.bloc.md)

## Generator
[Generator](docs/core.generator.md)

## Flutter dependencies:

```
flutter pub add flutter_bloc get_it injectable retrofit dio intl go_router auto_mappr_annotation json_annotation freezed_annotation
```

## Flutter dev dependencies:

```
flutter pub add dev:build_runner dev:freezed dev:json_serializable dev:json_annotation dev:injectable_generator dev:auto_mappr dev:retrofit_generator dev:mock_web_server 
```
