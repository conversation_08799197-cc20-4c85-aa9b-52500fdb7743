name: gp_core_v2
description: Gapo Flutter core v2
version: 2.0.0
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: "3.29.1"

dependency_overrides:
  intl: 0.20.2
  http: 1.3.0
  dart_style: 3.0.1
  pub_semver: 2.2.0
  flutter_sticky_header: 0.7.0
  sentry: 8.14.0
  sentry_flutter: 8.14.0
  sentry_logging: 8.14.0
  sentry_dio: 8.14.0
  source_gen:
    git:
      url: **********************:flutter/utils/gp_source_gen.git
      path: "source_gen"
      ref: "feat/3.29.1"

dependencies:
  flutter:
    sdk: flutter

  get_it: 8.0.3
  talker: 4.7.1
  talker_flutter: 4.7.1
  talker_bloc_logger: 4.7.1
  flutter_bloc: 9.1.0
  injectable: 2.5.0
  logger: 2.5.0
  retrofit: 4.4.2
  freezed_annotation: 3.0.0

  # utils
  equatable: 2.0.7

  gp_core:
    git:
      url: **********************:flutter/core/gp_core.git
      ref: "develop"

  navigation_history_observer: 1.1.1
  flutter_list_view: 1.1.29

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: 5.0.0

  build_runner: 2.4.15

  injectable_generator: 2.7.0

  mockito: 5.4.5
  freezed: 3.0.4
  json_serializable: 6.9.4
  json_annotation: 4.9.0
# flutter:
