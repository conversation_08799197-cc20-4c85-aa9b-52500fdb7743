//@GeneratedMicroModule;GpCoreV2PackageModule;package:gp_core_v2/di/di.component.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:gp_core/base/networking/base/interceptors/logger_interceptor.dart'
    as _i842;
import 'package:gp_core/core.dart' as _i85;
import 'package:gp_core_v2/base/bloc/common/common_bloc.dart' as _i151;
import 'package:gp_core_v2/base/configs/app/app.configs.dart' as _i137;
import 'package:gp_core_v2/base/exception/exception.dart' as _i859;
import 'package:gp_core_v2/base/exception/handler/exception_handler.dart'
    as _i620;
import 'package:gp_core_v2/base/exception/handler/exception_message_mapper.dart'
    as _i835;
import 'package:gp_core_v2/base/exception/mapper/dio_exception_mapper.dart'
    as _i600;
import 'package:gp_core_v2/base/network/interceptor/connectivity.interceptor.dart'
    as _i209;
import 'package:gp_core_v2/di/modules/client.module.dart' as _i359;
import 'package:gp_core_v2/di/modules/url.module.dart' as _i988;
import 'package:gp_dio_log/gp_dio_log.dart' as _i382;
import 'package:injectable/injectable.dart' as _i526;

const String _SaasDevelop = 'SaasDevelop';
const String _SaasQa = 'SaasQa';
const String _OnPremiseDevelop = 'OnPremiseDevelop';
const String _OnPremiseQa = 'OnPremiseQa';

class GpCoreV2PackageModule extends _i526.MicroPackageModule {
// initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final urlModule = _$UrlModule();
    final clientModule = _$ClientModule();
    gh.singleton<_i209.ConnectivityInterceptor>(
        () => _i209.ConnectivityInterceptor());
    gh.singleton<_i137.AppUseCaseManagement>(
        () => _i137.AppUseCaseManagement());
    gh.singleton<String>(
      () => urlModule.kApiBaseUrl,
      instanceName: 'kApiBaseUrl',
    );
    gh.lazySingleton<_i382.GPDioLogInterceptor>(
      () => clientModule.kGPDioLogInterceptor,
      registerFor: {
        _SaasDevelop,
        _SaasQa,
        _OnPremiseDevelop,
        _OnPremiseQa,
      },
    );
    gh.lazySingleton<_i842.LoggerInterceptor>(
      () => clientModule.kLoggerInterceptor,
      registerFor: {
        _SaasDevelop,
        _SaasQa,
        _OnPremiseDevelop,
        _OnPremiseQa,
      },
    );
    gh.singleton<_i85.BaseOptions>(() => clientModule.kBaseOptions);
    gh.singleton<List<_i85.Interceptor>>(() => clientModule.kInterceptors);
    gh.singleton<_i85.Dio>(
      () => clientModule.createRefreshTokenDio(),
      instanceName: 'kRefreshTokenDio',
    );
    gh.singleton<_i85.Dio>(
      () => clientModule.createUploadDio(),
      instanceName: 'kUploadDio',
    );
    gh.singleton<_i85.Dio>(
      () => clientModule.createDio(
        options: gh<_i85.BaseOptions>(),
        interceptors: gh<List<_i85.Interceptor>>(),
      ),
      instanceName: 'kDio',
    );
    gh.singleton<_i151.CommonBloc>(
      () => _i151.CommonBloc(),
      instanceName: 'kCommonBloc',
    );
    gh.singleton<_i835.ExceptionMessageMapper>(
      () => const _i835.ExceptionMessageMapper(),
      instanceName: 'kExceptionMessageMapper',
    );
    gh.singleton<_i600.DioExceptionMapper>(
      () => _i600.DioExceptionMapper(),
      instanceName: 'kDioExceptionMapper',
    );
    gh.singleton<_i620.ExceptionHandler>(
      () => _i620.ExceptionHandlerImpl(gh<_i859.ExceptionMessageMapper>(
          instanceName: 'kExceptionMessageMapper')),
      instanceName: 'kExceptionHandler',
    );
  }
}

class _$UrlModule extends _i988.UrlModule {}

class _$ClientModule extends _i359.ClientModule {}
