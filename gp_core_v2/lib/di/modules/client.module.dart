/*
 * Created Date: 5/12/2023 11:04:39
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 21st February 2024 10:56:36
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core/base/networking/base/interceptors/authentication_interceptor.dart';
import 'package:gp_core/base/networking/base/interceptors/logger_interceptor.dart';
import 'package:gp_core/base/networking/base/interceptors/token_interceptor.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/network/interceptor/null_absent_interceptor.dart';
import 'package:gp_dio_log/gp_dio_log.dart';
import 'package:injectable/injectable.dart';

import '../../base/constants/di.constants.dart';
import '../../base/constants/server/server_timeout.constants.dart';
import '../../base/network/interceptor/datetime.interceptor.dart';

@module
abstract class ClientModule {
  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kDio')
  Dio createDio({
    BaseOptions? options,
    List<Interceptor> interceptors = const [],
  }) {
    final dio = Dio(options ?? kBaseOptions);

    // Inject token 401 interceptor
    interceptors.add(TokenInterceptor(dio));

    dio.interceptors.addAll(interceptors);

    return dio;
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kRefreshTokenDio')
  Dio createRefreshTokenDio() {
    final options = BaseOptions(
      baseUrl: Constants.baseUrl,
      contentType: Headers.jsonContentType,
      connectTimeout: ServerTimeoutConstants.kRefreshTokenConnectTimeout,
      receiveTimeout: ServerTimeoutConstants.kRefreshTokenReceiveTimeout,
      sendTimeout: ServerTimeoutConstants.kRefreshTokenSendTimeout,
    );

    return Dio(options);
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  @Named('kUploadDio')
  Dio createUploadDio() {
    final options = BaseOptions(
      baseUrl: Constants.uploadDomain,
      contentType: Headers.jsonContentType,
      connectTimeout: ServerTimeoutConstants.kRefreshTokenConnectTimeout,
      receiveTimeout: ServerTimeoutConstants.kRefreshTokenReceiveTimeout,
      sendTimeout: ServerTimeoutConstants.kRefreshTokenSendTimeout,
    );

    final dio = Dio(options);
    dio.interceptors.addAll(
      [
        AuthenticationInterceptor(),
        TokenInterceptor(dio),
        LoggerInterceptor(),
        QueuedInterceptor(),
      ],
    );

    return dio;
  }

  @Singleton(order: DiConstants.kDataDioOrder)
  BaseOptions get kBaseOptions {
    return BaseOptions(
      connectTimeout: ServerTimeoutConstants.kConnectTimeout,
      receiveTimeout: ServerTimeoutConstants.kReceiveTimeout,
      sendTimeout: ServerTimeoutConstants.kSendTimeout,
      baseUrl: Constants.baseUrl,
    );
  }

  @LazySingleton(env: kFlavorDevs)
  GPDioLogInterceptor get kGPDioLogInterceptor => GPDioLogInterceptor();

  @LazySingleton(env: kFlavorDevs)
  LoggerInterceptor get kLoggerInterceptor => LoggerInterceptor();

  @Singleton(order: DiConstants.kDataDioOrder)
  List<Interceptor> get kInterceptors {
    final ret = [
      DateTimeInterceptor(),
      AuthenticationInterceptor(),
      QueuedInterceptor(),
      const NullAbsentInterceptor()
    ];

    if (GetIt.I.isRegistered<GPDioLogInterceptor>()) {
      ret.add(GetIt.I.get<GPDioLogInterceptor>());
    }

    if (GetIt.I.isRegistered<LoggerInterceptor>()) {
      ret.add(GetIt.I.get<LoggerInterceptor>());
    }

    return ret;
  }
}
