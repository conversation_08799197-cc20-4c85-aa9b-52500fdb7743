// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'common_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$CommonState {
  AppExceptionWrapper? get appExceptionWrapper;
  bool get isLoading;
  String? get boldMessage;
  String? get message;
  String? get message2;
  String? get undoTitle;
  SnackbarType? get snackbarType;
  Function()? get undoCallback;

  /// Create a copy of CommonState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CommonStateCopyWith<CommonState> get copyWith =>
      _$CommonStateCopyWithImpl<CommonState>(this as CommonState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CommonState &&
            (identical(other.appExceptionWrapper, appExceptionWrapper) ||
                other.appExceptionWrapper == appExceptionWrapper) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.boldMessage, boldMessage) ||
                other.boldMessage == boldMessage) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.message2, message2) ||
                other.message2 == message2) &&
            (identical(other.undoTitle, undoTitle) ||
                other.undoTitle == undoTitle) &&
            (identical(other.snackbarType, snackbarType) ||
                other.snackbarType == snackbarType) &&
            (identical(other.undoCallback, undoCallback) ||
                other.undoCallback == undoCallback));
  }

  @override
  int get hashCode => Object.hash(runtimeType, appExceptionWrapper, isLoading,
      boldMessage, message, message2, undoTitle, snackbarType, undoCallback);

  @override
  String toString() {
    return 'CommonState(appExceptionWrapper: $appExceptionWrapper, isLoading: $isLoading, boldMessage: $boldMessage, message: $message, message2: $message2, undoTitle: $undoTitle, snackbarType: $snackbarType, undoCallback: $undoCallback)';
  }
}

///
abstract mixin class $CommonStateCopyWith<$Res> {
  factory $CommonStateCopyWith(
          CommonState value, $Res Function(CommonState) _then) =
      _$CommonStateCopyWithImpl;
  @useResult
  $Res call(
      {AppExceptionWrapper? appExceptionWrapper,
      bool isLoading,
      String? boldMessage,
      String? message,
      String? message2,
      String? undoTitle,
      SnackbarType? snackbarType,
      dynamic Function()? undoCallback});
}

///
class _$CommonStateCopyWithImpl<$Res> implements $CommonStateCopyWith<$Res> {
  _$CommonStateCopyWithImpl(this._self, this._then);

  final CommonState _self;
  final $Res Function(CommonState) _then;

  /// Create a copy of CommonState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appExceptionWrapper = freezed,
    Object? isLoading = null,
    Object? boldMessage = freezed,
    Object? message = freezed,
    Object? message2 = freezed,
    Object? undoTitle = freezed,
    Object? snackbarType = freezed,
    Object? undoCallback = freezed,
  }) {
    return _then(_self.copyWith(
      appExceptionWrapper: freezed == appExceptionWrapper
          ? _self.appExceptionWrapper
          : appExceptionWrapper // ignore: cast_nullable_to_non_nullable
              as AppExceptionWrapper?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      boldMessage: freezed == boldMessage
          ? _self.boldMessage
          : boldMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      message2: freezed == message2
          ? _self.message2
          : message2 // ignore: cast_nullable_to_non_nullable
              as String?,
      undoTitle: freezed == undoTitle
          ? _self.undoTitle
          : undoTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      snackbarType: freezed == snackbarType
          ? _self.snackbarType
          : snackbarType // ignore: cast_nullable_to_non_nullable
              as SnackbarType?,
      undoCallback: freezed == undoCallback
          ? _self.undoCallback!
          : undoCallback // ignore: cast_nullable_to_non_nullable
              as dynamic Function()?,
    ));
  }
}

///

class _CommonState extends CommonState {
  const _CommonState(
      {this.appExceptionWrapper,
      this.isLoading = false,
      this.boldMessage,
      this.message,
      this.message2,
      this.undoTitle,
      this.snackbarType,
      this.undoCallback})
      : super._();

  @override
  final AppExceptionWrapper? appExceptionWrapper;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? boldMessage;
  @override
  final String? message;
  @override
  final String? message2;
  @override
  final String? undoTitle;
  @override
  final SnackbarType? snackbarType;
  @override
  final dynamic Function()? undoCallback;

  /// Create a copy of CommonState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CommonStateCopyWith<_CommonState> get copyWith =>
      __$CommonStateCopyWithImpl<_CommonState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CommonState &&
            (identical(other.appExceptionWrapper, appExceptionWrapper) ||
                other.appExceptionWrapper == appExceptionWrapper) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.boldMessage, boldMessage) ||
                other.boldMessage == boldMessage) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.message2, message2) ||
                other.message2 == message2) &&
            (identical(other.undoTitle, undoTitle) ||
                other.undoTitle == undoTitle) &&
            (identical(other.snackbarType, snackbarType) ||
                other.snackbarType == snackbarType) &&
            (identical(other.undoCallback, undoCallback) ||
                other.undoCallback == undoCallback));
  }

  @override
  int get hashCode => Object.hash(runtimeType, appExceptionWrapper, isLoading,
      boldMessage, message, message2, undoTitle, snackbarType, undoCallback);

  @override
  String toString() {
    return 'CommonState(appExceptionWrapper: $appExceptionWrapper, isLoading: $isLoading, boldMessage: $boldMessage, message: $message, message2: $message2, undoTitle: $undoTitle, snackbarType: $snackbarType, undoCallback: $undoCallback)';
  }
}

///
abstract mixin class _$CommonStateCopyWith<$Res>
    implements $CommonStateCopyWith<$Res> {
  factory _$CommonStateCopyWith(
          _CommonState value, $Res Function(_CommonState) _then) =
      __$CommonStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {AppExceptionWrapper? appExceptionWrapper,
      bool isLoading,
      String? boldMessage,
      String? message,
      String? message2,
      String? undoTitle,
      SnackbarType? snackbarType,
      dynamic Function()? undoCallback});
}

///
class __$CommonStateCopyWithImpl<$Res> implements _$CommonStateCopyWith<$Res> {
  __$CommonStateCopyWithImpl(this._self, this._then);

  final _CommonState _self;
  final $Res Function(_CommonState) _then;

  /// Create a copy of CommonState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? appExceptionWrapper = freezed,
    Object? isLoading = null,
    Object? boldMessage = freezed,
    Object? message = freezed,
    Object? message2 = freezed,
    Object? undoTitle = freezed,
    Object? snackbarType = freezed,
    Object? undoCallback = freezed,
  }) {
    return _then(_CommonState(
      appExceptionWrapper: freezed == appExceptionWrapper
          ? _self.appExceptionWrapper
          : appExceptionWrapper // ignore: cast_nullable_to_non_nullable
              as AppExceptionWrapper?,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      boldMessage: freezed == boldMessage
          ? _self.boldMessage
          : boldMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      message2: freezed == message2
          ? _self.message2
          : message2 // ignore: cast_nullable_to_non_nullable
              as String?,
      undoTitle: freezed == undoTitle
          ? _self.undoTitle
          : undoTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      snackbarType: freezed == snackbarType
          ? _self.snackbarType
          : snackbarType // ignore: cast_nullable_to_non_nullable
              as SnackbarType?,
      undoCallback: freezed == undoCallback
          ? _self.undoCallback
          : undoCallback // ignore: cast_nullable_to_non_nullable
              as dynamic Function()?,
    ));
  }
}

// dart format on
