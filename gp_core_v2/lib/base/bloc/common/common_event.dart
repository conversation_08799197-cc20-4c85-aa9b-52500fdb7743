/*
 * Created Date: 5/12/2023 14:53:16
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 29th December 2023 15:37:05
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/bloc/base_event.dart';
import 'package:gp_core_v2/base/exception/base/app_exception_wrapper.dart';

sealed class CommonEvent extends CoreV2BaseEvent {
  const CommonEvent();
}

class ExceptionEmitted extends CommonEvent {
  const ExceptionEmitted({
    required this.appExceptionWrapper,
  });

  final AppExceptionWrapper appExceptionWrapper;
}

class LoadingVisibilityEmitted extends CommonEvent {
  const LoadingVisibilityEmitted({
    required this.isLoading,
  });

  final bool isLoading;
}

class MessageEmitted extends CommonEvent {
  const MessageEmitted({
    required this.message,
    this.message2,
    this.boldMessage,
    this.snackbarType,
    this.undoTitle,
    this.undoCallback,
  });

  final String message;
  final String? message2;
  final String? boldMessage;

  final SnackbarType? snackbarType;

  final String? undoTitle;
  final Function()? undoCallback;
}
