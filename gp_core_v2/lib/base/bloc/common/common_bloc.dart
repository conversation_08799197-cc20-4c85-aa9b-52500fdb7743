/*
 * Created Date: 2/12/2023 10:56:3
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 29th December 2023 15:37:01
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../constants/di.constants.dart';
import '../base_bloc.dart';
import 'common.dart';

@Singleton(order: DiConstants.kBlocOrder)
@Named('kCommonBloc')
final class CommonBloc extends CoreV2BaseBloc<CommonEvent, CommonState> {
  CommonBloc() : super(const CommonState()) {
    on<ExceptionEmitted>(_onExceptionEmitted);

    on<LoadingVisibilityEmitted>(_onLoadingVisibilityEmitted);

    on<MessageEmitted>(_onMessageEmitted);
  }

  FutureOr<void> _onExceptionEmitted(
    ExceptionEmitted event,
    Emitter<CommonState> emit,
  ) {
    emit(state.copyWith(
      isLoading: false,
      appExceptionWrapper: event.appExceptionWrapper,
      message: null,
      boldMessage: null,
      undoTitle: null,
      undoCallback: null,
    ));
  }

  FutureOr _onLoadingVisibilityEmitted(
    LoadingVisibilityEmitted event,
    Emitter<CommonState> emit,
  ) {
    emit(state.copyWith(
      isLoading: event.isLoading,
      appExceptionWrapper: null,
      message: null,
      boldMessage: null,
      undoTitle: null,
      undoCallback: null,
    ));
  }

  FutureOr _onMessageEmitted(
    MessageEmitted event,
    Emitter<CommonState> emit,
  ) {
    emit(state.copyWith(
      isLoading: false,
      appExceptionWrapper: null,
      message: event.message,
      message2: event.message2,
      boldMessage: event.boldMessage,
      undoTitle: event.undoTitle,
      undoCallback: event.undoCallback,
      snackbarType: event.snackbarType,
    ));
  }
}
