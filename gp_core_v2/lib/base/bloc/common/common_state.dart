/*
 * Created Date: 5/12/2023 14:48:53
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 29th December 2023 15:37:09
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core/utils/popup.dart';

import '../../exception/base/app_exception_wrapper.dart';
import '../base_state.dart';

part 'common_state.freezed.dart';

@Freezed(
  copyWith: true,
  equal: true,
  fromJson: false,
  toJson: false,
  toStringOverride: true,
)
abstract class CommonState extends CoreV2BaseState with _$CommonState {
  const CommonState._();

  const factory CommonState({
    AppExceptionWrapper? appExceptionWrapper,
    @Default(false) bool isLoading,
    String? boldMessage,
    String? message,
    String? message2,
    String? undoTitle,
    SnackbarType? snackbarType,
    Function()? undoCallback,
  }) = _CommonState;
}
