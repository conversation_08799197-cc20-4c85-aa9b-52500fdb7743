/*
 * Created Date: 4/11/2023 09:34:0
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 29th December 2023 15:36:56
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2023 GAPO
 */

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_core_v2/base/utils/extension/num_utils.dart';
import 'package:talker/talker.dart';

import '../configs/app/app.configs.dart';
import '../exception/exception.dart';
import '../utils/mixin/log_mixin.dart';
import 'bloc.dart';

abstract class CoreV2BaseBloc<Event extends CoreV2BaseEvent,
        State extends CoreV2BaseState> extends BaseBlocDelegate<Event, State>
    with EventTransformerMixin<Event, State> {
  CoreV2BaseBloc(super.initialState);
}

abstract class BaseBlocDelegate<Event extends CoreV2BaseEvent,
    State extends CoreV2BaseState> extends Bloc<Event, State> with LogMixin {
  /// GetIt.I here should be `late final`
  BaseBlocDelegate(super.initialState);

  /// for common action like show/hide loading, handle error....
  /// no need to inject `commonBloc` to constructor for unit test
  late final CommonBloc commonBloc =
      GetIt.I<CommonBloc>(instanceName: 'kCommonBloc');

  @override
  void onEvent(Event event) {
    if (GetIt.I.isRegistered<AppUseCaseManagement>()) {
      if (GetIt.I
          .get<AppUseCaseManagement>()
          .blocEventContainsUsecase(event: event)) {
        logD("Bloc ignored onEvent: ${event.runtimeType}");
        return;
      }
    }

    super.onEvent(event);
  }

  @override
  void add(Event event) {
    if (GetIt.I.isRegistered<AppUseCaseManagement>()) {
      // just add and handle normal events
      if (GetIt.I
          .get<AppUseCaseManagement>()
          .blocEventContainsUsecase(event: event)) {
        logD("Bloc ignored: ${event.runtimeType}");
        if (GetIt.I.isRegistered<Talker>()) {
          GetIt.I
              .get<Talker>()
              .warning("Bloc ignore add: ${event.runtimeType}");
        }
        return;
      }
    }

    super.add(event);
  }

  @override
  void on<E extends Event>(
    EventHandler<E, State> handler, {
    EventTransformer<E>? transformer,
  }) {
    if (GetIt.I.isRegistered<AppUseCaseManagement>()) {
      // just add and handle normal events
      if (GetIt.I.get<AppUseCaseManagement>().blocEventContainsUsecase<E>()) {
        logD("Bloc ignore on: $E");

        if (GetIt.I.isRegistered<Talker>()) {
          GetIt.I.get<Talker>().warning("Bloc ignore on: $E");
        }
        return;
      }
    }

    super.on<E>(handler, transformer: transformer);
  }

  @override
  void onError(Object error, StackTrace stackTrace) {
    super.onError(error, stackTrace);

    if (error is GPAppExceptionV2) {
      _addException(AppExceptionWrapper(appException: error));
    } else {
      _addException(AppExceptionWrapper(
        appException: AppUncaughtException(error),
      ));
    }

    GPCoreTracker().appendError(
      'Flutter.core.CoreV2BaseBloc.onError',
      data: {'error': error, 'stacktrace': stackTrace},
    );

    GPCoreTracker().sendLog(
      message: 'Flutter.core.CoreV2BaseBloc.onError',
    );
  }

  void showLoading() {
    commonBloc.add(const LoadingVisibilityEmitted(isLoading: true));
  }

  void hideLoading() {
    commonBloc.add(const LoadingVisibilityEmitted(isLoading: false));
  }
}

extension HandleErrorMixin on BaseBlocDelegate {
  Future<void> defaultDoOnError(Object error, StackTrace stackTrace) async {
    logE('runCatching defaultDoOnError: $error, stackTrace: $stackTrace');
  }

  Future<void> runCatching({
    required Future<void> Function() action,
    Future<void> Function()? doOnStarted,
    Future<void> Function()? doOnSuccessOrError,
    Future<void> Function(Object, StackTrace)? doOnError,
    Future<void> Function()? doOnCompleted,
    Future<void> Function()? doOnRetry,
    bool handleLoading = true,
    bool handleError = true,
    bool handleRetry = false,
    bool Function(GPAppExceptionV2)? forceHandleError,
    String? overrideErrorMessage,
    int? maxRetries,
  }) async {
    assert(maxRetries == null || maxRetries > 0, 'maxRetries must be positive');
    Completer? recursion;
    try {
      await doOnStarted?.call();
      if (handleLoading) {
        showLoading();
      }

      await action.call();

      if (handleLoading) {
        hideLoading();
      }
      await doOnSuccessOrError?.call();
    } catch (e, s) {
      if (handleLoading) {
        hideLoading();
      }
      await doOnSuccessOrError?.call();
      await (doOnError ?? defaultDoOnError).call(e, s);

      if (e is GPAppExceptionV2) {
        if (handleError || (forceHandleError?.call(e) == true)) {
          logD('handleError with maxRetries = $maxRetries');
          logE('handleError, stackTrace: $s');
          // recursive with maxRetries += 1
          await _addException(AppExceptionWrapper(
            appException: e,
            doOnRetry: doOnRetry ??
                (handleRetry && maxRetries != 1
                    ? () async {
                        recursion = Completer();
                        await runCatching(
                          action: action,
                          doOnCompleted: doOnCompleted,
                          doOnStarted: doOnStarted,
                          doOnSuccessOrError: doOnSuccessOrError,
                          doOnError: doOnError,
                          doOnRetry: doOnRetry,
                          forceHandleError: forceHandleError,
                          handleError: handleError,
                          handleLoading: handleLoading,
                          handleRetry: handleRetry,
                          overrideErrorMessage: overrideErrorMessage,
                          maxRetries: maxRetries?.minus(1),
                        );
                        recursion?.complete();
                      }
                    : null),
            exceptionCompleter: Completer<void>(),
            overrideMessage: overrideErrorMessage,
          ));
        }
      }
    } finally {
      await recursion?.future;
      await doOnCompleted?.call();
    }

    return recursion?.future;
  }

  Future<void> _addException(AppExceptionWrapper appExceptionWrapper) async {
    commonBloc.add(ExceptionEmitted(
      appExceptionWrapper: appExceptionWrapper,
    ));

    return appExceptionWrapper.exceptionCompleter?.future;
  }
}
