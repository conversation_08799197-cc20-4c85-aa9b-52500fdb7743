import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/widgets/base_list_widget.dart';

abstract class BaseListViewListenerV2<T> {
  Future onLoadData(
    BuildContext context, {
    dynamic inputData,
    bool isRefreshData = false,
  });

  Widget buildItem(
      ListViewMode mode, BuildContext context, T entity, int index);

  /// skeleton loading
  Widget? buildFirstPageProgressIndicator(
      ListViewMode mode, BuildContext context);

  /// loadmore indicator
  Widget buildNewPageProgressIndicator(ListViewMode mode, BuildContext context);

  Widget buildDivider(ListViewMode mode, BuildContext context, int index);

  /// emptyview
  Widget? buildNoItemsFoundIndicator(ListViewMode mode, BuildContext context);

  /// errorview
  Widget? buildErrorView(ListViewMode mode, BuildContext context);

  Future? handleError(ListViewMode mode, Object? error, StackTrace stackTrace,
      bool isInitialLoad);

  bool get canLoadMore;

  bool get enablePullDown;

  bool get enablePullUp;
}

mixin BaseListViewMixin<T> implements BaseListViewListenerV2<T> {
  @override
  Widget buildDivider(ListViewMode mode, BuildContext context, int index) {
    return const Divider();
  }

  @override
  Widget buildNewPageProgressIndicator(
      ListViewMode mode, BuildContext context) {
    return Platform.isAndroid
        ? SizedBox(
            height: 50,
            child: Center(
              child: CircularProgressIndicator(color: GPColor.workPrimary),
            ),
          )
        : const CupertinoActivityIndicator();
  }

  @override
  Widget? buildErrorView(ListViewMode mode, BuildContext context) {
    return const SizedBox();
  }

  @override
  Future? handleError(ListViewMode mode, Object? error, StackTrace stackTrace,
      bool isInitialLoad) {
    // do nothing here, override if needed.
    return Future.value(null);
  }

  @override
  bool get enablePullDown => true;

  @override
  bool get enablePullUp => true;
}
