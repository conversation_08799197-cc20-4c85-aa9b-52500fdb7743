// ignore_for_file: library_private_types_in_public_api

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_list_view/flutter_list_view.dart';
import 'package:gp_core/core.dart';

import '../constants/client/duration.constants.dart';
import 'base_list_view_listener.dart';

enum BaseListViewState {
  idle,
  loading,
  loadDataSuccess,
  loadDataFailed,
  loadDataIsEmpty,
}

enum ListViewMode {
  normal,
  select,
}

final class BaseListWidgetControllerV2<T> {
  BaseListWidgetControllerV2({
    this.listener,
    this.inputData,
  });

  BaseListViewListenerV2<T>? listener;

  ListViewMode listViewMode = ListViewMode.normal;

  final dynamic inputData;

  final FlutterListViewController listViewController =
      FlutterListViewController();

  final ValueNotifier<_BasePageV2<T>> _currentState =
      ValueNotifier(_BasePageV2.initial());

  List<T> _data = [];
  Object? _error = '';

  _HighlightItem? highlightAddedItems;
  Map<int, _HighlightItem> highlightItemsByPosition = {};

  void setOnBaseListViewListenerV2(BaseListViewListenerV2<T> listener) {
    this.listener = listener;
  }

  set loading(bool isLoading) {
    if (isLoading) {
      // Chỉ set loading nếu thực sự chưa có data
      if (!hasData) {
        _currentState.value = _BasePageV2(
          state: BaseListViewState.loading,
          data: data,
        );
      }
    } else {
      _updateState();
    }
  }

  void appendData(
    List<T> addedData, {
    bool needHighlightAddedItems = false,
    bool reverse = false,
  }) {
    if (reverse) {
      final newData = this._data.toList();
      this._data = [...addedData, ...newData];
    } else {
      this._data.addAll(addedData);
    }

    if (needHighlightAddedItems) {
      _highlightItems(addedData, reverse: reverse);
    }

    _updateState();
  }

  void appendDataByPosition(
    Map<int, T> dataByPosition, {
    bool needHighlightAddedItems = false,
  }) {
    dataByPosition.forEach((key, value) {
      if (key != -1) {
        this._data.insert(key, value);
      }
    });

    if (needHighlightAddedItems) {
      _highlightItemsByPosition(dataByPosition);
    }

    _updateState();
  }

  void highlight(int start, int end) {
    highlightAddedItems = _HighlightItem(
        startIndex: start,
        endIndex: end,
        highlightDuration: DurationConstants.kHighlightDuration);

    _currentState.value = _BasePageV2(
      state: BaseListViewState.loadDataSuccess,
      data: data,
    );

    Future.delayed(highlightAddedItems!.highlightDuration).whenComplete(() {
      highlightAddedItems = null;

      _currentState.value = _BasePageV2(
        state: BaseListViewState.loadDataSuccess,
        data: data,
      );

      _updateState();
    });
  }

  void dispose() {
    listViewController.dispose();
    _currentState.dispose();
  }

  Future _highlightItems(List<T> highlightData, {bool reverse = false}) async {
    if (reverse) {
      highlightAddedItems = _HighlightItem(
          startIndex: 0,
          endIndex: highlightData.length - 1,
          highlightDuration: DurationConstants.kHighlightDuration);
    } else {
      highlightAddedItems = _HighlightItem(
          startIndex: this._data.length - 1,
          endIndex: this._data.length + highlightData.length - 1,
          highlightDuration: DurationConstants.kHighlightDuration);
    }

    _setIdleState();

    Future.delayed(highlightAddedItems!.highlightDuration).whenComplete(() {
      highlightAddedItems = null;

      // quick trick
      _setIdleState();

      _updateState();
    });
  }

  Future _highlightItemsByPosition(Map<int, T> highlightData) async {
    highlightData.forEach((key, value) {
      highlightItemsByPosition[key] = _HighlightItem(
          startIndex: key,
          endIndex: key,
          highlightDuration: DurationConstants.kHighlightDuration);
    });

    _setIdleState();

    Future.delayed(highlightItemsByPosition.values.first.highlightDuration)
        .whenComplete(() {
      highlightItemsByPosition.clear();

      // quick trick
      _setIdleState();

      _updateState();
    });
  }

  set data(List<T> data) {
    this._error = null;
    this._data = data.toList();
    _updateState();
  }

  set error(Object? error) {
    this._error = error;
    _updateState();
  }

  /// quick trick
  void _setIdleState() {
    // TODO(toannm): update item state instead of listView's state
    _currentState.value = _BasePageV2(
      state: BaseListViewState.idle,
      data: data,
    );
  }

  void _updateState() {
    BaseListViewState newState;
    if (hasError) {
      newState = BaseListViewState.loadDataFailed;
    } else if (hasData) {
      newState = BaseListViewState.loadDataSuccess;
    } else {
      newState = BaseListViewState.loadDataIsEmpty;
    }
    // Luôn tạo instance mới để ValueNotifier thông báo cho listener
    _currentState.value = _BasePageV2(
      state: newState,
      data: data,
    );
  }

  void notifyDataSetChanged() {
    _setIdleState();

    _updateState();
  }

  ValueNotifier<_BasePageV2<T>> get state => _currentState;

  List<T> get data => _data;

  bool get hasError {
    return _error != null;
  }

  bool get hasData {
    return _data.isNotEmpty;
  }
}

final class _BasePageV2<T> {
  _BasePageV2({
    required this.state,
    required this.data,
  });

  factory _BasePageV2.initial() {
    return _BasePageV2(data: [], state: BaseListViewState.loading);
  }

  BaseListViewState state;

  List<T> data;
}

/// Support class to build default item for [BaseListWidgetV2]
///
/// Make default item builder can be visible outside library
class BaseListViewDefaultItemBuilder {
  static Widget? build<T>(BuildContext context, ListViewMode mode,
      BaseListWidgetControllerV2<T> controller, int index) {
    final item = controller.listener?.buildItem(
      mode,
      context,
      controller.data[index],
      index,
    );

    if (controller.highlightAddedItems != null) {
      final highlightAddedItems = controller.highlightAddedItems!;

      if (index >= highlightAddedItems.startIndex &&
          index <= highlightAddedItems.endIndex) {
        return Container(color: GPColor.greenLight, child: item);
      }

      return item;
    }

    // highlight by position
    if (controller.highlightItemsByPosition.isNotEmpty) {
      final highlightAddedItems = controller.highlightItemsByPosition[index];

      if (highlightAddedItems != null) {
        return Container(color: GPColor.greenLight, child: item);
      }

      return item;
    }

    return item;
  }
}

class BaseListWidgetV2<T> extends StatefulWidget {
  const BaseListWidgetV2({
    required this.controller,
    this.customBuilder,
    this.shrinkWrap = false,
    this.dissmissKeyboardOnScroll = true,
    super.key,
  });

  final bool shrinkWrap;
  final bool dissmissKeyboardOnScroll;

  /// Sync [controller] and [shrinkWrap] from inside to build
  /// a custom list
  final Widget? Function(
      bool shrinkWrap, BaseListWidgetControllerV2<T> controller)? customBuilder;

  final BaseListWidgetControllerV2<T> controller;

  @override
  State<BaseListWidgetV2<T>> createState() => _BaseListWidgetV2State<T>();
}

class _BaseListWidgetV2State<T> extends State<BaseListWidgetV2<T>> {
  final RefreshController _refreshController = RefreshController();

  @override
  void dispose() {
    super.dispose();

    _refreshController.dispose();
  }

  @override
  void didUpdateWidget(covariant BaseListWidgetV2<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (!const ListEquality()
        .equals(oldWidget.controller.data, widget.controller.data)) {
      widget.controller.data.clear();
      widget.controller.appendData(oldWidget.controller.data);
    }
  }

  void _refreshCompleted() {
    _refreshController.loadComplete();
    _refreshController.refreshCompleted();
    widget.controller.loading = false;
  }

  @override
  Widget build(BuildContext context) {
    final controller = widget.controller;
    final listener = widget.controller.listener;
    final mode = widget.controller.listViewMode;
    return ListenableBuilder(
      listenable: controller.state,
      builder: (context, child) {
        return SmartRefresher(
          controller: _refreshController,
          onRefresh: () async {
            controller.loading = true;

            return listener
                ?.onLoadData(
              context,
              inputData: widget.controller.inputData,
              isRefreshData: true,
            )
                .then((value) {
              if (value is List<T>) {
                controller.data = value;
              }
            }).onError((error, stackTrace) {
              controller.error = error;

              listener.handleError.call(mode, error, stackTrace, true);
            }).whenComplete(() {
              _refreshCompleted();
            });
          },
          onLoading: () async {
            if (listener?.canLoadMore == false) {
              _refreshController.loadNoData();
              return;
            }

            return listener
                ?.onLoadData(
              context,
              inputData: widget.controller.inputData,
              isRefreshData: false,
            )
                .onError((error, stackTrace) {
              listener.handleError.call(mode, error, stackTrace, false);
            }).whenComplete(() {
              _refreshCompleted();
            });
          },
          enablePullDown: listener?.enablePullDown ?? false,
          enablePullUp: listener?.enablePullUp ?? false,
          header: const GPPullToRefreshHeader(),
          footer: const GPPullToRefreshFooter(),
          child: _buildChild(),
        );

        // return BaseSmartRefresher(
        //   onReload: () async {
        //     controller.loading = true;

        //     return listener.onLoadData(isRefreshData: true).then((value) {
        //       if (value is List) {
        //         controller.data = value;
        //       }
        //     }).onError((error, stackTrace) {
        //       controller.error = error;

        //       listener.handleError.call(error, stackTrace, true);
        //     }).whenComplete(() {
        //       controller.loading = false;
        //     });
        //   },
        //   onLoadMore: () {
        //     return listener
        //         .onLoadData(isRefreshData: false)
        //         .onError((error, stackTrace) {
        //       listener.handleError.call(error, stackTrace, false);
        //     }).whenComplete(() {
        //       controller.loading = false;
        //     });
        //   },
        //   isFinish: true,
        //   child: _buildChild(),
        // );
      },
    );
  }

  Widget _buildChild() {
    final mode = widget.controller.listViewMode;
    switch (widget.controller.state.value.state) {
      case BaseListViewState.loading:
        return widget.controller.listener
                ?.buildFirstPageProgressIndicator(mode, context) ??
            const Center(child: CircularProgressIndicator());
      case BaseListViewState.loadDataSuccess:
        final data = widget.controller.data;

        return widget.customBuilder
                ?.call(widget.shrinkWrap, widget.controller) ??
            FlutterListView(
              keyboardDismissBehavior: widget.dissmissKeyboardOnScroll
                  ? ScrollViewKeyboardDismissBehavior.onDrag
                  : ScrollViewKeyboardDismissBehavior.manual,
              shrinkWrap: widget.shrinkWrap,
              delegate: FlutterListViewDelegate(
                (context, index) {
                  return BaseListViewDefaultItemBuilder.build(
                      context, mode, widget.controller, index);
                },
                childCount: data.length,
                keepPosition: true,
              ),
              controller: widget.controller.listViewController,
            );

      case BaseListViewState.loadDataFailed:
        return widget.controller.listener?.buildErrorView(mode, context) ??
            ErrorView(LocaleKeys.error_nodata.tr);
      case BaseListViewState.loadDataIsEmpty:
        return widget.controller.listener
                ?.buildNoItemsFoundIndicator(mode, context) ??
            const SizedBox();
      default:
        return const SizedBox();
    }
  }
}

/// highlight item by index
class _HighlightItem {
  _HighlightItem({
    required this.startIndex,
    required this.endIndex,
    required this.highlightDuration,
  });

  final int startIndex;
  final int endIndex;

  final Duration highlightDuration;
}
