/*
 * Created Date: 2/01/2024 15:12:9
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:58:55
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer' as dev;

import '../../configs/log/log.configs.dart';

mixin LogMixin on Object {
  void _log(
    String message, {
    DateTime? time,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (!LogConfig.kEnableDebugLog) return;

    dev.log(
      message,
      name: runtimeType.toString(),
      time: time ?? DateTime.now(),
      level: 1,
      error: error,
      stackTrace: stackTrace,
    );
  }

  void logD(
    String message, {
    DateTime? time,
  }) {
    _log('🟢 $message', time: time);
  }

  void logE(
    String message, {
    DateTime? time,
  }) {
    _log('🔴 $message', time: time);
  }
}
