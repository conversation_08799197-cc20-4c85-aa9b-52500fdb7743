/*
 * Created Date: 2/01/2024 17:36:17
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:49:10
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart' hide RefreshIndicator;

import '../../../bloc/common/common.dart';
import '../../../exception/handler/exception_handler.dart';

extension BlocListenerExt on Widget {
  Widget withBlocCommonListener({
    Key? key,
    void Function(BuildContext, CommonState)? listener,
    bool Function(CommonState, CommonState)? listenWhen,
  }) {
    return BlocListener<CommonBloc, CommonState>(
      key: key,
      bloc: GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
      listenWhen: listenWhen ?? _kD<PERSON><PERSON>,
      listener: listener ?? _kDefaultCommonBlocListener,
      child: this,
    );
  }

  bool _kDefaultListenWhen(CommonState previous, CommonState current) {
    return previous.appExceptionWrapper != current.appExceptionWrapper &&
        current.appExceptionWrapper != null;
  }

  void _kDefaultCommonBlocListener(BuildContext context, CommonState state) {
    if (state.appExceptionWrapper == null) {
      // TODO(ToanNM): add sentry here
      return;
    }

    GetIt.I<ExceptionHandler>(instanceName: 'kExceptionHandler')
        .handleException(state.appExceptionWrapper!)
        .then(
      (value) {
        state.appExceptionWrapper!.exceptionCompleter?.complete();
      },
    );
  }
}

extension BlocBuilderExt on Widget {
  Widget withBlocCommonBuilder({
    Key? key,
    Widget Function(BuildContext, CommonState)? builder,
    bool Function(CommonState, CommonState)? buildWhen,
    Widget? overrideLoadingView,
    Widget Function(String error)? overrideErrorView,
    String? overrideErrorString,
    Function()? onRetry,
  }) {
    return BlocBuilder<CommonBloc, CommonState>(
      key: key,
      bloc: GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
      buildWhen: buildWhen ?? _kDefaultBuildWhen,
      builder: builder ??
          (_, state) => _KDefaultCommonBlocBuilder(
                state,
                this,
                overrideLoadingView: overrideLoadingView,
                overrideErrorView: overrideErrorView,
                overrideErrorString: overrideErrorString,
                onRetry: onRetry,
              ),
    );
  }

  bool _kDefaultBuildWhen(CommonState previous, CommonState current) {
    final hasErrorChange =
        previous.appExceptionWrapper != current.appExceptionWrapper &&
            current.appExceptionWrapper != null;

    final hasLoadingChange = previous.isLoading != current.isLoading;

    return hasErrorChange || hasLoadingChange;
  }
}

extension BlocConsumerExt on Widget {
  Widget withBlocCommonConsumer({
    Key? key,
    Widget Function(BuildContext, CommonState)? builder,
    bool Function(CommonState, CommonState)? buildWhen,
    Widget? overrideLoadingView,
    String? overrideErrorString,
  }) {
    return BlocConsumer<CommonBloc, CommonState>(
      key: key,
      bloc: GetIt.I<CommonBloc>(instanceName: 'kCommonBloc'),
      buildWhen: buildWhen ?? _kDefaultBuildWhen,
      builder: builder ??
          (_, state) => _KDefaultCommonBlocBuilder.noError(
                state,
                this,
                overrideLoadingView: overrideLoadingView,
              ),
      listener: (BuildContext context, CommonState state) {
        final hasError = state.appExceptionWrapper != null;
        final hasMessage = state.message?.isNotEmpty == true;

        if (hasError || hasMessage) {
          final kExceptionHandler =
              GetIt.I<ExceptionHandler>(instanceName: 'kExceptionHandler');
          String error = state.message ??
              overrideErrorString ??
              kExceptionHandler.exceptionMessageMapper
                  .map(state.appExceptionWrapper!.appException);

          final defaultSnackBarType =
              hasError ? SnackbarType.error : SnackbarType.normal;
          final snackbarType = state.snackbarType ?? defaultSnackBarType;

          Popup.instance.showSnackBar(
            message: error,
            message2: state.message2,
            undoTitle: state.undoTitle,
            messageBold1: state.boldMessage ?? '',
            undoCallback: state.undoCallback,
            type: snackbarType,
          );
        }
      },
      // listenWhen: _kDefaultListenWhen,
    );
  }

  bool _kDefaultBuildWhen(CommonState previous, CommonState current) {
    final hasErrorChange =
        previous.appExceptionWrapper != current.appExceptionWrapper &&
            current.appExceptionWrapper != null;

    return hasErrorChange;
  }

  // bool _kDefaultListenWhen(CommonState previous, CommonState current) {
  //   return previous.isLoading != current.isLoading;
  // }
}

class _KDefaultCommonBlocBuilder extends StatelessWidget {
  const _KDefaultCommonBlocBuilder(
    this.state,
    this.child, {
    this.overrideLoadingView,
    this.overrideErrorView,
    this.overrideErrorString,
    this.onRetry,
  }) : needToHandleError = true;

  const _KDefaultCommonBlocBuilder.noError(
    this.state,
    this.child, {
    this.overrideLoadingView,
  })  : needToHandleError = false,
        overrideErrorString = null,
        onRetry = null,
        overrideErrorView = null;

  final bool needToHandleError;

  final CommonState state;
  final Widget child;
  final Widget? overrideLoadingView;

  final Widget Function(String error)? overrideErrorView;
  final String? overrideErrorString;
  final Function()? onRetry;

  @override
  Widget build(BuildContext context) {
    final isLoading = state.isLoading;
    final hasError = state.appExceptionWrapper != null;

    // loading
    if (isLoading) {
      return overrideLoadingView ?? const IndicatorLoadingView();
    }

    // error view
    if (needToHandleError) {
      if (hasError) {
        final kExceptionHandler =
            GetIt.I<ExceptionHandler>(instanceName: 'kExceptionHandler');
        String error = overrideErrorString ??
            kExceptionHandler.exceptionMessageMapper
                .map(state.appExceptionWrapper!.appException);

        if (error.isEmpty) {
          error = LocaleKeys.error_default_msg.tr;
        }

        return RefreshIndicator(
          onRefresh: () async {
            onRetry?.call();
          },
          child: ListView(
            children: [
              overrideErrorView?.call(error) ??
                  Padding(
                    padding: const EdgeInsets.only(top: 150),
                    child: EmptyView(emptyText: error),
                  ),
            ],
          ),
        );
      }
    }

    // normal view
    return child;
  }
}
