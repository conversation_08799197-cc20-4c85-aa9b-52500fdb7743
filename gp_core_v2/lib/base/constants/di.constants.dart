/*
 * Created Date: 5/12/2023 11:05:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Sunday, 24th March 2024 22:47:21
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:injectable/injectable.dart';

const _saasDevelop = 'SaasDevelop';
const _saasQa = 'SaasQa';
const _saasUat = 'SaasUat';
const _saasProd = 'SaasProd';

const _onPremiseDevelop = 'OnPremiseDevelop';
const _onPremiseQa = 'OnPremiseQa';
const _onPremiseUat = 'OnPremiseUat';
const _onPremiseProd = 'OnPremiseProd';

// ---------- Env ---------- \\
const kFlavorSaasDevelopment = Environment(_saasDevelop);
const kFlavorSaasQA = Environment(_saasQa);
const kFlavorSaasUAT = Environment(_saasUat);
const kFlavorSaasProduction = Environment(_saasProd);

const kFlavorOnPremiseDevelopment = Environment(_onPremiseDevelop);
const kFlavorOnPremiseQA = Environment(_onPremiseQa);
const kFlavorOnPremiseUAT = Environment(_onPremiseUat);
const kFlavorOnPremiseProduction = Environment(_onPremiseProd);

// ---------- List env ---------- \\
const List<String> kFlavorSaasDevs = [_saasDevelop, _saasQa];
const List<String> kFlavorSaasProds = [_saasUat, _saasProd];

const List<String> kFlavorOnPremiseDevs = [_onPremiseDevelop, _onPremiseQa];
const List<String> kFlavorOnPremiseProd = [_onPremiseUat, _onPremiseProd];

const List<String> kFlavorDevs = [...kFlavorSaasDevs, ...kFlavorOnPremiseDevs];
const List<String> kFlavorProds = [
  ...kFlavorSaasProds,
  ...kFlavorOnPremiseProd
];

const List<String> kFlavorAlls = [...kFlavorDevs, ...kFlavorProds];

sealed class DiConstants {
  /*
    - low order registry first
    - default order:
      - unOrdered(0) -> Data -> Domain
        - Data: dio -> interceptor -> service -> repoImpl
        - Domain: mapper -> usecase
  */

  // data
  static const kDataDioOrder = 1;
  static const kDataInterceptorOrder = 2;
  static const kDataServiceOrder = 25;
  static const kDataRepositoryOrder = 30;

  static const kBlocOrder = 50;

  // domain
  static const kDomainMapperOrder = 70;
  static const kDomainHandlerOrder = 71;
  static const kDomainUseCaseOrder = 90;
  static const kDomainPresentationOrder = 93;
  static const kLastestOrder = 99;
}

enum AppNavigator {
  flutter('flutter'),
  goRouter('goRouter'),
  getx('getx'),
  ;

  const AppNavigator(this.value);

  final String value;
}
