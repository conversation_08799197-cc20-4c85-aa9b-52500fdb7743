/*
 * Created Date: 5/01/2024 11:21:21
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 24th January 2024 08:49:55
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

sealed class DurationConstants {
  const DurationConstants._();

  static const kDefaultEventTransfomDuration = Duration(milliseconds: 500);

  static const kDefaultSnackBarDuration = Duration(seconds: 3);
  static const kDefaultErrorVisibleDuration = Duration(seconds: 3);

  static const kDebounceSearchShortDuration = Duration(milliseconds: 300);
  static const kDebounceSearchMediumDuration = Duration(milliseconds: 500);
  static const kDebounceSearchLongDuration = Duration(milliseconds: 600);
  
  static const kHighlightShortDuration = Duration(milliseconds: 1500);
  static const kHighlightDuration = Duration(milliseconds: 3000);
  static const kHighlightLongDuration = Duration(milliseconds: 4000);
}
