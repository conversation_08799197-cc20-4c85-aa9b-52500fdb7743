/*
 * Created Date: 5/12/2023 11:04:39
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 25th January 2024 11:27:34
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

sealed class ServerTimeoutConstants {
  static const kConnectTimeout = Duration(seconds: 10);
  static const kReceiveTimeout = Duration(seconds: 30);
  static const kSendTimeout = Duration(seconds: 30);

  // refreshToken
  static const kRefreshTokenConnectTimeout = Duration(seconds: 10);
  static const kRefreshTokenReceiveTimeout = Duration(seconds: 10);
  static const kRefreshTokenSendTimeout = Duration(seconds: 10);

  // upload
  static const kUploadConnectTimeout = Duration(seconds: 10);
  static const kUploadReceiveTimeout = Duration(seconds: 86400);
  static const kUploadSendTimeout = Duration(seconds: 86400);
}

