/*
 * Created Date: 2/01/2024 15:22:22
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:48:54
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer' as dev;

import 'package:gp_core_v2/base/configs/log/log.configs.dart';
import 'package:rxdart/rxdart.dart';

extension StreamExt<T> on Stream<T> {
  Stream<T> log(
    String name, {
    bool logOnListen = false,
    bool logOnData = false,
    bool logOnError = false,
    bool logOnDone = false,
    bool logOnCancel = false,
  }) {
    return doOnListen(() {
      if (LogConfig.kLogOnStreamListen && logOnListen) {
        dev.log(
          '▶️ onSubscribed',
          time: DateTime.now(),
          name: name,
        );
      }
    }).doOnData((event) {
      if (LogConfig.kLogOnStreamData && logO<PERSON>) {
        dev.log(
          '🟢 onEvent: $event',
          time: DateTime.now(),
          name: name,
        );
      }
    }).doOnCancel(() {
      if (LogConfig.kLogOnStreamCancel && logOnCancel) {
        dev.log('🟡 onCanceled', time: DateTime.now(), name: name);
      }
    }).doOnError((e, _) {
      if (LogConfig.kLogOnStreamError && logOnError) {
        dev.log('🔴 onError $e', time: DateTime.now(), name: name);
      }
    }).doOnDone(() {
      if (LogConfig.kLogOnStreamDone && logOnDone) {
        dev.log('☑️️ onCompleted', time: DateTime.now(), name: name);
      }
    });
  }
}
