/*
 * Created Date: 4/01/2024 08:42:32
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 4th January 2024 09:15:24
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import '../base/app_exception.dart';

class AppUncaughtException extends GPAppExceptionV2 {
  const AppUncaughtException(this.rootError) : super(AppExceptionType.uncaught);

  final Object? rootError;

  @override
  String toString() {
    return 'rootError: ${rootError?.toString()}';
  }
}
