/*
 * Created Date: 2/01/2024 14:29:9
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:48:40
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/base/networking/base/response_error.dart';

import '../base/app_exception.dart';

class RemoteException extends GPAppExceptionV2 {
  const RemoteException({
    required this.kind,
    this.responseError,
    this.rootException,
  }) : super(AppExceptionType.remote);

  final RemoteExceptionKind kind;
  final ResponseError? responseError;
  final Object? rootException;

  int? get responseCode => responseError?.code;

  int? get httpStatusCode => responseError?.httpStatusCode;

  @override
  String toString() {
    return 'RemoteException: {kind: $kind, responseError: $responseError, rootException: $rootException}';
  }
}

/// define all remote exception kind for `gp_core_v2`
enum RemoteExceptionKind {
  noInternet,

  /// host not found, cannot connect to host, SocketException
  network,

  /// server has defined response
  serverDefined,

  /// server has not defined response
  serverUndefined,

  /// Caused by an incorrect certificate as configured by [ValidateCertificate]
  badCertificate,

  /// error occurs when passing JSON
  decodeError,

  refreshTokenFailed,
  timeout,
  cancellation,
  unknown,
}
