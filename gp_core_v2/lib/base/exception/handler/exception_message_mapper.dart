/*
 * Created Date: 2/01/2024 15:47:42
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:48:30
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/core.dart';
import 'package:injectable/injectable.dart';

import '../../constants/di.constants.dart';
import '../../utils/mixin/log_mixin.dart';
import '../exception.dart';

@Singleton(order: DiConstants.kDomainMapperOrder)
@Named('kExceptionMessageMapper')
final class ExceptionMessageMapper with LogMixin {
  const ExceptionMessageMapper();

  String map(GPAppExceptionV2 appException) {
    logE('ExceptionMessageMapper for exception: $appException');
    return switch (appException.appExceptionType) {
      AppExceptionType.remote => switch (
            (appException as RemoteException).kind) {
          RemoteExceptionKind.badCertificate => 'badCertificate',
          RemoteExceptionKind.noInternet => 'noInternet',
          RemoteExceptionKind.network => 'network',
          RemoteExceptionKind.serverDefined => _serverException(appException),
          RemoteExceptionKind.serverUndefined =>
            LocaleKeys.error_mapper_server_undefined.tr,
          RemoteExceptionKind.timeout => LocaleKeys.error_mapper_timeout.tr,
          RemoteExceptionKind.cancellation =>
            LocaleKeys.error_mapper_cancellation.tr,
          RemoteExceptionKind.unknown => LocaleKeys.error_mapper_unknow.tr,
          RemoteExceptionKind.refreshTokenFailed =>
            LocaleKeys.error_mapper_server_defined_E401,
          RemoteExceptionKind.decodeError =>
            LocaleKeys.error_mapper_decode_error.tr,
        },
      AppExceptionType.parse => _parseException(appException as ParseException),
      AppExceptionType.validation =>
        _validationException(appException as ValidationException),
      AppExceptionType.remoteConfig => 'remoteConfig exception',
      AppExceptionType.uncaught => LocaleKeys.error_mapper_uncaught.tr,
    };
  }

  String _serverException(RemoteException appException) {
    final int? errorCode = appException.responseCode;

    if (errorCode == null) return "";

    if (errorCode == 504) {
      return LocaleKeys.error_mapper_server_defined_E504.tr;
    } else if (errorCode == 503) {
      return LocaleKeys.error_mapper_server_defined_E503.tr;
    } else if (errorCode == 502) {
      return LocaleKeys.error_mapper_server_defined_E502.tr;
    } else if (errorCode == 500) {
      return LocaleKeys.error_mapper_server_defined_E500.tr;
    } else if (errorCode == 404) {
      return LocaleKeys.error_mapper_server_defined_E404.tr;
    } else if (errorCode == 401) {
      return LocaleKeys.error_mapper_server_defined_E401.tr;
    } else if (errorCode == 400) {
      return LocaleKeys.error_mapper_server_defined_E400.tr;
    }

    return LocaleKeys.error_mapper_server_undefined.tr;
  }

  String _parseException(ParseException appException) {
    final ParseExceptionKind kind = appException.kind;

    switch (kind) {
      case ParseExceptionKind.invalidFormat:
        return LocaleKeys.error_mapper_parse_invalid_format.tr;
      case ParseExceptionKind.invalidSourceFormat:
        return LocaleKeys.error_mapper_parse_invalid_source_format.tr;

      case ParseExceptionKind.errorMapping:
        return LocaleKeys.error_mapper_parse_error_mapping.tr;
      // default:
      //   return LocaleKeys.error_mapper_uncaught.tr;
    }
  }

  String _validationException(ValidationException appException) {
    final ValidationExceptionKind kind = appException.kind;

    switch (kind) {
      case ValidationExceptionKind.empty:
        return LocaleKeys.error_mapper_validation_empty.tr;
      case ValidationExceptionKind.invalidData:
        return LocaleKeys.error_mapper_validation_invalid_data.tr;

      case ValidationExceptionKind.invalidDataType:
        return LocaleKeys.error_mapper_validation_invalid_data_type.tr;
      // default:
      //   return LocaleKeys.error_mapper_uncaught.tr;
    }
  }
}
