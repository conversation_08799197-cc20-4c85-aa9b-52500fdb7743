/*
 * Created Date: 2/01/2024 15:47:42
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:48:25
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/base/networking/base/response_error.dart';
import 'package:gp_core/core.dart';
import 'package:injectable/injectable.dart';

import '../../constants/di.constants.dart';
import '../exception.dart';

abstract class ExceptionHandler {
  const ExceptionHandler(this.exceptionMessageMapper);

  final ExceptionMessageMapper exceptionMessageMapper;

  String get defaultErrorMsg;

  Future<void> handleException(Object exception);
}

@Singleton(as: ExceptionHandler, order: DiConstants.kDomainHandlerOrder)
@Named('kExceptionHandler')
class ExceptionHandlerImpl extends ExceptionHandler {
  ExceptionHandlerImpl(
      @Named('kExceptionMessageMapper') super.exceptionMessageMapper);

  // final ExceptionHandlerListener listener;

  @override
  String get defaultErrorMsg => LocaleKeys.error_default_msg.tr;

  @override
  Future<void> handleException(
    Object exception,
  ) async {
    if (exception is AppException) {
      // handle appException v1
      _handleAppExceptionV1(exception);
    } else if (exception is AppExceptionWrapper) {
      // handle appException v2
      _handleGPAppExceptionV2(exception);
    } else if (exception is String) {
      _handleAppExceptionString(exception);
    } else {
      _showSnackBarError(defaultErrorMsg);
    }
  }

  Future<void> _handleAppExceptionV1(AppException exception) async {
    var message = defaultErrorMsg;

    if (exception.message == null) {
      message = defaultErrorMsg;
    } else {
      message = exception.toString();
    }

    if (!(await GPConnectionConcrete().isInternetConnected())) {
      return _showSnackBarError(LocaleKeys.error_wifi_connection.tr);
    }

    if (message.isEmpty) {
      return _showSnackBarError(message);
    }

    if (exception.response != null && exception.response?.popupType != null) {
      switch (exception.response?.popupType) {
        case ErrorPopupType.bottomSheetDialog:
          return _showAlert(
            exception.response?.titleMobile ??
                LocaleKeys.taskCreation_failure_max16AttachmentFilesTitle.tr,
            message,
          );
        case ErrorPopupType.snackBar:
        default:
          return Popup.instance.showSnackBar(
            message: message,
            type: SnackbarType.error,
          );
      }
    } else {
      return _showSnackBarError(message);
    }
  }

  Future<void> _handleGPAppExceptionV2(
      AppExceptionWrapper appExceptionWrapper) async {
    final message = appExceptionWrapper.overrideMessage ??
        exceptionMessageMapper.map(appExceptionWrapper.appException);

    switch (appExceptionWrapper.appException.appExceptionType) {
      case AppExceptionType.remote:
        final exception = appExceptionWrapper.appException as RemoteException;
        switch (exception.kind) {
          case RemoteExceptionKind.refreshTokenFailed:
            // TODO(ToanNM): no handle here now
            return;
          case RemoteExceptionKind.noInternet:
          case RemoteExceptionKind.timeout:
            // TODO(ToanNM): handle retry later
            return _showSnackBarError(LocaleKeys.error_wifi_connection.tr);
          default:
            return _showSnackBarError(message);
        }
      case AppExceptionType.parse:
        return _showSnackBarError('Parse data client error');
      case AppExceptionType.uncaught:
        return _showSnackBarError(defaultErrorMsg);
      case AppExceptionType.remoteConfig:
      case AppExceptionType.validation:
        // TODO(ToanNM): no handle here now
        return;
    }
  }

  Future<void> _handleAppExceptionString(String exception) async {
    var message = defaultErrorMsg;

    if (exception == "null") {
      message = defaultErrorMsg;
    }

    _showSnackBarError(message);

    return;
  }

  void _showSnackBarError(String message) {
    Popup.instance.showSnackBar(message: message, type: SnackbarType.error);
  }

  void _showAlert(String title, String message) {
    Popup.instance.showAlert(
      title: title,
      message: message,
    );
  }
}

/// inject to `ExceptionHandler` later
/// singleton for now
// abstract class ExceptionHandlerListener {
//   void onRefreshTokenFailed();
// }
