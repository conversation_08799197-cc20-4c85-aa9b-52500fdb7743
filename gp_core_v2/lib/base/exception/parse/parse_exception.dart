/*
 * Created Date: 4/01/2024 08:58:14
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 4th January 2024 09:15:15
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import '../base/app_exception.dart';

class ParseException extends GPAppExceptionV2 {
  const ParseException(this.kind, this.rootException)
      : super(AppExceptionType.parse);

  final ParseExceptionKind kind;
  final Object? rootException;

  @override
  String toString() {
    return 'ParseException: {kind: $kind, rootException: $rootException}';
  }
}

enum ParseExceptionKind {
  invalidFormat, // such as: type 'double' is not a subtype of type 'int?'
  invalidSourceFormat, // unused now
  errorMapping,
}
