/*
 * Created Date: 5/12/2023 14:47:48
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:59:41
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

abstract class GPAppExceptionV2 implements Exception {
  const GPAppExceptionV2(this.appExceptionType);

  final AppExceptionType appExceptionType;
}

enum AppExceptionType {
  /// from backend
  remote,

  /// parsing data error:
  /// such as:
  /// client: calendar.schedule.duration is type 'double' is not a subtype of type 'int?'
  parse,

  /// remoteConfig like firebase remoteConfig
  remoteConfig,

  /// other exception
  uncaught,

  /// validation data error:
  /// such as
  /// client: calendar.schedule.duration `is empty` in A case
  validation,
}
