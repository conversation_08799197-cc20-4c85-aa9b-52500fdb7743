/*
 * Created Date: 5/12/2023 14:47:48
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 17:59:44
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:gp_core_v2/base/exception/handler/exception_handler.dart';

import 'app_exception.dart';

final class AppExceptionWrapper {
  AppExceptionWrapper({
    required this.appException,
    this.exceptionCompleter,
    this.doOnRetry,
    this.overrideMessage,
  });

  final GPAppExceptionV2 appException;
  final Completer<void>? exceptionCompleter;
  final Future<void> Function()? doOnRetry;

  /// đề phòng tương lai có thể có case cần tới, đỡ phải sửa base.
  /// ưu tiên hiển thị `overrideMessage` khi có exception
  final String? overrideMessage;

  @override
  String toString() {
    return 'AppExceptionWrapper(appException: $appException, exceptionCompleter: $exceptionCompleter, doOnRetry: $doOnRetry, overrideMessage: $overrideMessage)';
  }
}

extension AppExceptionWrapperMapperExt on AppExceptionWrapper {
  String get errorMapped =>
      GetIt.I<ExceptionHandler>(instanceName: 'kExceptionHandler')
          .exceptionMessageMapper
          .map(appException);
}
