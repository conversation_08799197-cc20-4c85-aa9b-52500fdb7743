import '../base/app_exception.dart';

class ValidationException extends GPAppExceptionV2 {
  const ValidationException(this.kind) : super(AppExceptionType.validation);

  final ValidationExceptionKind kind;

  @override
  String toString() {
    return 'ValidationException: {kind: $kind}';
  }
}

enum ValidationExceptionKind {
  empty, // data is empty in A case
  invalidData, // data is invalid
  invalidDataType, // data is invalid type, such as: expected double, but pass int
}
