/*
 * Created Date: 2/01/2024 14:27:50
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 14:47:50
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:io';

import 'package:dio/dio.dart';
import 'package:gp_core/base/networking/base/response_error.dart';
import 'package:gp_core_v2/base/constants/di.constants.dart';
import 'package:gp_core_v2/base/exception/remote/remote_exception.dart';
import 'package:injectable/injectable.dart';

import 'exception_mapper.dart';

@Named('kDioExceptionMapper')
@Singleton(order: DiConstants.kDomainHandlerOrder)
class DioExceptionMapper extends ExceptionMapper<RemoteException> {
  DioExceptionMapper();

  @override
  RemoteException map(Object? exception) {
    if (exception is RemoteException) {
      return exception;
    }

    if (exception is DioException) {
      switch (exception.type) {
        case DioExceptionType.cancel:
          return const RemoteException(kind: RemoteExceptionKind.cancellation);
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.sendTimeout:
          return RemoteException(
            kind: RemoteExceptionKind.timeout,
            rootException: exception,
          );
        case DioExceptionType.badResponse:
          // defined error
          if (exception.response?.data != null) {
            final ResponseError? responseError;
            if (exception.response?.data is Map) {
              responseError = ResponseError.fromJson(exception.response?.data);
            } else {
              responseError =
                  ResponseError.fromDioResponse(exception.response!.data);
            }

            return RemoteException(
              kind: RemoteExceptionKind.serverDefined,
              responseError: responseError,
              rootException: exception,
            );
          }

          // un-defined error
          return RemoteException(
            kind: RemoteExceptionKind.serverUndefined,
            rootException: exception,
          );
        case DioExceptionType.badCertificate:
          return RemoteException(
            kind: RemoteExceptionKind.badCertificate,
            rootException: exception,
          );
        case DioExceptionType.connectionError:
          return RemoteException(
              kind: RemoteExceptionKind.network, rootException: exception);
        case DioExceptionType.unknown:
          if (exception.error is SocketException) {
            return RemoteException(
                kind: RemoteExceptionKind.network, rootException: exception);
          }

          if (exception.error is RemoteException) {
            return exception.error as RemoteException;
          }
      }
    }

    return RemoteException(
        kind: RemoteExceptionKind.unknown, rootException: exception);
  }
}
