/*
 * Created Date: 4/01/2024 11:40:42
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:23:23
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import '../../configs/log/log.configs.dart';
import '../model/base_input.dart';
import '../model/base_output.dart';
import '../model/base_use_case.dart';

abstract class GPBaseFutureUseCase<Input extends GPBaseInput,
    Output extends GPBaseOutput> extends BaseUseCase<Input, Future<Output>> {
  const GPBaseFutureUseCase();

  Future<Output> execute(Input input) async {
    try {
      if (LogConfig.kEnableLogUseCaseInput) {
        logD('FutureUseCase Input: $input');
      }

      final output = await buildUseCase(input);
      if (LogConfig.kEnableLogUseCaseOutput) {
        logD('FutureUseCase Output: $output');
      }

      return output;
    } catch (e, s) {
      if (LogConfig.kEnableLogUseCaseError) {
        logE('FutureUseCase Error: $e, \n$s');
      }

      throw mapException(e);
    }
  }
}
