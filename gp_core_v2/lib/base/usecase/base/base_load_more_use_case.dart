/*
 * Created Date: 4/01/2024 11:48:40
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:23:27
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/base/models/list_api_response.dart';
import 'package:gp_core/base/networking/base/app_exception.dart';

import '../../configs/log/log.configs.dart';
import '../../constants/client/paging.constants.dart';
import '../../exception/base/app_exception.dart';
import '../../exception/uncaught/app_uncaught_exception.dart';
import '../model/base_input.dart';
import '../model/base_use_case.dart';
import '../model/load_more_output.dart';

abstract class GPBaseLoadMoreUseCase<Input extends GPBaseInput, Output>
    extends BaseUseCase<Input, Future<ListAPIResponse<Output>>> {
  GPBaseLoadMoreUseCase({
    this.initPage = PagingConstants.kInitialPage,
    this.initOffset = 0,
  })  : _output = LoadMoreOutput<Output>(
            data: <Output>[], page: initPage, offset: initOffset),
        _oldOutput = LoadMoreOutput<Output>(
            data: <Output>[], page: initPage, offset: initOffset);

  final int initPage;
  final int initOffset;

  LoadMoreOutput<Output> _output;
  LoadMoreOutput<Output> _oldOutput;

  int get page => _output.page;
  int get offset => _output.offset;

  Future<LoadMoreOutput<Output>> execute(
      Input input, bool isInitialLoad) async {
    try {
      if (isInitialLoad) {
        _output = LoadMoreOutput<Output>(
            data: <Output>[], page: initPage, offset: initOffset);
      }
      if (LogConfig.kEnableLogUseCaseInput) {
        logD(
          'LoadMoreUseCase Input: $input, page: $page, offset: $offset',
        );
      }
      final pagedList = await buildUseCase(input);

      final data = pagedList.data ?? [];

      final newOutput = _oldOutput.copyWith(
        data: data,
        otherData: pagedList.links,
        page: isInitialLoad
            ? initPage + (data.isNotEmpty ? 1 : 0)
            : _oldOutput.page + (data.isNotEmpty ? 1 : 0),
        offset: isInitialLoad
            ? (initOffset + data.length)
            : _oldOutput.offset + data.length,
        isLastPage: pagedList.links?.next?.isEmpty ?? false,
        isRefreshSuccess: isInitialLoad,
      );

      _output = newOutput;
      _oldOutput = newOutput;
      if (LogConfig.kEnableLogUseCaseOutput) {
        logD(
          'LoadMoreUseCase Output: pagedList: $pagedList, inputPage: $page, inputOffset: $offset, newOutput: $newOutput',
        );
      }

      return newOutput;
    } catch (e, s) {
      if (LogConfig.kEnableLogUseCaseError) {
        logE('LoadMoreUseCase Error: $e, stackTrace: $s');
      }
      _output = _oldOutput;

      throw e is GPAppExceptionV2
          ? e
          : e is AppException
              ? e
              : AppUncaughtException(e);
    }
  }
}
