/*
 * Created Date: 5/01/2024 11:14:40
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:23:33
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import '../../configs/log/log.configs.dart';
import '../model/base_input.dart';
import '../model/base_output.dart';
import '../model/base_use_case.dart';

abstract class GPBaseSyncUseCase<Input extends GPBaseInput,
    Output extends GPBaseOutput> extends BaseUseCase<Input, Output> {
  const GPBaseSyncUseCase();

  Output execute(Input input) {
    try {
      if (LogConfig.kEnableLogUseCaseInput) {
        logD('SyncUseCase Input: $input');
      }
      final output = buildUseCase(input);
      if (LogConfig.kEnableLogUseCaseOutput) {
        logD('SyncUseCase Output: $output');
      }

      return output;
    } catch (e) {
      if (LogConfig.kEnableLogUseCaseError) {
        logE('SyncUseCase Error: $e');
      }

      throw mapException(e);
    }
  }
}
