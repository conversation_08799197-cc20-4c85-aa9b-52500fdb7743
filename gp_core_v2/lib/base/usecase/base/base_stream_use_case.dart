/*
 * Created Date: 5/01/2024 11:13:22
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:23:30
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/helper/stream/stream_logger.dart';

import '../model/base_input.dart';
import '../model/base_use_case.dart';

abstract class GPBaseStreamUseCase<Input extends GPBaseInput, Output>
    extends BaseUseCase<Input, Stream<Output>> {
  const GPBaseStreamUseCase();

  Stream<Output> execute(Input input) {
    return buildUseCase(input).log(runtimeType.toString());
  }
}
