import 'package:gp_core/base/models/base_models.dart';
import 'package:gp_core_v2/base/usecase/model/base_output.dart';

import '../model.dart';

class ApiResponseV2<T> extends ApiResponse<T> implements GPBaseOutput {
  ApiResponseV2({required super.data, super.status});

  factory ApiResponseV2.fromJson(
    Map<String, dynamic> json,
    GenericCreator<T> genericCreator,
  ) {
    return ApiResponseV2<T>(
      status: json["status"],
      data: genericCreator(json['data']),
    );
  }
}
