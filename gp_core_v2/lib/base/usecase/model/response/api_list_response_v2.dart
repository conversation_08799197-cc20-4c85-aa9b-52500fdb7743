import 'package:gp_core/base/models/base_models.dart';

import '../model.dart';

class ListAPIResponseV2<T> extends ListAPIResponse<T> implements GPBaseOutput {
  ListAPIResponseV2({
    required super.data,
    super.code,
    super.message,
    super.links,
  });

  factory ListAPIResponseV2.fromJson(
    Map<String, dynamic> json,
    GenericCreator<T> genericCreator,
  ) {
    return ListAPIResponseV2(
      code: json["code"],
      message: json["message"],
      data: json["data"] != null
          ? List<T>.from(json["data"].map((x) => genericCreator(x)
              // GPParserJson.parseJson<T>(x)
              ))
          : null,
      links: json["links"] != null
          ? Links.fromJson(json["links"])
          : json["link"] != null
              ? Links.fromJson(json["link"])
              : Links(
                  next: "",
                  prev: "",
                  totalPages: 1,
                ),
    );
  }
}
