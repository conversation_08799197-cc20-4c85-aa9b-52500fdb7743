import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core_v2/base/usecase/model/base_output.dart';

part 'api_response_v2_without_data.g.dart';

@JsonSerializable(
  createToJson: false,
)
class ApiResponseV2WithoutData extends GPBaseOutput {
  ApiResponseV2WithoutData({this.message, this.status});

  factory ApiResponseV2WithoutData.fromJson(Map<String, dynamic> data) =>
      _$ApiResponseV2WithoutDataFromJson(data);

  final String? status;
  final String? message;
}
