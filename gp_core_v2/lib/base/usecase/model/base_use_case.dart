/*
 * Created Date: 4/01/2024 11:39:25
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:23:41
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';

import '../../exception/base/app_exception.dart';
import '../../exception/mapper/dio_exception_mapper.dart';
import '../../exception/uncaught/app_uncaught_exception.dart';
import '../../utils/mixin/log_mixin.dart';
import 'base_input.dart';

abstract class BaseUseCase<Input extends GPBaseInput, Output> with LogMixin {
  const BaseUseCase();

  Output buildUseCase(Input input);

  Exception mapException(Object? e) {
    if (e is DioException) {
      throw GetIt.I<DioExceptionMapper>(instanceName: 'kDioExceptionMapper')
          .map(e);
    }

    throw e is GPAppExceptionV2
        ? e
        : e is AppException
            ? e
            : AppUncaughtException(e);
  }
}
