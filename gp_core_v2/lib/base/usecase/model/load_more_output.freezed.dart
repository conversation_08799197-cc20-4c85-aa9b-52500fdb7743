// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'load_more_output.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$LoadMoreOutput<T> {
  List<T> get data;
  Object? get otherData;
  int get page;
  bool get isRefreshSuccess;
  int get offset;
  bool get isLastPage;

  /// Create a copy of LoadMoreOutput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadMoreOutputCopyWith<T, LoadMoreOutput<T>> get copyWith =>
      _$LoadMoreOutputCopyWithImpl<T, LoadMoreOutput<T>>(
          this as LoadMoreOutput<T>, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadMoreOutput<T> &&
            const DeepCollectionEquality().equals(other.data, data) &&
            const DeepCollectionEquality().equals(other.otherData, otherData) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.isRefreshSuccess, isRefreshSuccess) ||
                other.isRefreshSuccess == isRefreshSuccess) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.isLastPage, isLastPage) ||
                other.isLastPage == isLastPage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(data),
      const DeepCollectionEquality().hash(otherData),
      page,
      isRefreshSuccess,
      offset,
      isLastPage);

  @override
  String toString() {
    return 'LoadMoreOutput<$T>(data: $data, otherData: $otherData, page: $page, isRefreshSuccess: $isRefreshSuccess, offset: $offset, isLastPage: $isLastPage)';
  }
}

///
abstract mixin class $LoadMoreOutputCopyWith<T, $Res> {
  factory $LoadMoreOutputCopyWith(
          LoadMoreOutput<T> value, $Res Function(LoadMoreOutput<T>) _then) =
      _$LoadMoreOutputCopyWithImpl;
  @useResult
  $Res call(
      {List<T> data,
      Object? otherData,
      int page,
      bool isRefreshSuccess,
      int offset,
      bool isLastPage});
}

///
class _$LoadMoreOutputCopyWithImpl<T, $Res>
    implements $LoadMoreOutputCopyWith<T, $Res> {
  _$LoadMoreOutputCopyWithImpl(this._self, this._then);

  final LoadMoreOutput<T> _self;
  final $Res Function(LoadMoreOutput<T>) _then;

  /// Create a copy of LoadMoreOutput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? otherData = freezed,
    Object? page = null,
    Object? isRefreshSuccess = null,
    Object? offset = null,
    Object? isLastPage = null,
  }) {
    return _then(_self.copyWith(
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<T>,
      otherData: freezed == otherData ? _self.otherData : otherData,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      isRefreshSuccess: null == isRefreshSuccess
          ? _self.isRefreshSuccess
          : isRefreshSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      offset: null == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      isLastPage: null == isLastPage
          ? _self.isLastPage
          : isLastPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

///

class _LoadMoreOutput<T> extends LoadMoreOutput<T> {
  const _LoadMoreOutput(
      {required final List<T> data,
      this.otherData = null,
      this.page = PagingConstants.kInitialPage,
      this.isRefreshSuccess = false,
      this.offset = PagingConstants.kSkipItems,
      this.isLastPage = false})
      : _data = data,
        super._();

  final List<T> _data;
  @override
  List<T> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  @JsonKey()
  final Object? otherData;
  @override
  @JsonKey()
  final int page;
  @override
  @JsonKey()
  final bool isRefreshSuccess;
  @override
  @JsonKey()
  final int offset;
  @override
  @JsonKey()
  final bool isLastPage;

  /// Create a copy of LoadMoreOutput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadMoreOutputCopyWith<T, _LoadMoreOutput<T>> get copyWith =>
      __$LoadMoreOutputCopyWithImpl<T, _LoadMoreOutput<T>>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadMoreOutput<T> &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            const DeepCollectionEquality().equals(other.otherData, otherData) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.isRefreshSuccess, isRefreshSuccess) ||
                other.isRefreshSuccess == isRefreshSuccess) &&
            (identical(other.offset, offset) || other.offset == offset) &&
            (identical(other.isLastPage, isLastPage) ||
                other.isLastPage == isLastPage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_data),
      const DeepCollectionEquality().hash(otherData),
      page,
      isRefreshSuccess,
      offset,
      isLastPage);

  @override
  String toString() {
    return 'LoadMoreOutput<$T>(data: $data, otherData: $otherData, page: $page, isRefreshSuccess: $isRefreshSuccess, offset: $offset, isLastPage: $isLastPage)';
  }
}

///
abstract mixin class _$LoadMoreOutputCopyWith<T, $Res>
    implements $LoadMoreOutputCopyWith<T, $Res> {
  factory _$LoadMoreOutputCopyWith(
          _LoadMoreOutput<T> value, $Res Function(_LoadMoreOutput<T>) _then) =
      __$LoadMoreOutputCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<T> data,
      Object? otherData,
      int page,
      bool isRefreshSuccess,
      int offset,
      bool isLastPage});
}

///
class __$LoadMoreOutputCopyWithImpl<T, $Res>
    implements _$LoadMoreOutputCopyWith<T, $Res> {
  __$LoadMoreOutputCopyWithImpl(this._self, this._then);

  final _LoadMoreOutput<T> _self;
  final $Res Function(_LoadMoreOutput<T>) _then;

  /// Create a copy of LoadMoreOutput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = null,
    Object? otherData = freezed,
    Object? page = null,
    Object? isRefreshSuccess = null,
    Object? offset = null,
    Object? isLastPage = null,
  }) {
    return _then(_LoadMoreOutput<T>(
      data: null == data
          ? _self._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<T>,
      otherData: freezed == otherData ? _self.otherData : otherData,
      page: null == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int,
      isRefreshSuccess: null == isRefreshSuccess
          ? _self.isRefreshSuccess
          : isRefreshSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      offset: null == offset
          ? _self.offset
          : offset // ignore: cast_nullable_to_non_nullable
              as int,
      isLastPage: null == isLastPage
          ? _self.isLastPage
          : isLastPage // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
