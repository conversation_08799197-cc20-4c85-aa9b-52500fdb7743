/*
 * Created Date: 4/01/2024 11:48:58
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:23:56
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core_v2/base/constants/client/paging.constants.dart';

import 'model.dart';

part 'load_more_output.freezed.dart';

@freezed
abstract class LoadMoreOutput<T> extends GPBaseOutput with _$LoadMoreOutput<T> {
  const LoadMoreOutput._();

  const factory LoadMoreOutput({
    required List<T> data,
    @Default(null) Object? otherData,
    @Default(PagingConstants.kInitialPage) int page,
    @Default(false) bool isRefreshSuccess,
    @Default(PagingConstants.kSkipItems) int offset,
    @Default(false) bool isLastPage,
  }) = _LoadMoreOutput;

  int get nextPage => page + 1;
  int get previousPage => page - 1;
}
