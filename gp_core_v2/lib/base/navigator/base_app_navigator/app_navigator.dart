import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../mapper/base_route_info_mapper.dart';
import '../model/popup/app_popup_info.dart';
import '../model/route/app_route_info.dart';
import '../model/snackbar/app_snackbar_info.dart';
import 'app_popup_navigator.dart';
import 'app_snackbar_navigator.dart';

abstract class GPAppNavigator<I extends GPAppRouteInfo> {
  const GPAppNavigator(this.navigatorKey);

  GPBaseRouteInfoMapper get routeInfoMapper;

  GPAppPopupNavigator get appPopupNavigator;

  GPAppSnackbarNavigator get appSnackbarNavigator;

  final GlobalKey<NavigatorState> navigatorKey;

  bool canPop(BuildContext context);

  void pop<T extends Object?>(
    BuildContext context, {
    T? result,
    bool useRootNavigator = false,
  });

  void popUntil<T extends Object?>(BuildContext context, I appRouteInfo);

  Future<T?> popAndPush<T extends Object?, R extends Object?>(
    BuildContext context,
    I appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  });

  Future<T?> push<T extends Object?, R extends Object?>(
    BuildContext context,
    I appRouteInfo, {
    R? result,
    bool useRootNavigator = false,
    Object? arguments,
  });

  Future<T?> replace<T extends Object?>(
    BuildContext context,
    I appRouteInfo, {
    Object? arguments,
  });

  Object? parseArguments<T extends Object?>(
    I appRouteInfo,
    Object? arguments,
  );

  // popup
  Future<T?> showDialog<T>({
    required BuildContext context,
    required GPAppPopupInfo popupInfo,
    bool barrierDismissible = true,
    bool useSafeArea = true,
    bool useRootNavigator = true,
  }) {
    return appPopupNavigator.showDialog(
      context: context,
      popupInfo: popupInfo,
      barrierDismissible: barrierDismissible,
      useSafeArea: useSafeArea,
      useRootNavigator: useRootNavigator,
    );
  }

  Future<T?> showBaseBottomSheet<T>({
    required material.BuildContext context,
    required GPAppPopupInfo popupInfo,
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    bool? showDragHandle,
    bool useSafeArea = false,
  }) {
    return appPopupNavigator.showBaseBottomSheet(
      context: context,
      popupInfo: popupInfo,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      constraints: constraints,
    );
  }

  Future<T?> showModalBottomSheet<T>(
    BuildContext context,
    material.Widget widget, {
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    bool? showDragHandle,
    bool useSafeArea = false,
  }) {
    return appPopupNavigator.showModalBottomSheet(
      context,
      widget,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      constraints: constraints,
      isScrollControlled: isScrollControlled,
      useRootNavigator: useRootNavigator,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      showDragHandle: showDragHandle,
      useSafeArea: useSafeArea,
    );
  }

  // snackbar
  void showSnackBar({
    required BuildContext context,
    required GPAppSnackBarInfo snackbarInfo,
    bool closeCurrentSnackBar = true,
    material.AnimationStyle? snackBarAnimationStyle,
  }) {
    appSnackbarNavigator.showSnackBar(
      context: context,
      snackbarInfo: snackbarInfo,
      closeCurrentSnackBar: closeCurrentSnackBar,
      snackBarAnimationStyle: snackBarAnimationStyle,
    );
  }

  void closeNearestBottomSheet() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is ModalBottomSheetRoute) {
        NavigationHistoryObserver().navigator?.pop();
        return;
      }
    }
  }

  void closeAllBottomSheet() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is ModalBottomSheetRoute) {
        NavigationHistoryObserver().navigator?.pop();
      }
    }
  }

  void closeAllDialog() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is DialogRoute) {
        NavigationHistoryObserver().navigator?.pop();
      }
    }
  }
}
