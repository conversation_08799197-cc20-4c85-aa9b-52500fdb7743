import 'package:flutter/material.dart' as material;

import '../model/popup/app_popup_info.dart';

abstract class GPAppPopupNavigator {
  const GPAppPopupNavigator();

  Future<T?> showDialog<T>({
    required material.BuildContext context,
    required GPAppPopupInfo popupInfo,
    bool barrierDismissible = true,
    bool useSafeArea = true,
    bool useRootNavigator = true,
  });

  Future<T?> showBaseBottomSheet<T>({
    required material.BuildContext context,
    required GPAppPopupInfo popupInfo,
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    bool? showDragHandle,
    bool useSafeArea = false,
  });

  material.PersistentBottomSheetController showBottomSheet(
    material.BuildContext context,
    material.Widget widget, {
    bool enableDrag = true,
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
  });

  Future<T?> showModalBottomSheet<T>(
    material.BuildContext context,
    material.Widget widget, {
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    bool? showDragHandle,
    bool useSafeArea = false,
  });

  void closeNearestBottomSheet();

  void closeAllBottomSheet();

  void closeAllDialog();
}
