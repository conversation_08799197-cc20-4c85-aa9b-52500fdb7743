import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class GPPopup {
  double? globalTopPadding;
  GPPopup._();

  static final instance = GPPopup._();

  Future<void> closeSnackBar({bool withAnimations = true}) async {
    return Popup.instance.closeSnackBar(withAnimations: withAnimations);
  }

  // void showSnackBar({
  //   required String message,
  //   String? messageBold1,
  //   String? message2,
  //   String? messageBold2,
  //   String? message3,
  //   String? messageBold3,
  //   SnackbarType type = SnackbarType.normal,
  //   Duration duration = const Duration(seconds: 3),
  //   Duration animationDuration = const Duration(seconds: 1),
  //   SnackPosition? snackPosition = SnackPosition.TOP,
  //   Color? backgroundColor,
  //   String? undoTitle,
  //   VoidCallback? undoCallback,
  //   double? customPaddingTop,
  // }) {
  //   backgroundColor ??= GPColor.bgInversePrimary;

  //   Popup.instance.showSnackBar(
  //       message: message,
  //       messageBold1: messageBold1,
  //       message2: message2,
  //       messageBold2: messageBold2,
  //       message3: message3,
  //       messageBold3: messageBold3,
  //       type: type,
  //       duration: duration,
  //       animationDuration: animationDuration,
  //       snackPosition: snackPosition,
  //       backgroundColor: backgroundColor,
  //       undoTitle: undoTitle,
  //       undoCallback: undoCallback,
  //       customPaddingTop: customPaddingTop);
  // }

  Future<dynamic> showModalBottomSheet(
    BuildContext context,
    Widget widget, {
    bool isDismissible = true,
  }) {
    return material.showModalBottomSheet(
      context: context,
      useRootNavigator: false,
      builder: (context) {
        // wrap để auto height
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Wrap(
            children: <Widget>[
              widget.paddingOnly(bottom: Utils.getBottomSheetPadding())
            ],
          ),
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      backgroundColor: GPColor.bgPrimary,
      enableDrag: true,
      isScrollControlled: true,
      useSafeArea: true,
      isDismissible: isDismissible,
    );
  }

  material.PersistentBottomSheetController showBottomSheet(
    BuildContext context,
    Widget widget, {
    bool isDismissible = true,
  }) {
    // Get.bottomSheet(bottomsheet);

    return material.showBottomSheet(
      context: context,
      builder: (context) {
        // wrap để auto height
        return Wrap(
          children: <Widget>[
            widget.paddingOnly(bottom: Utils.getBottomSheetPadding())
          ],
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      backgroundColor: GPColor.bgPrimary,
      enableDrag: true,
    );
  }

  material.PersistentBottomSheetController showAlert(
    BuildContext context, {
    String? title,
    String? message,
    Widget? doneBtn,
    bool isDismissible = true,
  }) {
    return showBottomSheet(
      context,
      GPAlertView(title: title, message: message, doneBtn: doneBtn),
      isDismissible: isDismissible,
    );
  }

  void closeNearestBottomSheet() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is ModalBottomSheetRoute) {
        NavigationHistoryObserver().navigator?.pop();
        return;
      }
    }
  }

  void closeAllBottomSheet() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is ModalBottomSheetRoute) {
        NavigationHistoryObserver().navigator?.pop();
      }
    }
  }

  void closeAllDialog() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is DialogRoute) {
        NavigationHistoryObserver().navigator?.pop();
      }
    }
  }
}
