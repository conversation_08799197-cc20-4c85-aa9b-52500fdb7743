import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

import '../../base_app_navigator/app_navigator.dart';
import '../../model/popup/app_popup_info.dart';
import '../../model/route/app_route_info.dart';

class GPBasePopupWidget extends StatelessWidget {
  const GPBasePopupWidget({
    super.key,
    required this.popupInfo,
    required this.appNavigator,
  });

  final GPAppPopupInfo popupInfo;
  final GPAppNavigator<GPAppRouteInfo> appNavigator;

  @override
  Widget build(BuildContext context) {
    final assetIcon = popupInfo.iconSrc;
    final title = popupInfo.title;
    final description = popupInfo.content;
    final iconType = popupInfo.iconType;
    final hasIcon = assetIcon != null;
    final width = MediaQuery.of(context).size.width;

    return AlertDialog(
      title: Text(
        title,
        style: textStyle(GPTypography.headingLarge),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasIcon) ...{
            if (iconType == GPAppPopupIconType.icon)
              SvgWidget(assetIcon, width: 64, height: 64),
            if (iconType == GPAppPopupIconType.image) ...{
              if (assetIcon.startsWith('packages') == true)
                SvgWidget(assetIcon, width: width),
              if (assetIcon.startsWith('http') == true)
                Image.network(assetIcon, width: width),
            }
          },
          if (hasIcon) const SizedBox(height: 8),
          Text(description,
              style: textStyle(GPTypography.bodyLarge)
                  ?.mergeColor(GPColor.contentSecondary)),
        ],
      ).paddingAll(24),
      actions: [
        _ButtonWidget(
          popupInfo: popupInfo,
          appNavigator: appNavigator,
        ),
      ],
    );
  }
}

class _ButtonWidget extends StatelessWidget {
  const _ButtonWidget({
    required this.popupInfo,
    required this.appNavigator,
  });

  final GPAppPopupInfo popupInfo;
  final GPAppNavigator<GPAppRouteInfo> appNavigator;

  @override
  Widget build(BuildContext context) {
    final buttonLayout = popupInfo.buttonLayout;
    final firstBtn = popupInfo.firstBtn;
    final secondBtn = popupInfo.secondBtn;

    switch (buttonLayout) {
      case GPAppPopupButtonLayout.vertical:
        return Column(
          children: [
            if (firstBtn != null)
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => firstBtn.onPressed.call(context),
                  style: TextButton.styleFrom(
                      backgroundColor: firstBtn.backgroundColor,
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(8))),
                      padding: const EdgeInsets.all(12)),
                  child: Text(
                    firstBtn.btnName,
                    style: textStyle(GPTypography.headingMedium)
                        ?.mergeColor(firstBtn.textColor),
                  ),
                ),
              ),
            if (secondBtn != null) const SizedBox(height: 12),
            if (secondBtn != null)
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => secondBtn.onPressed.call(context),
                  style: TextButton.styleFrom(
                      backgroundColor: secondBtn.backgroundColor,
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(8))),
                      padding: const EdgeInsets.all(12)),
                  child: Text(
                    secondBtn.btnName,
                    style: textStyle(GPTypography.headingMedium)
                        ?.mergeColor(secondBtn.textColor),
                  ),
                ),
              ),
          ],
        );
      case GPAppPopupButtonLayout.horizontal:
        return Row(
          children: [
            if (firstBtn != null)
              Expanded(
                child: TextButton(
                  onPressed: () => firstBtn.onPressed.call(context),
                  style: TextButton.styleFrom(
                      backgroundColor: firstBtn.backgroundColor,
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(8))),
                      padding: const EdgeInsets.all(12)),
                  child: Text(
                    firstBtn.btnName,
                    style: textStyle(GPTypography.headingMedium)
                        ?.mergeColor(firstBtn.textColor),
                  ),
                ),
              ),
            if (secondBtn != null) const SizedBox(width: 12),
            if (secondBtn != null)
              Expanded(
                child: TextButton(
                  onPressed: () => secondBtn.onPressed.call(context),
                  style: TextButton.styleFrom(
                      backgroundColor: secondBtn.backgroundColor,
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(8))),
                      padding: const EdgeInsets.all(12)),
                  child: Text(
                    secondBtn.btnName,
                    style: textStyle(GPTypography.headingMedium)
                        ?.mergeColor(secondBtn.textColor),
                  ),
                ),
              ),
          ],
        );
    }
  }
}
