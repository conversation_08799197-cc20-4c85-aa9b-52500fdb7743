import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../../base_app_navigator/app_navigator.dart';
import '../../base_app_navigator/app_popup_navigator.dart';
import '../../mapper/base_popup_info_mapper.dart';
import '../../mapper_impl/base_popup_info_mapper_impl.dart';
import '../../model/popup/app_popup_info.dart';
import '../../model/route/app_route_info.dart';

const _popupInfoMapper = GPBasePopupInfoMapperImpl();

const _barrierColor = Colors.black45;

/// {@template package:gp_core_v2/base/navigator/app_navigator_impl/app_popup_navigator_impl.dart}
/// Depend chéo giữa `GPAppNavigator` và `GPAppPopupNavigator` <br>
/// `GPAppNavigator` giữ instance của `GPAppPopupNavigator` để navigate nếu cần
/// <br>
/// Đồng thời, `GPAppNavigator` cũng giữ instance của `GPAppPopupNavigator`
/// để tiện showPopup, tránh việc developer phải nắm nhiều class
/// {@endtemplate}
final class GPAppPopupNavigatorImpl extends GPAppPopupNavigator {
  const GPAppPopupNavigatorImpl(
    this.appNavigator,
  );

  final GPAppNavigator<GPAppRouteInfo> appNavigator;

  GPBasePopupInfoMapper get popupInfoMapper => _popupInfoMapper;

  @override
  Future<T?> showDialog<T>({
    required material.BuildContext context,
    required GPAppPopupInfo popupInfo,
    bool barrierDismissible = true,
    bool useSafeArea = true,
    bool useRootNavigator = true,
  }) {
    return material.showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      useSafeArea: useSafeArea,
      useRootNavigator: useRootNavigator,
      builder: (context) {
        return popupInfoMapper.mapPopup(popupInfo, appNavigator);
      },
    );
  }

  @override
  Future<T?> showBaseBottomSheet<T>({
    required material.BuildContext context,
    required GPAppPopupInfo popupInfo,
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    bool? showDragHandle,
    bool useSafeArea = false,
  }) {
    return material.showModalBottomSheet(
      context: context,
      builder: (context) {
        // wrap để auto height
        return material.Wrap(
          children: <material.Widget>[
            popupInfoMapper
                .mapBaseBottomSheetPopup(popupInfo, appNavigator)
                .paddingOnly(bottom: Utils.getBottomSheetPadding())
          ],
        );
      },
      shape: shape ??
          const material.RoundedRectangleBorder(
            borderRadius: material.BorderRadius.only(
              topLeft: material.Radius.circular(16),
              topRight: material.Radius.circular(16),
            ),
          ),
      backgroundColor: backgroundColor ?? GPColor.bgPrimary,
      enableDrag: enableDrag,
      isScrollControlled: isScrollControlled,
      useSafeArea: useSafeArea,
      isDismissible: isDismissible,
      showDragHandle: showDragHandle,
      elevation: elevation,
      clipBehavior: clipBehavior,
      constraints: constraints,
      barrierColor: _barrierColor,
    );
  }

  @override
  material.PersistentBottomSheetController showBottomSheet(
    material.BuildContext context,
    material.Widget widget, {
    bool enableDrag = true,
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
  }) {
    return material.showBottomSheet(
      context: context,
      builder: (context) {
        // wrap để auto height
        return material.Wrap(
          children: <material.Widget>[
            widget.paddingOnly(bottom: Utils.getBottomSheetPadding())
          ],
        );
      },
      shape: shape ??
          const material.RoundedRectangleBorder(
            borderRadius: material.BorderRadius.only(
              topLeft: material.Radius.circular(16),
              topRight: material.Radius.circular(16),
            ),
          ),
      backgroundColor: backgroundColor ?? GPColor.bgPrimary,
      enableDrag: enableDrag,
      clipBehavior: clipBehavior,
      constraints: constraints,
      elevation: elevation,
    );
  }

  @override
  Future<T?> showModalBottomSheet<T>(
    material.BuildContext context,
    material.Widget widget, {
    material.Color? backgroundColor,
    double? elevation,
    material.ShapeBorder? shape,
    material.Clip? clipBehavior,
    material.BoxConstraints? constraints,
    bool isScrollControlled = true,
    bool useRootNavigator = false,
    bool isDismissible = true,
    bool enableDrag = true,
    bool? showDragHandle,
    bool useSafeArea = true,
  }) {
    return material.showModalBottomSheet(
      context: context,
      useRootNavigator: false,
      builder: (context) {
        // wrap để auto height
        return material.Padding(
          padding: material.MediaQuery.of(context).viewInsets,
          child: material.Wrap(
            children: <material.Widget>[
              widget.paddingOnly(bottom: Utils.getBottomSheetPadding())
            ],
          ),
        );
      },
      shape: shape ??
          const material.RoundedRectangleBorder(
            borderRadius: material.BorderRadius.only(
              topLeft: material.Radius.circular(16),
              topRight: material.Radius.circular(16),
            ),
          ),
      backgroundColor: backgroundColor ?? GPColor.bgPrimary,
      enableDrag: enableDrag,
      isScrollControlled: isScrollControlled,
      useSafeArea: useSafeArea,
      isDismissible: isDismissible,
      showDragHandle: showDragHandle,
      elevation: elevation,
      clipBehavior: clipBehavior,
      constraints: constraints,
      barrierColor: _barrierColor,
    );
  }

  @override
  void closeNearestBottomSheet() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is ModalBottomSheetRoute) {
        NavigationHistoryObserver().navigator?.pop();
        return;
      }
    }
  }

  @override
  void closeAllBottomSheet() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is ModalBottomSheetRoute) {
        NavigationHistoryObserver().navigator?.pop();
      }
    }
  }

  @override
  void closeAllDialog() {
    final history = NavigationHistoryObserver().history;

    for (var p0 in history) {
      if (p0 is DialogRoute) {
        NavigationHistoryObserver().navigator?.pop();
      }
    }
  }
}
