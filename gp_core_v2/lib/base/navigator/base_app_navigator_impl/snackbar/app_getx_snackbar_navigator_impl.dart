import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart' as material;
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/navigator/model/snackbar/app_snackbar_info.dart';

import '../../base_app_navigator/app_navigator.dart';
import '../../base_app_navigator/app_snackbar_navigator.dart';
import '../../model/route/app_route_info.dart';

final class GPAppGetXSnackbarNavigatorImpl extends GPAppSnackbarNavigator {
  GPAppGetXSnackbarNavigatorImpl(
    this.appNavigator,
  );

  final GPAppNavigator<GPAppRouteInfo> appNavigator;

  @override
  void showSnackBar({
    required material.BuildContext context,
    required GPAppSnackBarInfo snackbarInfo,
    bool closeCurrentSnackBar = true,
    material.AnimationStyle? snackBarAnimationStyle,
  }) {
    final action = snackbarInfo.action;
    final double topPadding = snackbarInfo.customPaddingTop ?? 16;

    Widget snackBarWidget() {
      final contentWidget = snackbarInfo.contentWidget;
      final action = snackbarInfo.action;
      final actionWidget = action != null
          ? Row(
              children: [
                Container(
                  width: 1,
                  height: 20,
                  color: GPColor.functionAlwaysLightPrimary.withOpacity(0.3),
                  margin: const EdgeInsets.only(left: 8),
                ),
                CupertinoButton(
                  onPressed: action.onPressed
                      .call(appNavigator.navigatorKey.currentContext),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  minSize: 0,
                  child: Text(
                    action.actionName,
                    style: textStyle(GPTypography.headingSmall)?.copyWith(
                      color: GPColor.functionAccentWorkSecondary,
                      height: 1,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            )
          : null;
      final type = snackbarInfo.snackBarType;

      final Widget? iconWidget;

      switch (type) {
        case GPSnackBarType.normal:
          iconWidget = null;
          break;
        case GPSnackBarType.success:
          iconWidget = Container(
            width: 22,
            height: 22,
            margin: const EdgeInsets.only(right: 10),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: GPColor.functionAlwaysLightPrimary),
            child: SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_CHECKMARK_CIRCLE_1_SVG,
              width: 22,
              height: 22,
              fit: BoxFit.none,
              color: GPColor.functionPositivePrimary,
            ),
          );
          break;
        case GPSnackBarType.error:
          iconWidget = Container(
            width: 22,
            height: 22,
            margin: const EdgeInsets.only(right: 10),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: GPColor.functionAlwaysLightPrimary),
            child: SvgWidget(
              Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_XMARK_CIRCLE_SVG,
              width: 22,
              height: 22,
              fit: BoxFit.none,
              color: GPColor.functionNegativePrimary,
            ),
          );
          break;
      }

      return Row(
        children: [
          if (iconWidget != null) iconWidget,
          Expanded(child: contentWidget),
          if (actionWidget != null) actionWidget,
        ],
      );
    }

    Get.snackbar(
      '',
      '',
      titleText: const SizedBox(),
      padding: snackbarInfo.padding ??
          EdgeInsets.only(
              left: 13, right: action != null ? 0 : 12, top: 6, bottom: 12),
      margin: snackbarInfo.margin ??
          EdgeInsets.only(left: 16, bottom: 16, top: topPadding, right: 16),
      backgroundColor: snackbarInfo.backgroundColor ?? GPColor.bgInversePrimary,
      snackPosition: snackbarInfo.snackBarPosition.toGetXSnackPosition,
      borderRadius: 8,
      duration: snackbarInfo.duration,
      // animationDuration: animationDuration,
      messageText: snackBarWidget(),
    );
  }
}
