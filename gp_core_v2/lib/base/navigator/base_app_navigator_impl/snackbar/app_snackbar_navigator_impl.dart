import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/navigator/model/snackbar/app_snackbar_info.dart';

import '../../base_app_navigator/app_navigator.dart';
import '../../base_app_navigator/app_snackbar_navigator.dart';
import '../../model/route/app_route_info.dart';

final class GPAppSnackbarNavigatorImpl extends GPAppSnackbarNavigator {
  GPAppSnackbarNavigatorImpl(
    this.appNavigator,
  );

  final GPAppNavigator<GPAppRouteInfo> appNavigator;

  @override
  void showSnackBar({
    required material.BuildContext context,
    required GPAppSnackBarInfo snackbarInfo,
    bool closeCurrentSnackBar = true,
    material.AnimationStyle? snackBarAnimationStyle,
  }) {
    SnackBar snackBarWidget() {
      final double topPadding = snackbarInfo.customPaddingTop ?? 16;

      final contentWidget = snackbarInfo.contentWidget;
      final action = snackbarInfo.action;
      final actionWidget = action != null
          ? SnackBarAction(
              label: action.actionName,
              backgroundColor: action.backgroundColor,
              disabledBackgroundColor: action.disabledBackgroundColor,
              textColor: action.textColor,
              disabledTextColor: action.disabledTextColor,
              onPressed: () => action.onPressed
                  .call(appNavigator.navigatorKey.currentContext),
            )
          : null;
      final type = snackbarInfo.snackBarType;

      final Widget? iconWidget;

      switch (type) {
        case GPSnackBarType.normal:
          iconWidget = null;
          break;
        case GPSnackBarType.success:
          iconWidget = Container(
            width: 22,
            height: 22,
            margin: const EdgeInsets.only(right: 10),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: GPColor.functionAlwaysLightPrimary),
            child: SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_CHECKMARK_CIRCLE_1_SVG,
              width: 22,
              height: 22,
              fit: BoxFit.none,
              color: GPColor.functionPositivePrimary,
            ),
          );
          break;
        case GPSnackBarType.error:
          iconWidget = Container(
            width: 22,
            height: 22,
            margin: const EdgeInsets.only(right: 10),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: GPColor.functionAlwaysLightPrimary),
            child: SvgWidget(
              Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_FILL_XMARK_CIRCLE_SVG,
              width: 22,
              height: 22,
              fit: BoxFit.none,
              color: GPColor.functionNegativePrimary,
            ),
          );
          break;
      }

      return SnackBar(
        content: Align(
          alignment: Alignment.topCenter,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (iconWidget != null) iconWidget,
              Expanded(child: contentWidget),
            ],
          ),
        ),
        action: actionWidget,
        margin: snackbarInfo.margin ??
            EdgeInsets.only(
              left: 16,
              top: topPadding,
              right: 16,
              bottom: snackbarInfo.snackBarPosition == GPSnackBarPosition.bottom
                  ? 16
                  : (MediaQuery.of(context).size.height -
                      MediaQuery.of(context).padding.top -
                      100),
            ),
        padding: snackbarInfo.padding ??
            EdgeInsets.only(
                left: 13, right: action != null ? 0 : 12, top: 6, bottom: 12),
        closeIconColor: snackbarInfo.closeIconColor,
        showCloseIcon: snackbarInfo.showCloseIcon,
        backgroundColor: snackbarInfo.backgroundColor,
        shape: snackbarInfo.shape ??
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: snackbarInfo.duration,
        behavior: SnackBarBehavior.floating,
        dismissDirection: snackbarInfo.snackBarPosition.toDismissDirection,
      );
    }

    if (closeCurrentSnackBar) {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }

    ScaffoldMessenger.of(context).showSnackBar(
      snackBarWidget(),
      snackBarAnimationStyle: AnimationStyle(
        curve: Curves.bounceIn,
        duration: const Duration(milliseconds: 100),
      ),
    );
  }
}
