import 'package:flutter/material.dart';
import 'package:gp_core/navigator/deeplink.dart';

class GPNavigatorObserver extends NavigatorObserver {
  GPNavigatorObserver();

  void updateNativeSwipeBackGestureByRoute(Route route) {
    if (route.isFirst) {
      Deeplink.enableNativeSwipeBackGesture();
    } else {
      Deeplink.disableNativeSwipeBackGesture();
    }
  }

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);

    updateNativeSwipeBackGestureByRoute(route);
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);

    updateNativeSwipeBackGestureByRoute(route);
  }
}
