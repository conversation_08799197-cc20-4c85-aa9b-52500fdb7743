import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core/core.dart';

part 'app_popup_info.freezed.dart';

enum GPAppPopupIconType {
  /// hiển thị icon 64x64
  icon,

  /// hiển thị dạng image, full width, tỷ lệ 3:2 hoặc 16:9, tùy theo iconSrc
  image,
}

enum GPAppPopupButtonLayout {
  vertical,
  horizontal,
}

final class GPAppPopupInfoBtn {
  GPAppPopupInfoBtn({
    required this.btnName,
    required this.onPressed,
    required this.backgroundColor,
    required this.textColor,
  });

  factory GPAppPopupInfoBtn.defaultNegativeModel({
    required String displayName,
    required Function(BuildContext context) onClick,
  }) {
    return GPAppPopupInfoBtn(
      btnName: displayName,
      onPressed: onClick,
      backgroundColor: GPColor.bgSecondary,
      textColor: GPColor.contentPrimary,
    );
  }

  factory GPAppPopupInfoBtn.defaultPositiveModel({
    required String displayName,
    required Function(BuildContext context) onClick,
  }) {
    return GPAppPopupInfoBtn(
      btnName: displayName,
      onPressed: onClick,
      backgroundColor: GPColor.functionAccentWorkPrimary,
      textColor: Colors.white,
    );
  }

  final String btnName;
  final Function(BuildContext context) onPressed;
  final Color backgroundColor, textColor;
}

@freezed
abstract class GPAppPopupInfo with _$GPAppPopupInfo {
  /// 19/06/2025: ToanNM check với designer, hiện tại chỉ có các loại popup:
  /// 1. Popup với 1 button
  /// 2. Popup với 2 button
  /// https://www.figma.com/design/m8JFY9bKI3ijwcAm4aNcu3/T%E1%BA%B7ng-To%C3%A0n?node-id=0-1&p=f&t=sKUgpGTXWltA2wFC-0
  factory GPAppPopupInfo({
    required String title,
    required String content,

    ///
    String? iconSrc,
    @Default(GPAppPopupIconType.icon) GPAppPopupIconType iconType,

    ///
    @Default(GPAppPopupButtonLayout.horizontal)
    GPAppPopupButtonLayout buttonLayout,

    ///
    GPAppPopupInfoBtn? firstBtn,
    GPAppPopupInfoBtn? secondBtn,
  }) = _GPAppPopupInfo;
}
