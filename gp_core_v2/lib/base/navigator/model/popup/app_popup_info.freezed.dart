// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_popup_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$GPAppPopupInfo {
  String get title;
  String get content;

  ///
  String? get iconSrc;
  GPAppPopupIconType get iconType;

  ///
  GPAppPopupButtonLayout get buttonLayout;

  ///
  GPAppPopupInfoBtn? get firstBtn;
  GPAppPopupInfoBtn? get secondBtn;

  /// Create a copy of GPAppPopupInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GPAppPopupInfoCopyWith<GPAppPopupInfo> get copyWith =>
      _$GPAppPopupInfoCopyWithImpl<GPAppPopupInfo>(
          this as GPAppPopupInfo, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GPAppPopupInfo &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.iconSrc, iconSrc) || other.iconSrc == iconSrc) &&
            (identical(other.iconType, iconType) ||
                other.iconType == iconType) &&
            (identical(other.buttonLayout, buttonLayout) ||
                other.buttonLayout == buttonLayout) &&
            (identical(other.firstBtn, firstBtn) ||
                other.firstBtn == firstBtn) &&
            (identical(other.secondBtn, secondBtn) ||
                other.secondBtn == secondBtn));
  }

  @override
  int get hashCode => Object.hash(runtimeType, title, content, iconSrc,
      iconType, buttonLayout, firstBtn, secondBtn);

  @override
  String toString() {
    return 'GPAppPopupInfo(title: $title, content: $content, iconSrc: $iconSrc, iconType: $iconType, buttonLayout: $buttonLayout, firstBtn: $firstBtn, secondBtn: $secondBtn)';
  }
}

///
abstract mixin class $GPAppPopupInfoCopyWith<$Res> {
  factory $GPAppPopupInfoCopyWith(
          GPAppPopupInfo value, $Res Function(GPAppPopupInfo) _then) =
      _$GPAppPopupInfoCopyWithImpl;
  @useResult
  $Res call(
      {String title,
      String content,
      String? iconSrc,
      GPAppPopupIconType iconType,
      GPAppPopupButtonLayout buttonLayout,
      GPAppPopupInfoBtn? firstBtn,
      GPAppPopupInfoBtn? secondBtn});
}

///
class _$GPAppPopupInfoCopyWithImpl<$Res>
    implements $GPAppPopupInfoCopyWith<$Res> {
  _$GPAppPopupInfoCopyWithImpl(this._self, this._then);

  final GPAppPopupInfo _self;
  final $Res Function(GPAppPopupInfo) _then;

  /// Create a copy of GPAppPopupInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? content = null,
    Object? iconSrc = freezed,
    Object? iconType = null,
    Object? buttonLayout = null,
    Object? firstBtn = freezed,
    Object? secondBtn = freezed,
  }) {
    return _then(_self.copyWith(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      iconSrc: freezed == iconSrc
          ? _self.iconSrc
          : iconSrc // ignore: cast_nullable_to_non_nullable
              as String?,
      iconType: null == iconType
          ? _self.iconType
          : iconType // ignore: cast_nullable_to_non_nullable
              as GPAppPopupIconType,
      buttonLayout: null == buttonLayout
          ? _self.buttonLayout
          : buttonLayout // ignore: cast_nullable_to_non_nullable
              as GPAppPopupButtonLayout,
      firstBtn: freezed == firstBtn
          ? _self.firstBtn
          : firstBtn // ignore: cast_nullable_to_non_nullable
              as GPAppPopupInfoBtn?,
      secondBtn: freezed == secondBtn
          ? _self.secondBtn
          : secondBtn // ignore: cast_nullable_to_non_nullable
              as GPAppPopupInfoBtn?,
    ));
  }
}

///

class _GPAppPopupInfo implements GPAppPopupInfo {
  _GPAppPopupInfo(
      {required this.title,
      required this.content,
      this.iconSrc,
      this.iconType = GPAppPopupIconType.icon,
      this.buttonLayout = GPAppPopupButtonLayout.horizontal,
      this.firstBtn,
      this.secondBtn});

  @override
  final String title;
  @override
  final String content;

  ///
  @override
  final String? iconSrc;
  @override
  @JsonKey()
  final GPAppPopupIconType iconType;

  ///
  @override
  @JsonKey()
  final GPAppPopupButtonLayout buttonLayout;

  ///
  @override
  final GPAppPopupInfoBtn? firstBtn;
  @override
  final GPAppPopupInfoBtn? secondBtn;

  /// Create a copy of GPAppPopupInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GPAppPopupInfoCopyWith<_GPAppPopupInfo> get copyWith =>
      __$GPAppPopupInfoCopyWithImpl<_GPAppPopupInfo>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GPAppPopupInfo &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.iconSrc, iconSrc) || other.iconSrc == iconSrc) &&
            (identical(other.iconType, iconType) ||
                other.iconType == iconType) &&
            (identical(other.buttonLayout, buttonLayout) ||
                other.buttonLayout == buttonLayout) &&
            (identical(other.firstBtn, firstBtn) ||
                other.firstBtn == firstBtn) &&
            (identical(other.secondBtn, secondBtn) ||
                other.secondBtn == secondBtn));
  }

  @override
  int get hashCode => Object.hash(runtimeType, title, content, iconSrc,
      iconType, buttonLayout, firstBtn, secondBtn);

  @override
  String toString() {
    return 'GPAppPopupInfo(title: $title, content: $content, iconSrc: $iconSrc, iconType: $iconType, buttonLayout: $buttonLayout, firstBtn: $firstBtn, secondBtn: $secondBtn)';
  }
}

///
abstract mixin class _$GPAppPopupInfoCopyWith<$Res>
    implements $GPAppPopupInfoCopyWith<$Res> {
  factory _$GPAppPopupInfoCopyWith(
          _GPAppPopupInfo value, $Res Function(_GPAppPopupInfo) _then) =
      __$GPAppPopupInfoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String title,
      String content,
      String? iconSrc,
      GPAppPopupIconType iconType,
      GPAppPopupButtonLayout buttonLayout,
      GPAppPopupInfoBtn? firstBtn,
      GPAppPopupInfoBtn? secondBtn});
}

///
class __$GPAppPopupInfoCopyWithImpl<$Res>
    implements _$GPAppPopupInfoCopyWith<$Res> {
  __$GPAppPopupInfoCopyWithImpl(this._self, this._then);

  final _GPAppPopupInfo _self;
  final $Res Function(_GPAppPopupInfo) _then;

  /// Create a copy of GPAppPopupInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? content = null,
    Object? iconSrc = freezed,
    Object? iconType = null,
    Object? buttonLayout = null,
    Object? firstBtn = freezed,
    Object? secondBtn = freezed,
  }) {
    return _then(_GPAppPopupInfo(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      iconSrc: freezed == iconSrc
          ? _self.iconSrc
          : iconSrc // ignore: cast_nullable_to_non_nullable
              as String?,
      iconType: null == iconType
          ? _self.iconType
          : iconType // ignore: cast_nullable_to_non_nullable
              as GPAppPopupIconType,
      buttonLayout: null == buttonLayout
          ? _self.buttonLayout
          : buttonLayout // ignore: cast_nullable_to_non_nullable
              as GPAppPopupButtonLayout,
      firstBtn: freezed == firstBtn
          ? _self.firstBtn
          : firstBtn // ignore: cast_nullable_to_non_nullable
              as GPAppPopupInfoBtn?,
      secondBtn: freezed == secondBtn
          ? _self.secondBtn
          : secondBtn // ignore: cast_nullable_to_non_nullable
              as GPAppPopupInfoBtn?,
    ));
  }
}

// dart format on
