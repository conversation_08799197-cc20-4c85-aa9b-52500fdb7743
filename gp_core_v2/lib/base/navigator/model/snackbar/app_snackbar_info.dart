import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_core/core.dart';

part 'app_snackbar_info.freezed.dart';

enum GPSnackBarType {
  normal,
  success,
  error,
}

enum GPSnackBarPosition {
  top,
  bottom,
}

extension GPSnackBarPositionMapperExt on GPSnackBarPosition {
  DismissDirection get toDismissDirection {
    return switch (this) {
      GPSnackBarPosition.top => DismissDirection.up,
      GPSnackBarPosition.bottom => DismissDirection.down,
    };
  }

  SnackPosition get toGetXSnackPosition {
    return switch (this) {
      GPSnackBarPosition.top => SnackPosition.TOP,
      GPSnackBarPosition.bottom => SnackPosition.BOTTOM,
    };
  }
}

final class GPAppSnackBarAction {
  GPAppSnackBarAction({
    required this.actionName,
    required this.onPressed,

    ///
    this.textColor,
    this.disabledTextColor,

    ///
    this.backgroundColor,
    this.disabledBackgroundColor,
  });

  final String actionName;

  final Function(BuildContext? context) onPressed;

  final Color? textColor,
      disabledTextColor,
      backgroundColor,
      disabledBackgroundColor;
}

@freezed
abstract class GPAppSnackBarInfo with _$GPAppSnackBarInfo {
  factory GPAppSnackBarInfo({
    required Widget contentWidget,
    GPAppSnackBarAction? action,
    EdgeInsets? margin,
    EdgeInsets? padding,
    bool? showCloseIcon,
    Color? closeIconColor,
    ShapeBorder? shape,
    Color? backgroundColor,

    ///
    @Default(GPSnackBarType.normal) GPSnackBarType snackBarType,
    @Default(GPSnackBarPosition.top) GPSnackBarPosition snackBarPosition,

    ///
    @Default(Duration(seconds: 3)) Duration duration,
    double? customPaddingTop,
  }) = _GPAppSnackBarInfo;
}
