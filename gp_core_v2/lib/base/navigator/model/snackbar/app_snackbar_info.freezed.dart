// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_snackbar_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

///
mixin _$GPAppSnackBarInfo {
  Widget get contentWidget;
  GPAppSnackBarAction? get action;
  EdgeInsets? get margin;
  EdgeInsets? get padding;
  bool? get showCloseIcon;
  Color? get closeIconColor;
  ShapeBorder? get shape;
  Color? get backgroundColor;

  ///
  GPSnackBarType get snackBarType;
  GPSnackBarPosition get snackBarPosition;

  ///
  Duration get duration;
  double? get customPaddingTop;

  /// Create a copy of GPAppSnackBarInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GPAppSnackBarInfoCopyWith<GPAppSnackBarInfo> get copyWith =>
      _$GPAppSnackBarInfoCopyWithImpl<GPAppSnackBarInfo>(
          this as GPAppSnackBarInfo, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GPAppSnackBarInfo &&
            (identical(other.contentWidget, contentWidget) ||
                other.contentWidget == contentWidget) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.margin, margin) || other.margin == margin) &&
            (identical(other.padding, padding) || other.padding == padding) &&
            (identical(other.showCloseIcon, showCloseIcon) ||
                other.showCloseIcon == showCloseIcon) &&
            (identical(other.closeIconColor, closeIconColor) ||
                other.closeIconColor == closeIconColor) &&
            (identical(other.shape, shape) || other.shape == shape) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.snackBarType, snackBarType) ||
                other.snackBarType == snackBarType) &&
            (identical(other.snackBarPosition, snackBarPosition) ||
                other.snackBarPosition == snackBarPosition) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.customPaddingTop, customPaddingTop) ||
                other.customPaddingTop == customPaddingTop));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      contentWidget,
      action,
      margin,
      padding,
      showCloseIcon,
      closeIconColor,
      shape,
      backgroundColor,
      snackBarType,
      snackBarPosition,
      duration,
      customPaddingTop);

  @override
  String toString() {
    return 'GPAppSnackBarInfo(contentWidget: $contentWidget, action: $action, margin: $margin, padding: $padding, showCloseIcon: $showCloseIcon, closeIconColor: $closeIconColor, shape: $shape, backgroundColor: $backgroundColor, snackBarType: $snackBarType, snackBarPosition: $snackBarPosition, duration: $duration, customPaddingTop: $customPaddingTop)';
  }
}

///
abstract mixin class $GPAppSnackBarInfoCopyWith<$Res> {
  factory $GPAppSnackBarInfoCopyWith(
          GPAppSnackBarInfo value, $Res Function(GPAppSnackBarInfo) _then) =
      _$GPAppSnackBarInfoCopyWithImpl;
  @useResult
  $Res call(
      {Widget contentWidget,
      GPAppSnackBarAction? action,
      EdgeInsets? margin,
      EdgeInsets? padding,
      bool? showCloseIcon,
      Color? closeIconColor,
      ShapeBorder? shape,
      Color? backgroundColor,
      GPSnackBarType snackBarType,
      GPSnackBarPosition snackBarPosition,
      Duration duration,
      double? customPaddingTop});
}

///
class _$GPAppSnackBarInfoCopyWithImpl<$Res>
    implements $GPAppSnackBarInfoCopyWith<$Res> {
  _$GPAppSnackBarInfoCopyWithImpl(this._self, this._then);

  final GPAppSnackBarInfo _self;
  final $Res Function(GPAppSnackBarInfo) _then;

  /// Create a copy of GPAppSnackBarInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contentWidget = null,
    Object? action = freezed,
    Object? margin = freezed,
    Object? padding = freezed,
    Object? showCloseIcon = freezed,
    Object? closeIconColor = freezed,
    Object? shape = freezed,
    Object? backgroundColor = freezed,
    Object? snackBarType = null,
    Object? snackBarPosition = null,
    Object? duration = null,
    Object? customPaddingTop = freezed,
  }) {
    return _then(_self.copyWith(
      contentWidget: null == contentWidget
          ? _self.contentWidget
          : contentWidget // ignore: cast_nullable_to_non_nullable
              as Widget,
      action: freezed == action
          ? _self.action
          : action // ignore: cast_nullable_to_non_nullable
              as GPAppSnackBarAction?,
      margin: freezed == margin
          ? _self.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as EdgeInsets?,
      padding: freezed == padding
          ? _self.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets?,
      showCloseIcon: freezed == showCloseIcon
          ? _self.showCloseIcon
          : showCloseIcon // ignore: cast_nullable_to_non_nullable
              as bool?,
      closeIconColor: freezed == closeIconColor
          ? _self.closeIconColor
          : closeIconColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      shape: freezed == shape
          ? _self.shape
          : shape // ignore: cast_nullable_to_non_nullable
              as ShapeBorder?,
      backgroundColor: freezed == backgroundColor
          ? _self.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      snackBarType: null == snackBarType
          ? _self.snackBarType
          : snackBarType // ignore: cast_nullable_to_non_nullable
              as GPSnackBarType,
      snackBarPosition: null == snackBarPosition
          ? _self.snackBarPosition
          : snackBarPosition // ignore: cast_nullable_to_non_nullable
              as GPSnackBarPosition,
      duration: null == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      customPaddingTop: freezed == customPaddingTop
          ? _self.customPaddingTop
          : customPaddingTop // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

///

class _GPAppSnackBarInfo implements GPAppSnackBarInfo {
  _GPAppSnackBarInfo(
      {required this.contentWidget,
      this.action,
      this.margin,
      this.padding,
      this.showCloseIcon,
      this.closeIconColor,
      this.shape,
      this.backgroundColor,
      this.snackBarType = GPSnackBarType.normal,
      this.snackBarPosition = GPSnackBarPosition.top,
      this.duration = const Duration(seconds: 3),
      this.customPaddingTop});

  @override
  final Widget contentWidget;
  @override
  final GPAppSnackBarAction? action;
  @override
  final EdgeInsets? margin;
  @override
  final EdgeInsets? padding;
  @override
  final bool? showCloseIcon;
  @override
  final Color? closeIconColor;
  @override
  final ShapeBorder? shape;
  @override
  final Color? backgroundColor;

  ///
  @override
  @JsonKey()
  final GPSnackBarType snackBarType;
  @override
  @JsonKey()
  final GPSnackBarPosition snackBarPosition;

  ///
  @override
  @JsonKey()
  final Duration duration;
  @override
  final double? customPaddingTop;

  /// Create a copy of GPAppSnackBarInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GPAppSnackBarInfoCopyWith<_GPAppSnackBarInfo> get copyWith =>
      __$GPAppSnackBarInfoCopyWithImpl<_GPAppSnackBarInfo>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GPAppSnackBarInfo &&
            (identical(other.contentWidget, contentWidget) ||
                other.contentWidget == contentWidget) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.margin, margin) || other.margin == margin) &&
            (identical(other.padding, padding) || other.padding == padding) &&
            (identical(other.showCloseIcon, showCloseIcon) ||
                other.showCloseIcon == showCloseIcon) &&
            (identical(other.closeIconColor, closeIconColor) ||
                other.closeIconColor == closeIconColor) &&
            (identical(other.shape, shape) || other.shape == shape) &&
            (identical(other.backgroundColor, backgroundColor) ||
                other.backgroundColor == backgroundColor) &&
            (identical(other.snackBarType, snackBarType) ||
                other.snackBarType == snackBarType) &&
            (identical(other.snackBarPosition, snackBarPosition) ||
                other.snackBarPosition == snackBarPosition) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.customPaddingTop, customPaddingTop) ||
                other.customPaddingTop == customPaddingTop));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      contentWidget,
      action,
      margin,
      padding,
      showCloseIcon,
      closeIconColor,
      shape,
      backgroundColor,
      snackBarType,
      snackBarPosition,
      duration,
      customPaddingTop);

  @override
  String toString() {
    return 'GPAppSnackBarInfo(contentWidget: $contentWidget, action: $action, margin: $margin, padding: $padding, showCloseIcon: $showCloseIcon, closeIconColor: $closeIconColor, shape: $shape, backgroundColor: $backgroundColor, snackBarType: $snackBarType, snackBarPosition: $snackBarPosition, duration: $duration, customPaddingTop: $customPaddingTop)';
  }
}

///
abstract mixin class _$GPAppSnackBarInfoCopyWith<$Res>
    implements $GPAppSnackBarInfoCopyWith<$Res> {
  factory _$GPAppSnackBarInfoCopyWith(
          _GPAppSnackBarInfo value, $Res Function(_GPAppSnackBarInfo) _then) =
      __$GPAppSnackBarInfoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Widget contentWidget,
      GPAppSnackBarAction? action,
      EdgeInsets? margin,
      EdgeInsets? padding,
      bool? showCloseIcon,
      Color? closeIconColor,
      ShapeBorder? shape,
      Color? backgroundColor,
      GPSnackBarType snackBarType,
      GPSnackBarPosition snackBarPosition,
      Duration duration,
      double? customPaddingTop});
}

///
class __$GPAppSnackBarInfoCopyWithImpl<$Res>
    implements _$GPAppSnackBarInfoCopyWith<$Res> {
  __$GPAppSnackBarInfoCopyWithImpl(this._self, this._then);

  final _GPAppSnackBarInfo _self;
  final $Res Function(_GPAppSnackBarInfo) _then;

  /// Create a copy of GPAppSnackBarInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? contentWidget = null,
    Object? action = freezed,
    Object? margin = freezed,
    Object? padding = freezed,
    Object? showCloseIcon = freezed,
    Object? closeIconColor = freezed,
    Object? shape = freezed,
    Object? backgroundColor = freezed,
    Object? snackBarType = null,
    Object? snackBarPosition = null,
    Object? duration = null,
    Object? customPaddingTop = freezed,
  }) {
    return _then(_GPAppSnackBarInfo(
      contentWidget: null == contentWidget
          ? _self.contentWidget
          : contentWidget // ignore: cast_nullable_to_non_nullable
              as Widget,
      action: freezed == action
          ? _self.action
          : action // ignore: cast_nullable_to_non_nullable
              as GPAppSnackBarAction?,
      margin: freezed == margin
          ? _self.margin
          : margin // ignore: cast_nullable_to_non_nullable
              as EdgeInsets?,
      padding: freezed == padding
          ? _self.padding
          : padding // ignore: cast_nullable_to_non_nullable
              as EdgeInsets?,
      showCloseIcon: freezed == showCloseIcon
          ? _self.showCloseIcon
          : showCloseIcon // ignore: cast_nullable_to_non_nullable
              as bool?,
      closeIconColor: freezed == closeIconColor
          ? _self.closeIconColor
          : closeIconColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      shape: freezed == shape
          ? _self.shape
          : shape // ignore: cast_nullable_to_non_nullable
              as ShapeBorder?,
      backgroundColor: freezed == backgroundColor
          ? _self.backgroundColor
          : backgroundColor // ignore: cast_nullable_to_non_nullable
              as Color?,
      snackBarType: null == snackBarType
          ? _self.snackBarType
          : snackBarType // ignore: cast_nullable_to_non_nullable
              as GPSnackBarType,
      snackBarPosition: null == snackBarPosition
          ? _self.snackBarPosition
          : snackBarPosition // ignore: cast_nullable_to_non_nullable
              as GPSnackBarPosition,
      duration: null == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      customPaddingTop: freezed == customPaddingTop
          ? _self.customPaddingTop
          : customPaddingTop // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

// dart format on
