import 'package:flutter/material.dart';
import 'package:gp_core_v2/base/navigator/model/popup/app_popup_info.dart';
import 'package:gp_core_v2/base/navigator/model/route/app_route_info.dart';

import '../base_app_navigator/app_navigator.dart';
import '../mapper/base_popup_info_mapper.dart';
import '../popup/widget/base_bottomsheet_widget.dart';
import '../popup/widget/base_popup_widget.dart';

final class GPBasePopupInfoMapperImpl extends GPBasePopupInfoMapper {
  const GPBasePopupInfoMapperImpl();

  @override
  Widget mapPopup(
    GPAppPopupInfo popupInfo,
    GPAppNavigator<GPAppRouteInfo> appNavigator,
  ) {
    return GPBasePopupWidget(
      popupInfo: popupInfo,
      appNavigator: appNavigator,
    );
  }

  @override
  Widget mapBaseBottomSheetPopup(
    GPAppPopupInfo popupInfo,
    GPAppNavigator<GPAppRouteInfo> appNavigator,
  ) {
    return GPBaseBottomSheetWidget(
      popupInfo: popupInfo,
      appNavigator: appNavigator,
    );
  }
}
