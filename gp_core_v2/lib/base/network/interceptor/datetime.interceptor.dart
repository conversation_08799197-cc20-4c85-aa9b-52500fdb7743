/*
 * Created Date: 5/12/2023 11:04:39
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 15th January 2024 15:21:55
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core/core.dart';

final class DateTimeInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.queryParameters = options.queryParameters.map((key, value) {
      if (value is DateTime) {
        // maybe need to switch by feature laters
        return MapEntry(key, DateFormat("yyyy-MM-dd").format(value));
      } else {
        return MapEntry(key, value);
      }
    });
    handler.next(options);
  }
}
