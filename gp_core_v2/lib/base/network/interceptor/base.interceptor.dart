/*
 * Created Date: 5/12/2023 14:21:30
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 2nd January 2024 14:49:59
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:dio/dio.dart';

abstract class BaseInterceptor extends InterceptorsWrapper {
  static const basicAuthPriority = 40;
  static const connectivityPriority = 99;
  static const customLogPriority = 1;
  static const headerPriority = 19;
  static const accessTokenPriority = 20;
  static const refreshTokenPriority = 30;
  static const retryOnErrorPriority = 100;

  /// higher, add first
  /// lower, add last
  int get priority;
}
