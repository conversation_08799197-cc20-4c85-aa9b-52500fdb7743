import 'package:gp_core/core.dart';

/// remove empty key values from `headers`, `queryParameters`
class NullAbsentInterceptor extends Interceptor {
  const NullAbsentInterceptor();

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    //
    final queryParameters = options.queryParameters;
    queryParameters.removeWhere((k, v) {
      return v == null;
    });
    options.queryParameters = queryParameters;

    //
    final extra = options.extra;
    extra.removeWhere((k, v) {
      return v == null;
    });
    options.extra = extra;

    //
    final headers = options.headers;
    headers.removeWhere((k, v) {
      return v == null;
    });
    options.headers = headers;

    handler.next(options);
  }
}
