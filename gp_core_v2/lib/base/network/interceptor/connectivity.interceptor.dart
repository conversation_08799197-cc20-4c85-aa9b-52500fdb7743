/*
 * Created Date: 2/01/2024 14:49:8
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 15th January 2024 15:22:24
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/exception/remote/remote_exception.dart';
import 'package:injectable/injectable.dart';

import 'base.interceptor.dart';

@singleton
class ConnectivityInterceptor extends BaseInterceptor {
  @override
  int get priority => BaseInterceptor.connectivityPriority;

  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      return handler.reject(
        DioException(
          requestOptions: options,
          error: const RemoteException(kind: RemoteExceptionKind.noInternet),
        ),
      );
    }

    return super.onRequest(options, handler);
  }
}
