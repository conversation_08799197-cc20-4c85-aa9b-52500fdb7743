/*
 * Created Date: 4/11/2023 16:01:36
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:01:14
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/bloc/base_event.dart';
import 'package:injectable/injectable.dart';

@singleton
final class AppUseCaseManagement {
  /// ignore BlocEvent(s)
  final List<Type> ignoreEvents = [];

  bool blocEventContainsUsecase<Event extends CoreV2BaseEvent>({Event? event}) {
    if (event == null) {
      return ignoreEvents.any((element) {
        return Event == element;
      });
    }

    return ignoreEvents.any((element) {
      return event == element || event.runtimeType == element;
    });
  }
}
