/*
 * Created Date: 4/11/2023 16:48:4
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 15:08:20
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer' as developer;

import 'package:talker/talker.dart';

class GPTalkerObserver extends TalkerObserver {
  GPTalkerObserver();

  @override
  void onError(TalkerError err) {
    developer.log("GPTalkerObserver onError: $err");
    super.onError(err);
  }

  @override
  void onException(TalkerException err) {
    developer.log("GPTalkerObserver onException: ${err.error}");
    super.onException(err);
  }

  @override
  void onLog(TalkerData log) {
    developer.log("GPTalkerObserver:\n___${log.message ?? log.displayMessage}");
    super.onLog(log);
  }
}
