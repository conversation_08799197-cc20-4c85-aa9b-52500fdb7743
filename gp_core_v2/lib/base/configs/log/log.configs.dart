/*
 * Created Date: 5/12/2023 11:04:39
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 5th January 2024 11:09:08
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:flutter/foundation.dart';

sealed class LogConfig {
  /// for all debugLog
  static const bool kEnableDebugLog = kDebugMode;
  static const bool kEnableTalkerLog = false;

  static const bool kCurlOnRequest = true;
  static const bool kCurlOnResponse = true;
  static const bool kCurlOnError = true;

  /// navigation
  static const kLogOnNavigatorPushError = false;

  /// disposeBag
  static const kEnableDisposeBagLog = false;

  /// enable log usecase
  static const kEnableLogUseCaseInput = false;
  static const kEnableLogUseCaseOutput = false;
  static const kEnableLogUseCaseError = kDebugMode;

  /// stream event log
  static const kLogOnStreamListen = false;
  static const kLogOnStreamData = false;
  static const kLogOnStreamError = false;
  static const kLogOnStreamDone = false;
  static const kLogOnStreamCancel = false;
}
